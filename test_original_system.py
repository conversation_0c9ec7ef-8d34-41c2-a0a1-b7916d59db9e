#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试原始系统的饼图修复
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append('.')

def test_original_system():
    """测试原始系统的饼图功能"""
    
    try:
        # 导入修复后的notebook代码
        print("📚 正在加载修复后的智能体系统...")
        
        # 执行notebook中的代码
        with open('智能体开发.ipynb', 'r', encoding='utf-8') as f:
            notebook_content = f.read()
        
        # 提取Python代码部分
        import json
        notebook_data = json.loads(notebook_content)
        
        code_cells = []
        for cell in notebook_data['cells']:
            if cell['cell_type'] == 'code':
                code_cells.extend(cell['source'])
        
        # 执行代码
        full_code = ''.join(code_cells)
        
        # 创建一个安全的执行环境
        exec_globals = {
            '__name__': '__main__',
            '__file__': 'test_original_system.py'
        }
        
        print("⚙️ 正在执行智能体代码...")
        exec(full_code, exec_globals)
        
        # 获取测试器实例
        if 'viz_tester' in exec_globals:
            viz_tester = exec_globals['viz_tester']
            print("✅ 智能体系统加载成功!")
            
            # 初始化引擎
            print("🔧 正在初始化可视化引擎...")
            viz_tester.initialize_engines()
            
            # 测试饼图生成
            print("\n🧪 开始测试饼图生成...")
            test_question = "各品牌的市场份额占比如何？"
            
            result = viz_tester.chat_with_ai(test_question)
            
            if result.get('success'):
                print("🎉 饼图生成测试成功!")
                print(f"📊 图表类型: {result.get('chart_type', '未知')}")
                print(f"📁 图表路径: {result.get('chart_path', '未知')}")
                
                # 检查文件是否存在且不为空
                chart_path = result.get('chart_path')
                if chart_path and os.path.exists(chart_path):
                    file_size = os.path.getsize(chart_path)
                    print(f"📏 文件大小: {file_size} bytes")
                    
                    if file_size > 1000:  # 至少1KB，说明不是空白图
                        print("✅ 图表文件生成正常，不是空白图!")
                        return True
                    else:
                        print("⚠️ 图表文件太小，可能是空白图!")
                        return False
                else:
                    print("❌ 图表文件不存在!")
                    return False
            else:
                print(f"❌ 饼图生成失败: {result.get('message', '未知错误')}")
                return False
                
        else:
            print("❌ 未找到viz_tester实例!")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_before_after():
    """比较修复前后的效果"""

    print("\n📊 修复效果对比:")
    print("=" * 50)

    old_size = 0
    new_size = 0

    # 检查之前的空白图
    old_chart = "powerbank_memory/chart_1753346797.png"
    if os.path.exists(old_chart):
        old_size = os.path.getsize(old_chart)
        print(f"🔍 修复前图表: {old_chart}")
        print(f"📏 文件大小: {old_size} bytes")
    else:
        print("🔍 修复前图表: 未找到")

    # 检查新的测试图
    new_chart = "powerbank_memory/test_pie_chart_fixed.png"
    if os.path.exists(new_chart):
        new_size = os.path.getsize(new_chart)
        print(f"✅ 修复后图表: {new_chart}")
        print(f"📏 文件大小: {new_size} bytes")

        if new_size > 100000:
            print("🎉 修复成功! 图表生成正常，内容丰富!")
        else:
            print("⚠️ 需要进一步检查修复效果")
    else:
        print("❌ 修复后图表: 未找到")

    print("=" * 50)

if __name__ == "__main__":
    print("🚀 开始测试原始系统的饼图修复...")
    
    # 先比较修复前后
    compare_before_after()
    
    # 测试原始系统
    success = test_original_system()
    
    if success:
        print("\n🎉 原始系统饼图修复测试成功!")
        print("✅ 问题已解决: 饼图不再是空白图!")
    else:
        print("\n❌ 原始系统饼图修复测试失败!")
        print("⚠️ 需要进一步调试!")
