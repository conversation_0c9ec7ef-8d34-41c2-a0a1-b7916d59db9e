{"cells": [{"cell_type": "code", "execution_count": 7, "id": "7bb38cf5-339a-45c7-8158-11d46d805210", "metadata": {}, "outputs": [], "source": ["import os\n", "import io\n", "import json\n", "import httpx\n", "import inspect\n", "import numpy as np\n", "import pandas as pd\n", "import pymysql\n", "import openai\n", "import inspect\n", "import tiktoken\n", "from openai import OpenAI\n", "from dashscope import get_tokenizer # type: ignore"]}, {"cell_type": "code", "execution_count": 6, "id": "cd66fb32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting dashscope\n", "  Downloading dashscope-1.23.8-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting aiohttp (from dashscope)\n", "  Downloading aiohttp-3.12.14-cp312-cp312-win_amd64.whl.metadata (7.9 kB)\n", "Requirement already satisfied: requests in c:\\anaconda\\envs\\zjou-2025\\lib\\site-packages (from dashscope) (2.32.4)\n", "Collecting websocket-client (from dashscope)\n", "  Downloading websocket_client-1.8.0-py3-none-any.whl.metadata (8.0 kB)\n", "Requirement already satisfied: cryptography in c:\\anaconda\\envs\\zjou-2025\\lib\\site-packages (from dashscope) (45.0.5)\n", "Collecting aiohappyeyeballs>=2.5.0 (from aiohttp->dashscope)\n", "  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)\n", "Collecting aiosignal>=1.4.0 (from aiohttp->dashscope)\n", "  Downloading aiosignal-1.4.0-py3-none-any.whl.metadata (3.7 kB)\n", "Collecting attrs>=17.3.0 (from aiohttp->dashscope)\n", "  Downloading attrs-25.3.0-py3-none-any.whl.metadata (10 kB)\n", "Collecting frozenlist>=1.1.1 (from aiohttp->dashscope)\n", "  Downloading frozenlist-1.7.0-cp312-cp312-win_amd64.whl.metadata (19 kB)\n", "Collecting multidict<7.0,>=4.5 (from aiohttp->dashscope)\n", "  Downloading multidict-6.6.3-cp312-cp312-win_amd64.whl.metadata (5.4 kB)\n", "Collecting propcache>=0.2.0 (from aiohttp->dashscope)\n", "  Downloading propcache-0.3.2-cp312-cp312-win_amd64.whl.metadata (12 kB)\n", "Collecting yarl<2.0,>=1.17.0 (from aiohttp->dashscope)\n", "  Downloading yarl-1.20.1-cp312-cp312-win_amd64.whl.metadata (76 kB)\n", "Requirement already satisfied: idna>=2.0 in c:\\anaconda\\envs\\zjou-2025\\lib\\site-packages (from yarl<2.0,>=1.17.0->aiohttp->dashscope) (3.10)\n", "Requirement already satisfied: typing-extensions>=4.2 in c:\\anaconda\\envs\\zjou-2025\\lib\\site-packages (from aiosignal>=1.4.0->aiohttp->dashscope) (4.14.1)\n", "Requirement already satisfied: cffi>=1.14 in c:\\anaconda\\envs\\zjou-2025\\lib\\site-packages (from cryptography->dashscope) (1.17.1)\n", "Requirement already satisfied: pycparser in c:\\anaconda\\envs\\zjou-2025\\lib\\site-packages (from cffi>=1.14->cryptography->dashscope) (2.22)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in c:\\anaconda\\envs\\zjou-2025\\lib\\site-packages (from requests->dashscope) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\anaconda\\envs\\zjou-2025\\lib\\site-packages (from requests->dashscope) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\anaconda\\envs\\zjou-2025\\lib\\site-packages (from requests->dashscope) (2025.7.14)\n", "Downloading dashscope-1.23.8-py3-none-any.whl (1.3 MB)\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ---------------- ----------------------- 0.5/1.3 MB 61.2 kB/s eta 0:00:13\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   ------------------------ --------------- 0.8/1.3 MB 54.3 kB/s eta 0:00:10\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   -------------------------------- ------- 1.0/1.3 MB 57.5 kB/s eta 0:00:05\n", "   ---------------------------------------- 1.3/1.3 MB 61.2 kB/s eta 0:00:00\n", "Downloading aiohttp-3.12.14-cp312-cp312-win_amd64.whl (449 kB)\n", "Downloading multidict-6.6.3-cp312-cp312-win_amd64.whl (45 kB)\n", "Downloading yarl-1.20.1-cp312-cp312-win_amd64.whl (86 kB)\n", "Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)\n", "Downloading aiosignal-1.4.0-py3-none-any.whl (7.5 kB)\n", "Downloading attrs-25.3.0-py3-none-any.whl (63 kB)\n", "Downloading frozenlist-1.7.0-cp312-cp312-win_amd64.whl (43 kB)\n", "Downloading propcache-0.3.2-cp312-cp312-win_amd64.whl (41 kB)\n", "Downloading websocket_client-1.8.0-py3-none-any.whl (58 kB)\n", "Installing collected packages: websocket-client, propcache, multidict, frozenlist, attrs, aiohappyeyeballs, yarl, aiosignal, aiohttp, dashscope\n", "\n", "   ------------ ---------------------------  3/10 [frozenlist]\n", "   ------------------------ ---------------  6/10 [yarl]\n", "   -------------------------------- -------  8/10 [aiohttp]\n", "   -------------------------------- -------  8/10 [aiohttp]\n", "   ------------------------------------ ---  9/10 [dashscope]\n", "   ------------------------------------ ---  9/10 [dashscope]\n", "   ------------------------------------ ---  9/10 [dashscope]\n", "   ---------------------------------------- 10/10 [dashscope]\n", "\n", "Successfully installed aiohappyeyeballs-2.6.1 aiohttp-3.12.14 aiosignal-1.4.0 attrs-25.3.0 dashscope-1.23.8 frozenlist-1.7.0 multidict-6.6.3 propcache-0.3.2 websocket-client-1.8.0 yarl-1.20.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["# 如果还没有安装dashscope，先安装它\n", "%pip install dashscope\n", "from dashscope import get_tokenizer"]}, {"cell_type": "code", "execution_count": 8, "id": "ffcc3c3b-5871-4b43-a375-72b7cc5ec17f", "metadata": {}, "outputs": [], "source": ["api_key = \"sk-e2bad2bca73343a38f4f8d60f7435192\"\n", "base_url = \"https://api.deepseek.com/v1\""]}, {"cell_type": "code", "execution_count": 9, "id": "e56d949c-3496-4485-b95f-305fa895e704", "metadata": {}, "outputs": [], "source": ["def auto_call(client,messages,tools=None,model='deepseek-chat'):\n", "    if tools == None :\n", "        response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages\n", "        )\n", "        return response.choices[0].message.content\n", "    else :\n", "        #函数第一次调用\n", "        first_response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages,\n", "            tools = tools,\n", "            tool_choice=\"auto\",\n", "        )\n", "        message_call = first_response.choices[0].message\n", "        tools_calls = message_call.tool_calls\n", "        if tools_calls != None:\n", "            tool_call_id = tools_calls[0].id\n", "            func_name = first_response.choices[0].message.tool_calls[0].function.name\n", "            func_args = json.loads(tools_calls[0].function.arguments)['sql']\n", "            func_result = tools_list[func_name](func_args)\n", "            messages.append(message_call)\n", "            messages.append(\n", "                {\n", "                    \"role\":\"tool\",\n", "                    \"tool_call_id\":tool_call_id,\n", "                    \"name\":func_name,\n", "                    \"content\":func_result\n", "                }\n", "            )\n", "            #二次调用\n", "            second_response = client.chat.completions.create(\n", "                model=model,\n", "                messages=messages\n", "            )\n", "            return second_response.choices[0].message.content\n", "        else:\n", "            return first_response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 5, "id": "be327175-4a0a-406a-8e39-6ba405078db8", "metadata": {}, "outputs": [], "source": ["def get_user_info(sql,username='root',password='123456',db='user_info'):\n", "    \"\"\"\n", "    当前函数，用于查询指定数据库下的相关表中的数据\n", "    ：param sql：参数为必要参数,字符串类型的SQL语句,\n", "    ：param username：参数为可选参数,代表:用户名,字符串类型,\n", "    ：param password：参数为可选参数,代表:用户密码,字符串类型,\n", "    ：param db：参数为可选参数,代表:需要连接的数据库名字,字符串类型,\n", "    ：return:当前SQL语句执行完的查询结果\n", "    \"\"\"\n", "    conn = pymysql.connect(\n", "        host = 'localhost',\n", "        user = username,\n", "        password = password,\n", "        db = db,\n", "        charset = 'utf8'\n", "    )\n", "    try: \n", "        with conn.cursor() as cursor:\n", "            cursor.execute(sql)\n", "            results = cursor.fetchall()\n", "    finally:\n", "        cursor.close()\n", "        conn.close()\n", "    column_names = [desc[0] for desc in cursor.description]\n", "    df = pd.DataFrame(results, columns = column_names)\n", "    return df.to_json(orient = 'records')"]}, {"cell_type": "code", "execution_count": 6, "id": "95101242-23ab-4288-a573-9c31b6760602", "metadata": {}, "outputs": [], "source": ["http_client = httpx.Client(follow_redirects=True)\n", "\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")\n", "\n", "tools_list = {\"get_user_info\": get_user_info}\n", "\n", "tools = [{'type': 'function',\n", "         'function': {\n", "            'name': 'get_user_info',\n", "            'description': '当前函数，用于查询指定数据库下的相关表中的数据。输入参数为字符串类型的SQL语句，输出为当前SQL语句执行完的查询结果',\n", "            'parameters': {'type': 'object',\n", "                           'properties': {'sql': {'description': '必要参数，字符串类型的SQL语句','type': 'string'},\n", "                                          'username': {'description': '用户名,字符串类型','type': 'string'},\n", "                                          'password': {'description': '用户密码,字符串类型','type': 'string'},\n", "                                          'db': {'description': '需要连接的数据库名字,字符串类型','type': 'string'}\n", "                                         },\n", "         'required': ['sql']},\n", "        }}]"]}, {"cell_type": "code", "execution_count": 7, "id": "17f49c70-005a-47c6-8e7b-5343d3b4a2ce", "metadata": {}, "outputs": [], "source": ["with open(r'C:\\\\Users\\\\<USER>\\\\Desktop\\\\data.md', 'r', encoding='utf-8') as f:\n", "    system_info = f.read()\n", "# system_info "]}, {"cell_type": "code", "execution_count": 8, "id": "96474f61-f3e6-4e54-8c0d-fea3139a575f", "metadata": {}, "outputs": [], "source": ["class ShortMemoryBaseCount:\n", "    def __init__(self, \n", "                 messages=None,\n", "                 count_threshold=0,\n", "                 counts=0,\n", "                 model='deepseek-chat'):\n", "        if messages is None:\n", "            self.messages = []\n", "        else:\n", "            self.messages = messages\n", "        self.count_threshold = count_threshold\n", "        self.counts = counts\n", "        self.model = model\n", "    def _based_on_count(self):\n", "        if self.counts > self.count_threshold:\n", "            if type(self.messages[1]) is not dict and \"tool_call_id\" in self.messages[2]:\n", "                del self.messages[1]\n", "                del self.messages[1]\n", "                diff = self.counts - self.count_threshold\n", "                self.counts -= diff\n", "                del self.messages[1:(1 + diff)]\n", "            else:\n", "                diff = self.counts - self.count_threshold\n", "                self.counts -= diff\n", "                del self.messages[1:(1 + diff)]\n", "    def append_message(self, message:dict):\n", "        self.counts += 1\n", "        self.messages.append(message)\n", "        self._based_on_count()\n", "    def test_append_message(self, message:dict):\n", "        self.messages.append(message)\n", "        self._based_on_count()\n", "    def get_messages(self):\n", "        return self.messages"]}, {"cell_type": "markdown", "id": "ad17a733", "metadata": {}, "source": ["# 1.创建长期记忆"]}, {"cell_type": "code", "execution_count": 12, "id": "86d16ff2-61c6-476f-a58d-2ca6050b7504", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "from typing import List, Dict, Optional\n", "import tiktoken\n", "\n", "class LongTermMemory:\n", "    def __init__(self):\n", "        self.encoder = get_tokenizer('qwen-turbo')\n", "    \n", "    def create_folder(self, folder_path: str) -> None:\n", "        \"\"\"创建文件夹（自动处理路径存在情况）\"\"\"\n", "        try:\n", "            os.makedirs(folder_path, exist_ok=True)\n", "        except PermissionError as e:\n", "            print(f\"Permission denied creating folder: {e}\")\n", "        except OSError as e:\n", "            print(f\"OS error creating folder: {e}\")\n", "    \n", "    def create_file(self, file_path: str) -> bool:\n", "        \"\"\"创建空文件（返回是否成功）\"\"\"\n", "        try:\n", "            folder = os.path.dirname(file_path)\n", "            if folder:\n", "                self.create_folder(folder)\n", "            open(file_path, 'a', encoding='utf-8').close()\n", "            return True\n", "        except IOError as e:\n", "            print(f\"Error creating file: {e}\")\n", "            return False\n", "    \n", "    def append_qa(self, file_path: str, question: str, answer: str) -> bool:\n", "        \"\"\"安全追加QA对（自动创建缺失路径）\"\"\"\n", "        try:\n", "            qa = {\"Q\": question, \"A\": answer}\n", "            dir_path = os.path.dirname(file_path)\n", "            if dir_path:\n", "                self.create_folder(dir_path)\n", "            with open(file_path, 'a+', encoding='utf-8') as f:\n", "                f.write(json.dumps(qa) + \"\\n\")\n", "            return True\n", "        except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, json.JSONDecodeError) as e:\n", "            print(f\"Error appending QA: {e}\")\n", "            return False\n", "    \n", "    def read_all_qa(self, file_path: str) -> List[Dict]:\n", "        \"\"\"安全读取全部QA（处理文件不存在情况）\"\"\"\n", "        try:\n", "            if not os.path.exists(file_path):\n", "                return []\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                return [json.loads(line) for line in f if line.strip()]\n", "        except (FileNot<PERSON><PERSON>nd<PERSON><PERSON>r, json.JSONDecodeError) as e:\n", "            print(f\"Error reading QA: {e}\")\n", "            return []\n", "    \n", "    def read_recent_qa(self, file_path: str, k: int) -> List[Dict]:\n", "        \"\"\"安全读取最近k个QA（自动处理无效k值）\"\"\"\n", "        qa_list = self.read_all_qa(file_path)\n", "        try:\n", "            return qa_list[-abs(k):] if qa_list else []\n", "        except TypeError:\n", "            return []\n", "    \n", "    def get_qa_count(self, file_path: str) -> int:\n", "        \"\"\"安全获取QA数量\"\"\"\n", "        return len(self.read_all_qa(file_path))\n", "    \n", "    def get_token_count(self, file_path: str) -> int:\n", "        \"\"\"安全获取Token数量（处理读取错误）\"\"\"\n", "        try:\n", "            if not os.path.exists(file_path):\n", "                return 0\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                return len(self.encoder.encode(f.read()))\n", "        except (<PERSON><PERSON><PERSON><PERSON>, PermissionError) as e:\n", "            print(f\"Error counting tokens: {e}\")\n", "            return 0"]}, {"cell_type": "markdown", "id": "0fb4235e", "metadata": {}, "source": ["# 2.长期记忆测试"]}, {"cell_type": "markdown", "id": "a69af92a", "metadata": {}, "source": ["```\n", "在LongTermMemory类中定义名字为create_folder的方法。\n", "功能：实现创建文件夹的功能，自动处理路径存在的情况\n", "参数：folder_path：str，要创建的文件夹路径\n", "```"]}, {"cell_type": "code", "execution_count": 13, "id": "713194f9", "metadata": {}, "outputs": [], "source": ["ltm = LongTermMemory()\n", "ltm.create_folder(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder')\n", "ltm.create_folder(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder') "]}, {"cell_type": "markdown", "id": "bc7e2015", "metadata": {}, "source": ["```\n", "开发逻辑：\n", "    在LongTermMemory类中定义名字为create_file的方法。\n", "    功能：实现创建空文件的功能，并自动创建缺失的路径\n", "    参数：file_path：str，要创建的文件路径\n", "```"]}, {"cell_type": "markdown", "id": "a3ab5603", "metadata": {}, "source": ["```\n", "测试逻辑：\n", "    1）首先测试带有级联目录，并且存在目录不存在的情况，查看文件是否会被成功创建；\n", "    2）再测试文件重复被创建系统是否会容错。\n", "```"]}, {"cell_type": "code", "execution_count": 14, "id": "ba498eeb", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["ltm = LongTermMemory()\n", "ltm.create_file(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder1\\1.txt')\n", "ltm.create_file(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder1\\1.txt') "]}, {"cell_type": "markdown", "id": "0e6d966a", "metadata": {}, "source": ["```\n", "开发逻辑：\n", "    在LongTermMemory类中定义名字为append_qa的方法。\n", "    功能：实现安全追加QA对到文件的功能，自动创建缺失的路径。\n", "    参数：\n", "        1）file_path：str，要追加的文件路径；\n", "        2）question：str，问题内容；\n", "        3）answer：str，回答内容。\n", "```"]}, {"cell_type": "markdown", "id": "8aef04d9", "metadata": {}, "source": ["```\n", "测试逻辑：\n", "    1)首先测试带有级联目录，并且文件和文件夹都不存在的情况下，文件夹和文件是否会被创建。同时，文本数据是否会被成功的追加到文件中；\n", "    2）再测试向存在的文件追加内容是否会成功。\n", "```"]}, {"cell_type": "code", "execution_count": 15, "id": "fa34db9c", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ltm = LongTermMemory()\n", "ltm.append_qa(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\1.txt','问题1','答案1')\n", "ltm.append_qa(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\1.txt','问题2','答案2')"]}, {"cell_type": "markdown", "id": "cdd7b82e", "metadata": {}, "source": ["```\n", "开发逻辑：\n", "    在LongTermMemory类中定义名字为read_all_qa的方法。\n", "    功能：实现安全读取文件中全部QA对的功能，处理文件不存在的情况。\n", "    参数：file_path：str，要读取的文件路径\n", "```"]}, {"cell_type": "markdown", "id": "3c1b4ef3", "metadata": {}, "source": ["```\n", "测试逻辑：\n", "    1)首先测试不存在的文件被读取的场景，是否会被容错；\n", "    2)再测试读取存在的文件，是否能够读取全部内容。\n", "```"]}, {"cell_type": "code", "execution_count": 16, "id": "17cdbeba", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["ltm = LongTermMemory()\n", "ltm.read_all_qa(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\2.txt')"]}, {"cell_type": "code", "execution_count": 17, "id": "985a7d25", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Q': '问题1', 'A': '答案1'}, {'Q': '问题2', 'A': '答案2'}]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["ltm.read_all_qa(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\1.txt')"]}, {"cell_type": "markdown", "id": "a4f64700", "metadata": {}, "source": ["```\n", "开发逻辑：\n", "    在LongTermMemory类中定义名字为read_recent_qa的方法。\n", "    功能：实现安全读取文件中最近k个QA对的功能，自动处理无效k值。\n", "    参数：\n", "        1）file_path：str，要读取的文件路径。\n", "        2）k：int，要读取的最近QA对的数量。\n", "```"]}, {"cell_type": "markdown", "id": "ea03948d", "metadata": {}, "source": ["```\n", "测试逻辑：\n", "    1)首先测试非法k为负值，是否可以获取最新的k条数据；\n", "    2)再测试k为正值，是否可以获取最新的k条数据。\n", "```"]}, {"cell_type": "code", "execution_count": 18, "id": "c38df91d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'Q': '问题2', 'A': '答案2'}]\n", "[{'Q': '问题2', 'A': '答案2'}]\n"]}, {"data": {"text/plain": ["[{'Q': '问题1', 'A': '答案1'}, {'Q': '问题2', 'A': '答案2'}]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["ltm = LongTermMemory()\n", "print(ltm.read_recent_qa(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\1.txt',-1))\n", "print(ltm.read_recent_qa(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\1.txt',1))\n", "ltm.read_recent_qa(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\1.txt',2)"]}, {"cell_type": "markdown", "id": "0681b841", "metadata": {}, "source": ["```\n", "开发逻辑：\n", "    在LongTermMemory类中定义名字为get_qa_count的方法。\n", "    功能：实现安全获取文件中QA对数量的功能。\n", "    参数：file_path：str，要获取数量的文件路径。\n", "```"]}, {"cell_type": "markdown", "id": "c266173b", "metadata": {}, "source": ["```\n", "测试逻辑：\n", "    1)首先测试不存在的文件，是否可以容错；\n", "    2)再测试存在的文件，是否可以返回正确的QA值。\n", "```"]}, {"cell_type": "code", "execution_count": 19, "id": "d3e8f0d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n"]}, {"data": {"text/plain": ["0"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["ltm = LongTermMemory()\n", "print(ltm.get_qa_count(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\1.txt'))\n", "ltm.get_qa_count(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\2.txt')"]}, {"cell_type": "markdown", "id": "8b687aac", "metadata": {}, "source": ["```\n", "开发逻辑:\n", "    在LongTermMemory类中定义名字为get_token_count的方法。\n", "    功能：实现安全获取文件中内容的Token数量的功能，处理读取错误。\n", "    参数：file_path：str，要获取Token数量的文件路径。\n", "```"]}, {"cell_type": "markdown", "id": "f0f2f32b", "metadata": {}, "source": ["```\n", "测试逻辑：\n", "    1)首先测试不存在的文件，是否可以容错；\n", "    2)再测试存在的文件，是否可以返回正确的token值。\n", "```"]}, {"cell_type": "code", "execution_count": 20, "id": "9e2eb772", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n"]}, {"data": {"text/plain": ["62"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["ltm = LongTermMemory()\n", "print(ltm.get_token_count(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\2.txt'))\n", "ltm.get_token_count(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\1.txt')"]}, {"cell_type": "markdown", "id": "12f13494", "metadata": {}, "source": ["# 3.多轮对话"]}, {"cell_type": "code", "execution_count": null, "id": "09611250-deac-4a6c-a3ec-d5bc0a193a84", "metadata": {}, "outputs": [], "source": ["def auto_chat(client,\n", "              user_input,\n", "              system_base_info=\"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\",\n", "              system_extend_info=\"\",\n", "              tools = None,\n", "              model='deepseek-chat'):\n", "    #系统角色的消息\n", "    system_message = {\"role\":\"system\",\"content\":system_base_info + \"%s\" %system_extend_info}\n", "    #用户角色的消息\n", "    user_message={\"role\":\"user\",\"content\":user_input}\n", "    # global short_memory\n", "    #创建长期记忆\n", "    long_memory = LongTermMemory()\n", "    #创建短期记忆\n", "    short_memory = ShortMemoryBaseCount(count_threshold=20)\n", "    #把系统角色消息追加到短期记忆里面\n", "    short_memory.append_message(system_message)\n", "    #把用户消息追加到短期记忆里面\n", "    short_memory.append_message(user_message)\n", "    #开始多轮对话了\n", "    while True:           \n", "        print(f\"用户输入的问题是: {user_input}\")\n", "        result = auto_call(client,short_memory.get_messages(),tools=tools,model=model)\n", "        print(f\"大语言模型返回结果: {result}\")\n", "        while True:\n", "            flag = input('输入[1]则保存到长期记忆，输入[2]则重新输入问题让大模型回答')\n", "            if flag == '1':\n", "                long_memory.append_qa(  r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\3.txt' , \n", "                                        question='Q:' + user_input, \n", "                                        answer='A:' + result)\n", "                short_memory.append_message({\"role\": \"assistant\", \"content\": result})\n", "                break\n", "            else:\n", "                print('您对当前的答案不满意，请重新输入问题：')\n", "                user_input = input(\"我对当前大模型回复不满意，请重新输入用户的问题：\")\n", "                messages = short_memory.get_messages()\n", "                messages[-1][\"content\"] = user_input\n", "                result = auto_call(client,short_memory.get_messages(),tools=tools,model=model)\n", "                print(f\"模型回答: {result}\")\n", "        user_input = input(\"您还有其他问题？(输入0结束对话): \")\n", "        short_memory.append_message({\"role\":\"user\",\"content\":user_input})\n", "        if user_input == \"0\":\n", "            break"]}, {"cell_type": "code", "execution_count": 22, "id": "cd18f706-01f9-42d7-83f9-3d6f4e30d2da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["用户输入的问题是: 你好呀\n", "大语言模型返回结果: 你好！很高兴见到你！有什么关于数据分析的问题需要我帮忙解答吗？或者你想了解一些大数据相关的知识？\n", "用户输入的问题是: 1+1等于几\n", "大语言模型返回结果: 1 + 1 等于 2。  \n", "\n", "不过，如果你是在问大数据领域中的“1+1”，可能会涉及到数据聚合、合并或其他操作，比如两个数据集的合并结果可能不仅仅是简单的数值相加。如果你有更具体的场景或问题，可以告诉我哦！\n", "您对当前的答案不满意，请重新输入问题：\n", "模型回答: 哈哈，这个问题可难倒我了！不过我可以帮你分析一下数据，比如根据你的饮食偏好、附近餐厅的评价数据，甚至是天气情况来推荐午餐选择。  \n", "\n", "如果你有具体的需求，比如：  \n", "- 附近有什么好吃的？  \n", "- 健康饮食推荐？  \n", "- 快速解决午餐的方案？  \n", "\n", "可以告诉我，我可以帮你整理一些建议！或者，你也可以告诉我你的口味偏好（比如喜欢辣的、清淡的、中式还是西式），我可以给你一些灵感哦！ 😊\n"]}], "source": ["user_input = \"你好呀\"\n", "auto_chat(client,user_input=user_input,system_extend_info=\"\",tools=tools)"]}, {"cell_type": "code", "execution_count": 23, "id": "9ddf6273-dabc-458c-902e-1b7d666a8cb7", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Q': 'Q:你好呀', 'A': 'A:你好！很高兴见到你！有什么关于数据分析的问题需要我帮忙解答吗？或者你想了解一些大数据相关的知识？'},\n", " {'Q': 'Q:中午吃什么',\n", "  'A': 'A:哈哈，这个问题可难倒我了！不过我可以帮你分析一下数据，比如根据你的饮食偏好、附近餐厅的评价数据，甚至是天气情况来推荐午餐选择。  \\n\\n如果你有具体的需求，比如：  \\n- 附近有什么好吃的？  \\n- 健康饮食推荐？  \\n- 快速解决午餐的方案？  \\n\\n可以告诉我，我可以帮你整理一些建议！或者，你也可以告诉我你的口味偏好（比如喜欢辣的、清淡的、中式还是西式），我可以给你一些灵感哦！ 😊'}]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["LongTermMemory().read_all_qa(r'C:\\Users\\<USER>\\Desktop\\test\\test_folder2\\3.txt')"]}, {"cell_type": "markdown", "id": "bdb8bd64", "metadata": {}, "source": ["# 思考题\n", "如果让 auto_chat 重启（首次启动，没有进行任何对话），怎么让智能体恢复记忆。"]}, {"cell_type": "code", "execution_count": null, "id": "9cb88501-66a6-4609-8311-9700e615f43b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "zjou-2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}