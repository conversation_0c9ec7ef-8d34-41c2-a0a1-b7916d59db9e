#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重置记忆文件 - 清空现有记录，准备新的测试
"""

import os
import json
from pathlib import Path

def reset_memory_files():
    """重置记忆文件"""
    print("🔄 重置长期记忆文件...")
    
    memory_dir = Path("powerbank_memory")
    qa_file = memory_dir / "qa_history.json"
    analysis_file = memory_dir / "analysis_history.json"
    
    try:
        # 确保目录存在
        memory_dir.mkdir(exist_ok=True)
        
        # 重置为空数组
        empty_data = "[]"
        
        qa_file.write_text(empty_data, encoding='utf-8')
        analysis_file.write_text(empty_data, encoding='utf-8')
        
        print(f"✅ 已重置: {qa_file}")
        print(f"✅ 已重置: {analysis_file}")
        print("💡 现在可以重新运行智能体系统进行测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 重置失败: {e}")
        return False

if __name__ == "__main__":
    reset_memory_files()
