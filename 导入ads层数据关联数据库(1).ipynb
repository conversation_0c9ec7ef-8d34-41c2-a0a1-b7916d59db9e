{"cells": [{"cell_type": "code", "execution_count": null, "id": "5ae7310c", "metadata": {}, "outputs": [], "source": ["# 导入ads层数据\n", "import pandas as pd\n", "import pymysql\n", "\n", "# 初始化数据库连接\n", "connection = pymysql.connect(\n", "    host='localhost',\n", "    user='root',\n", "    password='your_password',  # 请替换为您的密码\n", "    database='powerbank_dw',\n", "    charset='utf8mb4'\n", ")\n", "\n", "def create_and_import_all_ads_tables():\n", "    \"\"\"创建并导入ADS层的四个数据表\"\"\"\n", "    \n", "    try:\n", "        cursor = connection.cursor()\n", "        \n", "        # 1. 创建所有表结构\n", "        print(\"🏗️ 正在创建ADS层表结构...\")\n", "        \n", "        # 创建订单表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS ads_order_table (\n", "                订单ID INT PRIMARY KEY,\n", "                用户ID INT,\n", "                地区ID INT,\n", "                时间ID INT,\n", "                开始时间 DATETIME,\n", "                结束时间 DATETIME,\n", "                单价 DECIMAL(10,2),\n", "                品牌 VARCHAR(50)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建地区表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS ads_region_table (\n", "                地区ID INT PRIMARY KEY,\n", "                省份 VARCHAR(50),\n", "                省份简称 VARCHAR(10),\n", "                城市 VARCHAR(50),\n", "                区县 VARCHAR(50),\n", "                经度 DECIMAL(10,6),\n", "                纬度 DECIMAL(10,6)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建时间表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS ads_time_table (\n", "                时间ID INT PRIMARY KEY,\n", "                日期数字 INT,\n", "                标准日期 DATE,\n", "                中文日期 VARCHAR(50),\n", "                年月 VARCHAR(20),\n", "                年月中文 VARCHAR(20),\n", "                年份 INT,\n", "                年份中文 VARCHAR(20),\n", "                星期 VARCHAR(10),\n", "                英文星期 VARCHAR(20),\n", "                周数 INT,\n", "                年周 VARCHAR(20),\n", "                星期数 INT,\n", "                年内天数 INT\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建用户表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS ads_user_table (\n", "                用户ID INT PRIMARY KEY,\n", "                姓名 VARCHAR(50),\n", "                年龄 INT,\n", "                性别 VARCHAR(10),\n", "                地址 VARCHAR(200),\n", "                职业 VARCHAR(50)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        print(\"✅ 所有ADS层表结构创建完成\")\n", "        \n", "        # 2. 导入数据\n", "        print(\"🔄 开始导入ADS层数据...\")\n", "        \n", "        # 导入订单数据\n", "        print(\"🔄 正在导入订单数据...\")\n", "        order_df = pd.read_csv(r'C:\\Users\\<USER>\\Desktop\\Shared-Power-Bank-Data-Warehouse-master\\data\\order.csv', encoding='utf-8')\n", "        for index, row in order_df.iterrows():\n", "            cursor.execute(\"\"\"\n", "                INSERT IGNORE INTO ads_order_table (订单ID, 用户ID, 地区ID, 时间ID, 开始时间, 结束时间, 单价, 品牌) \n", "                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)\n", "            \"\"\", (row['订单ID'], row['用户ID'], row['地区ID'], row['时间ID'], \n", "                  row['开始时间'], row['结束时间'], row['单价'], row['品牌']))\n", "        \n", "        # 导入地区数据\n", "        print(\"🔄 正在导入地区数据...\")\n", "        region_df = pd.read_csv(r'C:\\Users\\<USER>\\Desktop\\Shared-Power-Bank-Data-Warehouse-master\\data\\cleaned_data\\region_table.csv', encoding='utf-8')\n", "        for index, row in region_df.iterrows():\n", "            cursor.execute(\"\"\"\n", "                INSERT IGNORE INTO ads_region_table (地区ID, 省份, 省份简称, 城市, 区县, 经度, 纬度) \n", "                VALUES (%s, %s, %s, %s, %s, %s, %s)\n", "            \"\"\", (row['地区ID'], row['省份'], row['省份简称'], row['城市'], \n", "                  row['区县'], row['经度'], row['纬度']))\n", "        \n", "        # 导入时间数据\n", "        print(\"🔄 正在导入时间数据...\")\n", "        time_df = pd.read_csv(r'C:\\Users\\<USER>\\Desktop\\Shared-Power-Bank-Data-Warehouse-master\\data\\cleaned_data\\time_table.csv', encoding='utf-8')\n", "        for index, row in time_df.iterrows():\n", "            cursor.execute(\"\"\"\n", "                INSERT IGNORE INTO ads_time_table (时间ID, 日期数字, 标准日期, 中文日期, 年月, 年月中文, 年份, 年份中文, 星期, 英文星期, 周数, 年周, 星期数, 年内天数) \n", "                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)\n", "            \"\"\", (row['时间ID'], row['日期数字'], row['标准日期'], row['中文日期'], \n", "                  row['年月'], row['年月中文'], row['年份'], row['年份中文'], \n", "                  row['星期'], row['英文星期'], row['周数'], row['年周'], \n", "                  row['星期数'], row['年内天数']))\n", "        \n", "        # 导入用户数据\n", "        print(\"🔄 正在导入用户数据...\")\n", "        user_df = pd.read_csv(r'C:\\Users\\<USER>\\Desktop\\Shared-Power-Bank-Data-Warehouse-master\\data\\cleaned_data\\user_table.csv', encoding='utf-8')\n", "        for index, row in user_df.iterrows():\n", "            cursor.execute(\"\"\"\n", "                INSERT IGNORE INTO ads_user_table (用户ID, 姓名, 年龄, 性别, 地址, 职业) \n", "                VALUES (%s, %s, %s, %s, %s, %s)\n", "            \"\"\", (row['用户ID'], row['姓名'], row['年龄'], row['性别'], \n", "                  row['地址'], row['职业']))\n", "        \n", "        connection.commit()\n", "        \n", "        # 验证导入结果\n", "        tables = ['ads_order_table', 'ads_region_table', 'ads_time_table', 'ads_user_table']\n", "        print(\"\\n📊 导入结果统计:\")\n", "        for table in tables:\n", "            cursor.execute(f\"SELECT COUNT(*) FROM {table}\")\n", "            count = cursor.fetchone()[0]\n", "            print(f\"✅ {table}: {count} 条记录\")\n", "            \n", "        print(\"\\n🎉 所有ADS层数据导入完成！\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 导入过程中出现错误: {e}\")\n", "        connection.rollback()\n", "    finally:\n", "        cursor.close()\n", "\n", "# 执行完整的四表导入\n", "create_and_import_all_ads_tables()"]}, {"cell_type": "code", "execution_count": null, "id": "94e89c36", "metadata": {}, "outputs": [], "source": ["# 导入ads层数据\n", "import pandas as pd\n", "import pymysql\n", "\n", "def create_and_import_all_five_ads_tables():\n", "    \"\"\"创建并导入ADS层的五个汇总表 \"\"\"\n", "    \n", "    try:\n", "        cursor = connection.cursor()\n", "        \n", "        # 1. 创建所有表结构\n", "        print(\"🏗️ 正在创建ADS层表结构...\")\n", "        \n", "        # 创建brand_revenue_summary表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS brand_revenue_summary (\n", "                品牌名称 VARCHAR(50) PRIMARY KEY,\n", "                订单数量 INT,\n", "                总收入 DECIMAL(15,2),\n", "                平均单价 DECIMAL(10,2),\n", "                市场份额 DECIMAL(5,2)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建user_behavior_summary表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS user_behavior_summary (\n", "                用户ID INT PRIMARY KEY,\n", "                使用次数 INT,\n", "                总消费 DECIMAL(15,2)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建time_summary表（修正后的结构）\n", "        cursor.execute(\"DROP TABLE IF EXISTS time_summary\")\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE time_summary (\n", "                汇总类型 VARCHAR(20),\n", "                年份 INT,\n", "                记录数 INT,\n", "                PRIMARY KEY (汇总类型, 年份)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建region_usage_summary表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS region_usage_summary (\n", "                地区ID INT PRIMARY KEY,\n", "                总收入 DECIMAL(15,2)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建region_heatmap_data表（修正后的结构）\n", "        cursor.execute(\"DROP TABLE IF EXISTS region_heatmap_data\")\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE region_heatmap_data (\n", "                省份 VARCHAR(50),\n", "                城市 VARCHAR(50),\n", "                总收入 DECIMAL(15,2),\n", "                PRIMARY KEY (省份, 城市)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        print(\"✅ 所有ADS层表结构创建完成\")\n", "        \n", "        # 2. 导入数据\n", "        print(\"🔄 开始导入ADS层数据...\")\n", "        \n", "        # 导入brand_revenue_summary\n", "        print(\"🔄 正在导入 brand_revenue_summary...\")\n", "        brand_df = pd.read_csv(r'C:\\Users\\<USER>\\Desktop\\Shared-Power-Bank-Data-Warehouse-master\\data\\ADS\\brand_revenue_summary.csv', encoding='utf-8')\n", "        for index, row in brand_df.iterrows():\n", "            cursor.execute(\"\"\"\n", "                INSERT IGNORE INTO brand_revenue_summary (品牌名称, 订单数量, 总收入, 平均单价, 市场份额) \n", "                VALUES (%s, %s, %s, %s, %s)\n", "            \"\"\", (row['品牌名称'], row['订单数量'], row['总收入'], row['平均单价'], row['市场份额']))\n", "        \n", "        # 导入user_behavior_summary\n", "        print(\"🔄 正在导入 user_behavior_summary...\")\n", "        user_behavior_df = pd.read_csv(r'C:\\Users\\<USER>\\Desktop\\Shared-Power-Bank-Data-Warehouse-master\\data\\ADS\\user_behavior_summary.csv', encoding='utf-8')\n", "        for index, row in user_behavior_df.iterrows():\n", "            cursor.execute(\"\"\"\n", "                INSERT IGNORE INTO user_behavior_summary (用户ID, 使用次数, 总消费) \n", "                VALUES (%s, %s, %s)\n", "            \"\"\", (row['用户ID'], row['使用次数'], row['总消费']))\n", "        \n", "        # 导入time_summary\n", "        print(\"🔄 正在导入 time_summary...\")\n", "        time_summary_df = pd.read_csv(r'C:\\Users\\<USER>\\Desktop\\Shared-Power-Bank-Data-Warehouse-master\\data\\ADS\\time_summary.csv', encoding='utf-8')\n", "        for index, row in time_summary_df.iterrows():\n", "            cursor.execute(\"\"\"\n", "                INSERT IGNORE INTO time_summary (汇总类型, 年份, 记录数) \n", "                VALUES (%s, %s, %s)\n", "            \"\"\", (row['汇总类型'], row['年份'], row['记录数']))\n", "        \n", "        # 导入region_usage_summary\n", "        print(\"🔄 正在导入 region_usage_summary...\")\n", "        region_usage_df = pd.read_csv(r'C:\\Users\\<USER>\\Desktop\\Shared-Power-Bank-Data-Warehouse-master\\data\\ADS\\region_usage_summary.csv', encoding='utf-8')\n", "        for index, row in region_usage_df.iterrows():\n", "            cursor.execute(\"\"\"\n", "                INSERT IGNORE INTO region_usage_summary (地区ID, 总收入) \n", "                VALUES (%s, %s)\n", "            \"\"\", (row['地区ID'], row['总收入']))\n", "        \n", "        # 导入region_heatmap_data\n", "        print(\"🔄 正在导入 region_heatmap_data...\")\n", "        region_heatmap_df = pd.read_csv(r'C:\\Users\\<USER>\\Desktop\\Shared-Power-Bank-Data-Warehouse-master\\data\\ADS\\region_heatmap_data.csv', encoding='utf-8')\n", "        for index, row in region_heatmap_df.iterrows():\n", "            cursor.execute(\"\"\"\n", "                INSERT IGNORE INTO region_heatmap_data (省份, 城市, 总收入) \n", "                VALUES (%s, %s, %s)\n", "            \"\"\", (row['省份'], row['城市'], row['总收入']))\n", "        \n", "        connection.commit()\n", "        \n", "        # 验证导入结果\n", "        tables = ['brand_revenue_summary', 'user_behavior_summary', 'time_summary', 'region_usage_summary', 'region_heatmap_data']\n", "        print(\"\\n📊 导入结果统计:\")\n", "        for table in tables:\n", "            cursor.execute(f\"SELECT COUNT(*) FROM {table}\")\n", "            count = cursor.fetchone()[0]\n", "            print(f\"✅ {table}: {count} 条记录\")\n", "            \n", "        print(\"\\n🎉 所有ADS层数据导入完成！\")\n", "        \n", "        # 显示表结构信息\n", "        print(\"\\n📋 表结构信息:\")\n", "        for table in tables:\n", "            cursor.execute(f\"DESCRIBE {table}\")\n", "            columns = cursor.fetchall()\n", "            print(f\"\\n{table}:\")\n", "            for col in columns:\n", "                print(f\"  - {col[0]}: {col[1]}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 导入过程中出现错误: {e}\")\n", "        connection.rollback()\n", "    finally:\n", "        cursor.close()\n", "\n", "# 执行完整的五表导入\n", "create_and_import_all_five_ads_tables()\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "60713255", "metadata": {}, "outputs": [], "source": ["# 1. 创建9个表结构\n", "        print(\"🏗️ 正在创建表结构...\")\n", "        \n", "        # 创建user_table\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS user_table (\n", "                用户ID INT PRIMARY KEY,\n", "                姓名 VARCHAR(100),\n", "                年龄 INT,\n", "                性别 VARCHAR(10),\n", "                地址 VARCHAR(200),\n", "                职业 VARCHAR(100)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建region_table\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS region_table (\n", "                地区ID INT PRIMARY KEY,\n", "                省份 VARCHAR(50),\n", "                省份简称 VARCHAR(20),\n", "                城市 VARCHAR(50),\n", "                区县 VARCHAR(50),\n", "                经度 DECIMAL(10,6),\n", "                纬度 DECIMAL(10,6)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建time_table\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS time_table (\n", "                时间ID INT PRIMARY KEY,\n", "                日期数字 INT,\n", "                标准日期 DATE,\n", "                中文日期 VARCHAR(50),\n", "                年月 VARCHAR(20),\n", "                年月中文 VARCHAR(30),\n", "                年份 INT,\n", "                年份中文 VARCHAR(20),\n", "                星期 VARCHAR(20),\n", "                英文星期 VARCHAR(20),\n", "                周数 INT,\n", "                年周 VARCHAR(30),\n", "                星期数 INT,\n", "                年内天数 INT\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建order_table\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS order_table (\n", "                订单ID VARCHAR(20) PRIMARY KEY,\n", "                用户ID INT,\n", "                地区ID INT,\n", "                时间ID INT,\n", "                开始时间 VARCHAR(10),\n", "                结束时间 VARCHAR(10),\n", "                单价 INT,\n", "                品牌 VARCHAR(50)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "\n", "\n", "        # 创建brand_revenue_summary表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS brand_revenue_summary (\n", "                品牌名称 VARCHAR(50) PRIMARY KEY,\n", "                订单数量 INT,\n", "                总收入 DECIMAL(15,2),\n", "                平均单价 DECIMAL(10,2),\n", "                市场份额 DECIMAL(5,2)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建user_behavior_summary表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS user_behavior_summary (\n", "                用户ID INT PRIMARY KEY,\n", "                使用次数 INT,\n", "                总消费 DECIMAL(15,2)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建time_summary表（修正后的结构）\n", "        cursor.execute(\"DROP TABLE IF EXISTS time_summary\")\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE time_summary (\n", "                汇总类型 VARCHAR(20),\n", "                年份 INT,\n", "                记录数 INT,\n", "                PRIMARY KEY (汇总类型, 年份)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建region_usage_summary表\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE IF NOT EXISTS region_usage_summary (\n", "                地区ID INT PRIMARY KEY,\n", "                总收入 DECIMAL(15,2)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")\n", "        \n", "        # 创建region_heatmap_data表（修正后的结构）\n", "        cursor.execute(\"DROP TABLE IF EXISTS region_heatmap_data\")\n", "        cursor.execute(\"\"\"\n", "            CREATE TABLE region_heatmap_data (\n", "                省份 VARCHAR(50),\n", "                城市 VARCHAR(50),\n", "                总收入 DECIMAL(15,2),\n", "                PRIMARY KEY (省份, 城市)\n", "            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n", "        \"\"\")"]}, {"cell_type": "markdown", "id": "3c0499da", "metadata": {}, "source": ["# ADS层数据导入脚本总结文档\n", "\n", "## 📋 文件概述\n", "**文件名**: `导入ads层数据关联数据库.ipynb`  \n", "**功能**: 创建并导入共享充电宝数据仓库ADS层的9个数据表  \n", "**开发语言**: Python  \n", "**主要依赖**: pandas, pymysql  \n", "\n", "## 🏗️ 核心功能函数\n", "\n", "### `create_and_import_all_five_ads_tables()`\n", "**功能描述**: 创建并导入ADS层的五个汇总分析表\n", "\n", "#### 创建的表结构:\n", "\n", "1. **brand_revenue_summary** - 品牌收入汇总表\n", "   - `品牌名称` VARCHAR(50) PRIMARY KEY\n", "   - `订单数量` INT\n", "   - `总收入` DECIMAL(15,2)\n", "   - `平均单价` DECIMAL(10,2)\n", "   - `市场份额` DECIMAL(5,2)\n", "\n", "2. **user_behavior_summary** - 用户行为汇总表\n", "   - `用户ID` INT PRIMARY KEY\n", "   - `使用次数` INT\n", "   - `总消费` DECIMAL(15,2)\n", "\n", "3. **time_summary** - 时间维度汇总表\n", "   - `汇总类型` VARCHAR(20)\n", "   - `年份` INT\n", "   - `记录数` INT\n", "   - PRIMARY KEY (汇总类型, 年份)\n", "\n", "4. **region_usage_summary** - 地区使用汇总表\n", "   - `地区ID` INT PRIMARY KEY\n", "   - `总收入` DECIMAL(15,2)\n", "\n", "5. **region_heatmap_data** - 地区热力图数据表\n", "   - `省份` VARCHAR(50)\n", "   - `城市` VARCHAR(50)\n", "   - `总收入` DECIMAL(15,2)\n", "   - PRIMARY KEY (省份, 城市)\n", "\n", "#### 数据导入流程:\n", "1. 从CSV文件读取数据 (`pd.read_csv`)\n", "2. 逐行插入数据库 (`INSERT IGNORE`)\n", "3. 提交事务 (`connection.commit()`)\n", "4. 验证导入结果 (`SELECT COUNT(*)`)\n", "5. 显示表结构信息 (`DESCRIBE`)\n", "\n", "#### 导入的CSV文件:\n", "- `data/ADS/brand_revenue_summary.csv`\n", "- `data/ADS/user_behavior_summary.csv`\n", "- `data/ADS/time_summary.csv`\n", "- `data/ADS/region_usage_summary.csv`\n", "- `data/ADS/region_heatmap_data.csv`\n", "\n", "## 🔧 技术特点\n", "\n", "### 数据库配置\n", "- **字符集**: utf8mb4\n", "- **排序规则**: utf8mb4_unicode_ci\n", "- **支持中文**: 完全支持中文字段和数据\n", "\n", "### 错误处理机制\n", "- 使用 `INSERT IGNORE` 避免重复数据插入\n", "- `try-except` 异常处理\n", "- 事务回滚机制 (`connection.rollback()`)\n", "- 资源清理 (`cursor.close()`)\n", "\n", "### 数据验证功能\n", "- 导入后自动统计各表记录数\n", "- 显示详细的表结构信息\n", "- 提供导入状态反馈\n", "\n", "## 📊 数据表关系\n", "\n", "### ADS层表分类:\n", "1. **业务汇总表**:\n", "   - `brand_revenue_summary`: 品牌维度分析\n", "   - `user_behavior_summary`: 用户维度分析\n", "   - `region_usage_summary`: 地区维度分析\n", "\n", "2. **时间分析表**:\n", "   - `time_summary`: 时间维度统计\n", "\n", "3. **可视化数据表**:\n", "   - `region_heatmap_data`: 地区热力图展示\n", "\n", "## 🚀 执行流程\n", "\n", "1. **环境准备**\n", "   ```python\n", "   import pandas as pd\n", "   import pymysql\n", "   ```\n", "\n", "2. **数据库连接** (需要预先建立connection对象)\n", "\n", "3. **表结构创建**\n", "   - 删除已存在的表 (`DROP TABLE IF EXISTS`)\n", "   - 创建新表结构 (`CREATE TABLE`)\n", "\n", "4. **数据导入**\n", "   - 读取CSV文件\n", "   - 批量插入数据\n", "   - 事务提交\n", "\n", "5. **结果验证**\n", "   - 统计记录数量\n", "   - 显示表结构\n", "   - 输出执行状态\n", "\n", "## ⚠️ 注意事项\n", "\n", "### 前置条件:\n", "- 需要预先建立MySQL数据库连接 (`connection` 对象)\n", "- 确保CSV文件路径正确\n", "- 数据库用户需要有CREATE、INSERT权限\n", "\n", "### 数据处理:\n", "- 使用 `encoding='utf-8'` 处理中文编码\n", "- `INSERT IGNORE` 处理重复数据\n", "- DECIMAL类型处理金额数据\n", "\n", "### 性能考虑:\n", "- 逐行插入方式，适合中小规模数据\n", "- 对于大数据量建议使用批量插入优化\n", "\n", "## 📈 预期输出结果\n", "\n", "执行成功后会显示:\n", "```\n", "🏗️ 正在创建ADS层表结构...\n", "✅ 所有ADS层表结构创建完成\n", "🔄 开始导入ADS层数据...\n", "🔄 正在导入 brand_revenue_summary...\n", "🔄 正在导入 user_behavior_summary...\n", "🔄 正在导入 time_summary...\n", "🔄 正在导入 region_usage_summary...\n", "🔄 正在导入 region_heatmap_data...\n", "\n", "📊 导入结果统计:\n", "✅ brand_revenue_summary: X 条记录\n", "✅ user_behavior_summary: X 条记录\n", "✅ time_summary: X 条记录\n", "✅ region_usage_summary: X 条记录\n", "✅ region_heatmap_data: X 条记录\n", "\n", "🎉 所有ADS层数据导入完成！\n", "```\n", "\n", "## 🔍 扩展建议\n", "\n", "1. **性能优化**: 考虑使用 `executemany()` 进行批量插入\n", "2. **配置管理**: 将数据库连接参数提取到配置文件\n", "3. **日志记录**: 添加详细的日志记录功能\n", "4. **数据校验**: 增加数据质量检查机制\n", "5. **增量更新**: 支持增量数据更新功能\n", "\n", "--"]}], "metadata": {"kernelspec": {"display_name": "zjou-2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}