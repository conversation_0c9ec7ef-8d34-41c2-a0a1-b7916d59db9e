{"cells": [{"cell_type": "markdown", "id": "7b23b6be", "metadata": {}, "source": ["# 3.鸢尾花分类"]}, {"cell_type": "markdown", "id": "934fb973", "metadata": {}, "source": ["## 3.1 导入相关依赖"]}, {"cell_type": "code", "execution_count": null, "id": "a798dfdd", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn import datasets\n", "from sklearn.tree import DecisionTreeClassifier"]}, {"cell_type": "markdown", "id": "71da69c6", "metadata": {}, "source": ["## 3.2 加载数据"]}, {"cell_type": "code", "execution_count": null, "id": "a5327b71", "metadata": {}, "outputs": [], "source": ["iris = datasets.load_iris()"]}, {"cell_type": "markdown", "id": "e7359aad", "metadata": {}, "source": ["## 3.3 数据调研"]}, {"cell_type": "markdown", "id": "be8aa882", "metadata": {}, "source": ["标签 ： [0,0,0,1,1,1,2,2,2]\n", "返回值： [True,True,True,False, False, False, False, False, False, False]\n", "X: [[1.1],[2.2.],[3.3.],[4.4],[5.5],[6.6],[7.7],[8.8],[9.9],[10]]\n", "X[[True,True,True,<PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse],0]\n", "\n", "[1.1],[2.2],[3.3]\n"]}, {"cell_type": "code", "execution_count": null, "id": "bd7c899e", "metadata": {}, "outputs": [], "source": ["X = iris.data[:,2:]\n", "y = iris.target\n", "\n", "plt.scatter(X[y==0,0],X[y==0,1])\n", "plt.scatter(X[y==1,0],X[y==1,1])\n", "plt.scatter(X[y==2,0],X[y==2,1])\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "id": "42a75346", "metadata": {}, "source": ["## 3.4 模型训练"]}, {"cell_type": "code", "execution_count": null, "id": "e24b5c99", "metadata": {}, "outputs": [], "source": ["tree = DecisionTreeClassifier(max_depth=2,criterion=\"entropy\")\n", "tree.fit(X,y)"]}, {"cell_type": "markdown", "id": "07c8d751", "metadata": {}, "source": ["## 3.5 模型预测"]}, {"cell_type": "code", "execution_count": null, "id": "94be630a", "metadata": {}, "outputs": [], "source": ["X[40:60,]"]}, {"cell_type": "code", "execution_count": null, "id": "903420d7", "metadata": {}, "outputs": [], "source": ["tree.predict(X[40:60,])"]}, {"cell_type": "markdown", "id": "e45e9349", "metadata": {}, "source": ["## 3.6 查看模型得分"]}, {"cell_type": "code", "execution_count": null, "id": "ee5eaa7a", "metadata": {}, "outputs": [], "source": ["tree.score(X,y)"]}, {"cell_type": "markdown", "id": "f57f4051", "metadata": {}, "source": ["## 3.7 对构建的树模型进行可视化"]}, {"cell_type": "code", "execution_count": null, "id": "4dfa0264", "metadata": {}, "outputs": [], "source": ["# 树的深度为2\n", "from sklearn.tree import plot_tree\n", "import matplotlib.pyplot as plt\n", "plot_tree(tree,filled=True)\n", "plt.show()\n", "\n", "# X0[2.5] X1[3.6] \n", "# [\n", "#     [2.5 , 3.6]\n", "# ]\n", "# X0\n", "# 根节点 -> 信息熵(数值)\n", "# 左子节点 -> 信息熵(数值)\n", "# 右子节点 -> 信息熵(数值)\n", "# 差值-> 信息增益  \n", "# 总结:经过计算之后，发现信息增益越大的，说明用当前特征作为划分是最好的"]}, {"cell_type": "code", "execution_count": null, "id": "979f0cb9", "metadata": {}, "outputs": [], "source": ["# 树的深度为3\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.tree import plot_tree\n", "import matplotlib.pyplot as plt\n", "tree = DecisionTreeClassifier(max_depth=3,criterion=\"entropy\")\n", "tree.fit(X,y)\n", "plot_tree(tree,filled=True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "90a88d62", "metadata": {}, "outputs": [], "source": ["# 树的深度为4 \n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.tree import plot_tree\n", "import matplotlib.pyplot as plt\n", "tree = DecisionTreeClassifier(max_depth=4,criterion=\"entropy\")\n", "tree.fit(X,y)\n", "plot_tree(tree,filled=True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "31623732", "metadata": {}, "source": ["# 4.红酒分类"]}, {"cell_type": "markdown", "id": "1b7acfcc", "metadata": {}, "source": ["## 4.1 导入相关依赖"]}, {"cell_type": "code", "execution_count": null, "id": "3d27375b", "metadata": {}, "outputs": [], "source": ["from sklearn import tree\n", "from sklearn.datasets import load_wine\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import plot_tree"]}, {"cell_type": "markdown", "id": "4d60ef16", "metadata": {}, "source": ["## 4.2 加载数据"]}, {"cell_type": "code", "execution_count": null, "id": "7b75d483", "metadata": {}, "outputs": [], "source": ["wine = load_wine()"]}, {"cell_type": "code", "execution_count": null, "id": "d01eb1d1", "metadata": {}, "outputs": [], "source": ["type(wine.data)"]}, {"cell_type": "code", "execution_count": null, "id": "c00b49eb", "metadata": {}, "outputs": [], "source": ["wine.data.shape"]}, {"cell_type": "markdown", "id": "49bde0bf", "metadata": {}, "source": ["## 4.3 数据调研"]}, {"cell_type": "code", "execution_count": null, "id": "e566b582", "metadata": {}, "outputs": [], "source": ["# 查看一个数据集的信息\n", "wine.DESCR"]}, {"cell_type": "code", "execution_count": null, "id": "b1d12dd4", "metadata": {}, "outputs": [], "source": ["# 获取特征数据以及获取特征数据的形状\n", "wine.data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1dedc526", "metadata": {}, "outputs": [], "source": ["# 获取标签数据以及获取标签数据的形状\n", "wine.target.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d49a9530", "metadata": {}, "outputs": [], "source": ["# 获取特征的第一个样本数据\n", "wine.data[0]"]}, {"cell_type": "code", "execution_count": null, "id": "89490f4a", "metadata": {}, "outputs": [], "source": ["# 获取标签的第一个样本数据\n", "wine.target[0]"]}, {"cell_type": "code", "execution_count": null, "id": "96b15e5e", "metadata": {}, "outputs": [], "source": ["# 获取含有特征也含有标签的所有样本\n", "import pandas as pd\n", "pd.concat([pd.DataFrame(wine.data),pd.DataFrame(wine.target)],axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "63bd1fd2", "metadata": {}, "outputs": [], "source": ["# 获取数据集特征名字\n", "wine.feature_names"]}, {"cell_type": "code", "execution_count": null, "id": "b934f874", "metadata": {}, "outputs": [], "source": ["# 获取数据集标签名字\n", "wine.target_names"]}, {"cell_type": "markdown", "id": "61c7d3f0", "metadata": {}, "source": ["## 4.4 训练集与测试集拆分"]}, {"cell_type": "code", "execution_count": null, "id": "0d5c9ad4", "metadata": {}, "outputs": [], "source": ["Xtrain, Xtest, Ytrain, Ytest = train_test_split(wine.data,wine.target,test_size=0.2)"]}, {"cell_type": "markdown", "id": "f1b757fd", "metadata": {}, "source": ["## 4.5 构建模型以及可视化模型"]}, {"cell_type": "code", "execution_count": null, "id": "9fae2edf", "metadata": {}, "outputs": [], "source": ["from sklearn.tree import plot_tree\n", "import matplotlib.pyplot as plt\n", "clf = tree.DecisionTreeClassifier(criterion=\"entropy\")\n", "clf = clf.fit(<PERSON><PERSON><PERSON>, <PERSON>train)\n", "score = clf.score(Xtest, Ytest)\n", "print(score)\n", "plot_tree(clf,filled=True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "7c8ce16f", "metadata": {}, "source": ["# 5.重要参数"]}, {"cell_type": "markdown", "id": "998ea86e", "metadata": {}, "source": ["## 5.3 splitter"]}, {"cell_type": "code", "execution_count": null, "id": "b624f3fe", "metadata": {}, "outputs": [], "source": ["def get_data():\n", "    return (11,22,33,44)\n", "\n", "Xtrain, Xtest, Ytrain, Ytest = get_data()"]}, {"cell_type": "code", "execution_count": null, "id": "619cee09", "metadata": {}, "outputs": [], "source": ["Xtrain"]}, {"cell_type": "code", "execution_count": null, "id": "b861bb96", "metadata": {}, "outputs": [], "source": ["Xtest"]}, {"cell_type": "code", "execution_count": null, "id": "b6191cb4", "metadata": {}, "outputs": [], "source": ["# 导入相关依赖\n", "from sklearn import tree\n", "from sklearn.datasets import load_wine\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import plot_tree\n", "import matplotlib.pyplot as plt\n", "\n", "# 加载数据集   data、target\n", "wine = load_wine()\n", "# 训练集和测试集划分\n", "Xtrain, Xtest, Ytrain, Ytest = train_test_split(wine.data,wine.target,test_size=0.3)\n", "# 构建模型(决策树)\n", "clf = tree.DecisionTreeClassifier(criterion=\"entropy\"\n", "                                    ,random_state=100\n", "                                    ,splitter=\"best\"\n", "                                    )\n", "# 训练模型\n", "clf = clf.fit(<PERSON><PERSON><PERSON>, <PERSON>train)\n", "# 模型评估\n", "score = clf.score(Xtest, Ytest)\n", "print(score)\n", "plot_tree(clf,filled=True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "7861d5eb", "metadata": {}, "source": ["补充知识"]}, {"cell_type": "code", "execution_count": null, "id": "3eb30892", "metadata": {}, "outputs": [], "source": ["a = [1, 2, 3]\n", "\n", "b = ['a', 'b', 'c']\n", "\n", "zipped = zip(a, b)\n", "\n", "list(zipped)"]}, {"cell_type": "code", "execution_count": null, "id": "3e90af45", "metadata": {}, "outputs": [], "source": ["[*zip(a,b)]"]}, {"cell_type": "code", "execution_count": null, "id": "447ecb36", "metadata": {}, "outputs": [], "source": ["# 演示代码\n", "\n", "def add(a,b,c):\n", "    print(a+b+c)\n", "\n", "a = [1,2,3]\n", "\n", "add(*a)"]}, {"cell_type": "code", "execution_count": null, "id": "33f7f399", "metadata": {}, "outputs": [], "source": ["# 演示代码\n", "\n", "def test1():\n", "    return (1,2,3,4)\n", "\n", "def add(num1,num2,num3,num4):\n", "    return num1 + num2 + num3 + num4 \n", "\n", "add(*test1())"]}, {"cell_type": "code", "execution_count": null, "id": "7fab26d2", "metadata": {}, "outputs": [], "source": ["feature_name = ['酒精','苹果酸','灰','灰的碱性','镁','总酚','类黄酮','非黄烷类酚类','花青素','颜色强度','色调','od280/od315稀释葡萄酒','脯氨酸']\n", "# [*zip(feature_name,clf.feature_importances_)]\n", "# 或者\n", "list(zip(feature_name,clf.feature_importances_))"]}, {"cell_type": "markdown", "id": "e5f1c48a", "metadata": {}, "source": ["## 5.5 min_samples_leaf"]}, {"cell_type": "code", "execution_count": null, "id": "72411237", "metadata": {}, "outputs": [], "source": ["from sklearn import tree\n", "from sklearn.datasets import load_wine\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import plot_tree\n", "import matplotlib.pyplot as plt\n", "wine = load_wine()\n", "Xtrain, Xtest, Ytrain, Ytest = train_test_split(wine.data,wine.target,test_size=0.3,random_state=1)\n", "clf = tree.DecisionTreeClassifier(criterion=\"entropy\"\n", "                                    ,random_state=100\n", "                                    ,splitter=\"best\"\n", "                                    ,max_depth=3\n", "                                    ,min_samples_leaf=20\n", "                                    )\n", "clf = clf.fit(<PERSON><PERSON><PERSON>, <PERSON>train)\n", "score = clf.score(Xtest, Ytest)\n", "print(score)\n", "plot_tree(clf,filled=True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "ef0b361f", "metadata": {}, "source": ["## 5.6 min_samples_split"]}, {"cell_type": "code", "execution_count": null, "id": "7d8e598a", "metadata": {}, "outputs": [], "source": ["from sklearn import tree\n", "from sklearn.datasets import load_wine\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import plot_tree\n", "import matplotlib.pyplot as plt\n", "wine = load_wine()\n", "Xtrain, Xtest, Ytrain, Ytest = train_test_split(wine.data,wine.target,test_size=0.3,random_state=1)\n", "clf = tree.DecisionTreeClassifier(criterion=\"entropy\"\n", "                                    ,random_state=100\n", "                                    ,splitter=\"best\"\n", "                                    ,max_depth=3\n", "                                    ,min_samples_split=40\n", "                                    )\n", "clf = clf.fit(<PERSON><PERSON><PERSON>, <PERSON>train)\n", "score = clf.score(Xtest, Ytest)\n", "print(score)\n", "plot_tree(clf,filled=True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "bfb14ca9", "metadata": {}, "source": ["## 5.7 min_impurity_decrease"]}, {"cell_type": "code", "execution_count": null, "id": "4d158aaf", "metadata": {}, "outputs": [], "source": ["from sklearn import tree\n", "from sklearn.datasets import load_wine\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import plot_tree\n", "import matplotlib.pyplot as plt\n", "wine = load_wine()\n", "Xtrain, Xtest, Ytrain, Ytest = train_test_split(wine.data,wine.target,test_size=0.3,random_state=1)\n", "\n", "clf = tree.DecisionTreeClassifier(criterion=\"entropy\"\n", "                                    ,random_state=100\n", "                                    ,splitter=\"best\"\n", "                                    ,max_depth=3\n", "                                    ,min_impurity_decrease=0.2\n", "                                    )\n", "\n", "clf = clf.fit(<PERSON><PERSON><PERSON>, <PERSON>train)\n", "score = clf.score(Xtest, Ytest)\n", "print(score)\n", "plot_tree(clf,filled=True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "2ad99ba9", "metadata": {}, "source": ["# 7.如何确认最佳参数呢"]}, {"cell_type": "code", "execution_count": null, "id": "f8fe353f", "metadata": {}, "outputs": [], "source": ["# 导入相关依赖\n", "from sklearn import tree\n", "from sklearn.datasets import load_wine\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import plot_tree\n", "from sklearn.tree import DecisionTreeClassifier\n", "import matplotlib.pyplot as plt\n", "# 加载数据集\n", "wine = load_wine()\n", "# X列表[1,2,3,4,5,6,7,8,9,10]\n", "# 分数 [-,-,-...-]\n", "score_list = []\n", "# 训练集和测试集拆分\n", "X_train,X_test,Y_train,Y_test = train_test_split(wine.data,wine.target,test_size=0.3,random_state=10)\n", "# 循环\n", "for i in range(10):\n", "    clf = DecisionTreeClassifier(criterion='entropy',\n", "                                 random_state=1,\n", "                                 max_depth=i+1)\n", "    clf.fit(X_train,Y_train)\n", "    score_list.append(clf.score(X_test,Y_test))\n", "plt.plot(range(1,11),score_list,color='green',label='max_best_depth')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "8c9a8c2c", "metadata": {}, "source": ["# 8. 决策树模型可视化"]}, {"cell_type": "code", "execution_count": null, "id": "3dc6bdb8", "metadata": {}, "outputs": [], "source": ["from sklearn.tree import DecisionTreeRegressor\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "rrs = np.random.RandomState(1)\n", "X = np.sort( 3 * rrs.rand(80,1) , axis=0 )\n", "y = np.sin(X).ravel()\n", "\n", "y[::8] = y[::8] + ( 2 * (0.5 - rrs.rand(10)))\n", "plt.scatter(X,y,s=20,edgecolors='red',color='blue',label='data')\n", "\n", "regr_1 = DecisionTreeRegressor(max_depth=1)\n", "regr_3 = DecisionTreeRegressor(max_depth=5) \n", "\n", "regr_1.fit(X,y)\n", "regr_3.fit(X,y)\n", "\n", "X_test = np.arange(0.0,5.0,0.01)[:,np.newaxis]\n", "\n", "y_1 = regr_1.predict(X_test)\n", "y_3 = regr_3.predict(X_test)\n", "plt.scatter(X,y,s=20,edgecolors='black',c='orange')\n", "plt.plot(X_test,y_1,color='black',label='max_depth=1',linewidth=2)\n", "plt.plot(X_test,y_3,color='green',label='max_depth=5',linewidth=2)\n", "plt.xlabel(\"x\")\n", "plt.ylabel(\"y\")\n", "plt.title(\"Decision Tree Regression\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 52, "id": "411f895f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(X,y,s=20,edgecolors='black',c='orange')\n", "plt.plot(X_test,y_1,color='black',label='max_depth=1',linewidth=2)\n", "plt.plot(X_test,y_3,color='green',label='max_depth=5',linewidth=2)\n", "plt.xlabel(\"x\")\n", "plt.ylabel(\"y\")\n", "plt.title(\"Decision Tree Regression\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 51, "id": "7b860f71", "metadata": {}, "outputs": [], "source": ["y_1 = regr_1.predict(X_test)\n", "y_3 = regr_3.predict(X_test)"]}, {"cell_type": "code", "execution_count": 50, "id": "829e59d4", "metadata": {}, "outputs": [], "source": ["X_test = np.arange(0.0,5.0,0.01)[:,np.newaxis]"]}, {"cell_type": "code", "execution_count": null, "id": "01147a17", "metadata": {}, "outputs": [], "source": ["regr_1.fit(X,y)\n", "regr_3.fit(X,y)"]}, {"cell_type": "code", "execution_count": null, "id": "32996b7b", "metadata": {}, "outputs": [], "source": ["regr_1 = DecisionTreeRegressor(max_depth=1)\n", "regr_3 = DecisionTreeRegressor(max_depth=5) "]}, {"cell_type": "code", "execution_count": null, "id": "0dcd7114", "metadata": {}, "outputs": [], "source": ["plt.scatter(X,y,s=20,edgecolors='red',color='blue',label='data')"]}, {"cell_type": "code", "execution_count": null, "id": "3fa2c073", "metadata": {}, "outputs": [], "source": ["y[::8] = y[::8] + ( 2 * (0.5 - rrs.rand(10)))"]}, {"cell_type": "code", "execution_count": null, "id": "e5ea79e2", "metadata": {}, "outputs": [], "source": ["2 * (0.5 - rrs.rand(10))"]}, {"cell_type": "code", "execution_count": null, "id": "b7b26038", "metadata": {}, "outputs": [], "source": ["y[::8]"]}, {"cell_type": "code", "execution_count": null, "id": "0f1ca627", "metadata": {}, "outputs": [], "source": ["# 标签的集合(1维)\n", "y = np.sin(X).ravel()"]}, {"cell_type": "code", "execution_count": null, "id": "6cb52cc4", "metadata": {}, "outputs": [], "source": ["# 特征的集合(2维)\n", "X = np.sort(3 * rrs.rand(80,1) , axis=0)"]}, {"cell_type": "code", "execution_count": null, "id": "6c7cf0f8", "metadata": {}, "outputs": [], "source": ["rrs.rand(80,1).size"]}, {"cell_type": "code", "execution_count": null, "id": "4df96f66", "metadata": {}, "outputs": [], "source": ["type(rrs.rand(80,1))"]}, {"cell_type": "code", "execution_count": null, "id": "d0c5ad0d", "metadata": {}, "outputs": [], "source": ["from sklearn.tree import DecisionTreeRegressor\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "rrs = np.random.RandomState(1)"]}, {"cell_type": "markdown", "id": "6cc1d837", "metadata": {}, "source": ["# 9. 泰坦尼克号生存预测"]}, {"cell_type": "markdown", "id": "f9368aaa", "metadata": {}, "source": ["## 9.1 知识补充"]}, {"cell_type": "code", "execution_count": null, "id": "5a7e5998", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "df = pd.DataFrame({'A': [1, 2, 3], 'B': [4, np.NAN, 6]})\n", "df['B'] = df['B'].fillna(df['B'].mean())\n", "df['B']"]}, {"cell_type": "markdown", "id": "2e5305a6", "metadata": {}, "source": ["## 9.3 导入相关依赖"]}, {"cell_type": "code", "execution_count": null, "id": "66521ac3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.model_selection import cross_val_score\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "f79b0fe3", "metadata": {}, "source": ["## 9.4 加载数据"]}, {"cell_type": "code", "execution_count": null, "id": "e7790bc0", "metadata": {}, "outputs": [], "source": ["data = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\day01\\data.csv.csv\" , index_col = 0)"]}, {"cell_type": "code", "execution_count": null, "id": "8c195ed8", "metadata": {}, "outputs": [], "source": ["data"]}, {"cell_type": "markdown", "id": "cdf350fd", "metadata": {}, "source": ["## 9.5 数据探索"]}, {"cell_type": "code", "execution_count": null, "id": "32af344d", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b502ad8b", "metadata": {}, "outputs": [], "source": ["data.info()"]}, {"cell_type": "markdown", "id": "21d1d3f9", "metadata": {}, "source": ["## 9.6 简单的数据预处理"]}, {"cell_type": "code", "execution_count": null, "id": "5e81c86c", "metadata": {}, "outputs": [], "source": ["# 删除无用特征，客舱号码、姓名、船票号码\n", "data1 = data.drop(['<PERSON>abin','Name','Ticket'],inplace=False,axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "63ac91ef", "metadata": {}, "outputs": [], "source": ["data1.info()"]}, {"cell_type": "code", "execution_count": null, "id": "1a26972e", "metadata": {}, "outputs": [], "source": ["# 缺失值填充\n", "data1['Age'] = data1['Age'].fillna(data1['Age'].mean())"]}, {"cell_type": "code", "execution_count": null, "id": "97b1ddac", "metadata": {}, "outputs": [], "source": ["data1.info()"]}, {"cell_type": "code", "execution_count": null, "id": "72936c8b", "metadata": {}, "outputs": [], "source": ["# 删除存在缺失值数据所在的行\n", "data1 = data1.dropna()"]}, {"cell_type": "code", "execution_count": null, "id": "c2fcd9ba", "metadata": {}, "outputs": [], "source": ["data1.info()"]}, {"cell_type": "code", "execution_count": null, "id": "5af4f477", "metadata": {}, "outputs": [], "source": ["# 文字转化数值\n", "labels = data1['Sex'].unique().tolist()\n", "data1['Sex'] = data1['Sex'].apply(lambda x : labels.index(x))\n", "labels = data1['Embarked'].unique().tolist()\n", "data1['Embarked'] = data1['Embarked'].apply(lambda x : labels.index(x))"]}, {"cell_type": "code", "execution_count": null, "id": "f95aac83", "metadata": {}, "outputs": [], "source": ["data1.info()"]}, {"cell_type": "markdown", "id": "29d1ad28", "metadata": {}, "source": ["## 9.7 获取特征和标签"]}, {"cell_type": "code", "execution_count": null, "id": "8cd137b7", "metadata": {}, "outputs": [], "source": ["X = data1.iloc[:,data1.columns!='Survived']\n", "y = data1.iloc[:,data1.columns=='Survived']"]}, {"cell_type": "markdown", "id": "291e770d", "metadata": {}, "source": ["## 9.8 测试集和训练集的划分"]}, {"cell_type": "code", "execution_count": null, "id": "4c5af701", "metadata": {}, "outputs": [], "source": ["Xtrain, Xtest, Ytrain, Ytest = train_test_split(X,y,test_size=0.3)"]}, {"cell_type": "code", "execution_count": null, "id": "052757b0", "metadata": {}, "outputs": [], "source": ["Xtrain"]}, {"cell_type": "code", "execution_count": null, "id": "7e746180", "metadata": {}, "outputs": [], "source": ["Xtrain.index = range(Xtrain.shape[0])"]}, {"cell_type": "code", "execution_count": null, "id": "4e3c6869", "metadata": {}, "outputs": [], "source": ["Xtrain"]}, {"cell_type": "code", "execution_count": null, "id": "04408606", "metadata": {}, "outputs": [], "source": ["for i in [Xtrain, Xtest, Ytrain, Ytest]:\n", "    i.index = range(i.shape[0])\n", "Xtest.head()"]}, {"cell_type": "markdown", "id": "ca76ddcb", "metadata": {}, "source": ["## 9.9 模型训练"]}, {"cell_type": "code", "execution_count": null, "id": "8b506546", "metadata": {}, "outputs": [], "source": ["clf = DecisionTreeClassifier(random_state=1)\n", "clf = clf.fit(<PERSON><PERSON><PERSON>, <PERSON>train)\n", "score = clf.score(Xtest, Ytest)\n", "print(score)"]}, {"cell_type": "markdown", "id": "5b7efee4", "metadata": {}, "source": ["## 9.10 绘制模型表现曲线"]}, {"cell_type": "code", "execution_count": 2, "id": "b82b005b", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'DecisionTreeClassifier' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 5\u001b[0m\n\u001b[0;32m      3\u001b[0m cross_score_list \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m10\u001b[39m):\n\u001b[1;32m----> 5\u001b[0m     clf \u001b[38;5;241m=\u001b[39m DecisionTreeClassifier(random_state\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m25\u001b[39m\n\u001b[0;32m      6\u001b[0m                                  ,max_depth\u001b[38;5;241m=\u001b[39mi\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m1\u001b[39m\n\u001b[0;32m      7\u001b[0m                                  ,criterion\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mentropy\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m      8\u001b[0m                                 )\n\u001b[0;32m      9\u001b[0m     clf \u001b[38;5;241m=\u001b[39m clf\u001b[38;5;241m.\u001b[39mfit(<PERSON>tra<PERSON>, Ytrain)\n\u001b[0;32m     10\u001b[0m     base \u001b[38;5;241m=\u001b[39m clf\u001b[38;5;241m.\u001b[39mscore(Xtrain,Ytrain)\n", "\u001b[1;31mNameError\u001b[0m: name 'DecisionTreeClassifier' is not defined"]}], "source": ["import numpy as np\n", "base_score_list = []\n", "cross_score_list = []\n", "for i in range(10):\n", "    clf = DecisionTreeClassifier(random_state=25\n", "                                 ,max_depth=i+1\n", "                                 ,criterion=\"entropy\"\n", "                                )\n", "    clf = clf.fit(<PERSON><PERSON><PERSON>, <PERSON>train)\n", "    base = clf.score(<PERSON><PERSON><PERSON>,<PERSON><PERSON>in)\n", "    cross_list = cross_val_score(clf,X,y,cv=5)\n", "    print(cross_list)\n", "    cross = cross_list.mean()\n", "    base_score_list.append(base)\n", "    cross_score_list.append(cross)\n", "print(max(base_score_list))\n", "print(max(cross_score_list))\n", "plt.plot(range(1,11),base_score_list,color=\"red\",label=\"base\")\n", "plt.plot(range(1,11),cross_score_list,color=\"green\",label=\"cross\")\n", "plt.xticks(range(1,11))\n", "plt.yticks(np.arange(0.5, 1.01, 0.05))\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "aecc5c09", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}