#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
智能体可视化功能演示
展示如何在实际使用中调用可视化功能
"""

import pymysql
import time
from test_viz_engine import SimpleVisualizationEngine

def demo_real_data_visualization():
    """演示真实数据可视化"""
    print("🎬 智能体可视化功能演示")
    print("=" * 60)
    
    try:
        # 连接数据库
        print("🔗 连接数据库...")
        connection = pymysql.connect(
            host='localhost',
            user='root',
            passwd='',
            db='ads',
            charset='utf8'
        )
        
        print("✅ 数据库连接成功")
        
        # 初始化可视化引擎
        viz_engine = SimpleVisualizationEngine()
        
        # 演示场景1: 品牌收入分析
        print("\n📊 演示场景1: 品牌收入分析")
        print("-" * 40)
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = """
            SELECT 品牌名称, 订单数量, 总收入, 市场份额
            FROM brand_revenue_summary 
            ORDER BY 总收入 DESC
            """
            cursor.execute(sql)
            brand_data = cursor.fetchall()
            
            print(f"✅ 查询成功，获得 {len(brand_data)} 条品牌数据")
            
            # 生成饼图
            result = viz_engine.create_chart(brand_data, "饼图", "各品牌市场份额分布")
            if result["success"]:
                print(f"📈 饼图生成成功: {result['path']}")
            
            # 生成柱状图
            result = viz_engine.create_chart(brand_data, "柱状图", "各品牌收入对比")
            if result["success"]:
                print(f"📊 柱状图生成成功: {result['path']}")
        
        # 演示场景2: 时间趋势分析
        print("\n📈 演示场景2: 时间趋势分析")
        print("-" * 40)
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = """
            SELECT 年月, 记录数 as 订单数, 总收入, 活跃用户数
            FROM time_summary 
            WHERE 年份 = 2021
            ORDER BY 时间ID
            LIMIT 12
            """
            cursor.execute(sql)
            time_data = cursor.fetchall()
            
            print(f"✅ 查询成功，获得 {len(time_data)} 条时间数据")
            
            # 生成折线图
            result = viz_engine.create_chart(time_data, "折线图", "2021年月度订单趋势")
            if result["success"]:
                print(f"📈 折线图生成成功: {result['path']}")
        
        # 演示场景3: 地区分析
        print("\n🗺️ 演示场景3: 地区收入分析")
        print("-" * 40)
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = """
            SELECT r.省份 as 地区, SUM(ru.总收入) as 总收入
            FROM region_table r
            JOIN region_usage_summary ru ON r.地区ID = ru.地区ID
            GROUP BY r.省份
            ORDER BY 总收入 DESC
            LIMIT 10
            """
            cursor.execute(sql)
            region_data = cursor.fetchall()
            
            print(f"✅ 查询成功，获得 {len(region_data)} 条地区数据")
            
            # 生成柱状图
            result = viz_engine.create_chart(region_data, "柱状图", "各省份收入排行")
            if result["success"]:
                print(f"📊 柱状图生成成功: {result['path']}")
        
        connection.close()
        
        print("\n" + "=" * 60)
        print("🎉 可视化演示完成！")
        print("📁 所有图表已保存到 powerbank_memory/ 目录")
        print("💡 你可以打开图片文件查看可视化效果")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def show_visualization_capabilities():
    """展示可视化能力"""
    print("\n🎨 智能体可视化能力展示")
    print("=" * 60)
    
    capabilities = {
        "📊 柱状图": {
            "适用场景": "分类数据对比、排行分析",
            "示例问题": "各品牌收入对比、各地区订单量排行",
            "特点": "直观对比、支持数值标签"
        },
        "🥧 饼图": {
            "适用场景": "占比分析、份额展示",
            "示例问题": "各品牌市场份额、用户年龄分布",
            "特点": "百分比显示、颜色区分"
        },
        "📈 折线图": {
            "适用场景": "趋势分析、时间序列",
            "示例问题": "月度收入趋势、用户增长曲线",
            "特点": "趋势清晰、支持多条线"
        },
        "🔥 热力图": {
            "适用场景": "相关性分析、密度展示",
            "示例问题": "地区使用热度、指标相关性",
            "特点": "颜色映射、直观展示"
        },
        "⚡ 散点图": {
            "适用场景": "关联分析、分布展示",
            "示例问题": "年龄与消费关系、使用频次分布",
            "特点": "关系清晰、支持聚类"
        },
        "📚 堆叠图": {
            "适用场景": "多维度对比、组合分析",
            "示例问题": "各地区品牌分布、多指标对比",
            "特点": "层次清晰、信息丰富"
        }
    }
    
    for chart_type, info in capabilities.items():
        print(f"\n{chart_type}")
        print(f"  适用场景: {info['适用场景']}")
        print(f"  示例问题: {info['示例问题']}")
        print(f"  特点: {info['特点']}")
    
    print(f"\n🤖 AI智能推荐:")
    print(f"  系统会根据问题语义和数据特征自动选择最佳图表类型")
    print(f"  支持中文问题理解，无需指定图表类型")
    print(f"  结合业务场景，提供专业的可视化建议")

def main():
    """主函数"""
    print("🚀 智能体可视化功能完整演示")
    print("🎯 展示真实数据的可视化能力")
    
    # 运行真实数据演示
    success = demo_real_data_visualization()
    
    # 展示能力说明
    show_visualization_capabilities()
    
    if success:
        print(f"\n🎊 演示成功完成！")
        print(f"🔗 现在你的智能体具备了完整的数据可视化能力")
        print(f"💡 用户只需提问，系统会自动生成对应的图表")
    else:
        print(f"\n⚠️ 演示过程中遇到问题，请检查数据库连接")

if __name__ == "__main__":
    main()
