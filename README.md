# 🔋 共享充电宝数据仓库项目

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.0+-green.svg)](https://flask.palletsprojects.com/)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.0+-brightgreen.svg)](https://vuejs.org/)
[![ECharts](https://img.shields.io/badge/ECharts-5.0+-orange.svg)](https://echarts.apache.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📋 项目简介

共享充电宝数据仓库项目是一个基于大数据技术栈的完整数据分析解决方案，旨在为共享充电宝行业提供全面的数据洞察和决策支持。

### 🎯 核心功能

- **📊 多维度数据分析**：品牌分析、用户行为、时间趋势、地区分布
- **🗺️ 地理热力图**：全国省份收入分布可视化
- **📈 实时数据展示**：动态图表和交互式仪表板
- **🔄 数据处理流水线**：从MySQL到Hive的完整ETL流程
- **🎨 现代化UI**：响应式设计，支持多设备访问

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据源层      │    │   数据仓库层    │    │   应用展示层    │
│                 │    │                 │    │                 │
│  • CSV数据集    │───▶│  • MySQL暂存    │───▶│  • Vue.js前端   │
│  • 业务系统     │    │  • Hive数仓     │    │  • Flask API    │
│  • 外部数据     │    │  • 数据清洗     │    │  • ECharts图表  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🛠️ 技术栈

**后端技术**
- **Python 3.8+** - 核心开发语言
- **Flask** - Web框架和API服务
- **Pandas** - 数据处理和分析
- **MySQL** - 关系型数据库
- **Apache Hive** - 数据仓库

**前端技术**
- **Vue.js 3** - 前端框架
- **ECharts 5** - 数据可视化
- **CSS3** - 样式设计
- **JavaScript ES6+** - 交互逻辑

**大数据平台**
- **Hadoop** - 分布式存储和计算
- **Hive** - 数据仓库和SQL查询
- **阿里云大数据平台** - 云端部署方案

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 14+ (可选，用于前端开发)
- MySQL 5.7+
- Hadoop 3.x + Hive 3.x (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/shared-powerbank-warehouse.git
cd shared-powerbank-warehouse
```

2. **安装Python依赖**
```bash
pip install -r requirements.txt
```

3. **配置数据库**
```bash
# 导入示例数据到MySQL
python scripts/import_data.py
```

4. **启动后端服务**
```bash
cd backend
python run.py
```

5. **访问前端界面**
```bash
# 直接打开浏览器访问
open frontend/index.html
# 或使用本地服务器
cd frontend && python -m http.server 8080
```

## 📊 数据可视化展示

### 🌹 品牌市场份额分析
![品牌分析](docs/img/玫瑰图.png)

采用玫瑰图展示各品牌的市场占有率，直观反映品牌竞争格局。

### 👥 用户行为热力分析  
![用户行为分析](docs/img/用户行为分析.png)

通过热力图分析用户在不同时间段的使用模式，为运营策略提供数据支撑。

### 📈 时间趋势分析
![时间趋势](docs/img/时间趋势.png)

展示收入随时间变化的趋势，帮助识别业务增长模式和季节性特征。

### 🏆 地区收入排行榜
![排行榜](docs/img/排行榜.png)

漏斗图形式展示TOP10地区收入排名，清晰展现地区业务表现差异。

### 🗺️ 全国热力分布图
![热力分布](docs/img/热力分布.png)

基于中国地图的省份收入热力分布，为区域扩张决策提供地理维度洞察。

## 📁 项目结构

```
shared-powerbank-warehouse/
├── 📁 backend/                 # 后端API服务
│   ├── app.py                 # Flask应用主文件
│   ├── run.py                 # 启动脚本
│   └── __pycache__/           # Python缓存
├── 📁 data/                   # 数据文件
│   ├── AWS/                   # 处理后的数据
│   ├── cleaned_data/          # 清洗后的数据
│   ├── order.csv              # 订单数据
│   ├── region.csv             # 地区数据
│   ├── time.csv               # 时间数据
│   └── user.csv               # 用户数据
├── 📁 docs/                   # 项目文档
│   ├── img/                   # 截图和图片
│   ├── Hive操作完整记录.md     # Hive操作文档
│   └── MySQL数据导入记录.md    # MySQL导入文档
├── 📁 frontend/               # 前端界面
│   ├── index.html             # 主页面
│   ├── js/app.js              # Vue应用逻辑
│   └── styles/                # CSS样式文件
├── 📁 Hive-record/            # Hive操作记录
├── 📁 scripts/                # 数据处理脚本
├── requirements.txt           # Python依赖
├── 共享充电宝数据仓库需求文档.md # 需求文档
└── README.md                  # 项目说明
```

## 🔧 核心功能模块

### 1. 数据采集与集成
- **MySQL数据导入**：支持CSV批量导入
- **Hive数据抽取**：从MySQL到Hive的ETL流程
- **数据清洗**：处理缺失值、异常值和数据格式

### 2. 数据分析引擎
- **品牌分析**：市场份额、收入贡献度
- **用户行为**：使用模式、时间分布热力图
- **地区分析**：收入排行、地理分布热力图
- **时间趋势**：收入变化趋势、周期性分析

### 3. 可视化展示
- **交互式图表**：基于ECharts的丰富图表类型
- **实时数据**：动态更新的仪表板
- **响应式设计**：适配PC、平板、手机多端
- **主题定制**：现代化的视觉设计

## 🌟 特色亮点

- **🎨 现代化UI设计**：采用渐变色彩和毛玻璃效果
- **📱 响应式布局**：完美适配各种屏幕尺寸
- **⚡ 高性能渲染**：优化的图表渲染和数据加载
- **🔄 实时数据更新**：支持数据的实时刷新
- **🗺️ 地理可视化**：集成中国地图的热力分布
- **📊 多样化图表**：柱状图、饼图、热力图、漏斗图等

## 📈 数据指标

当前数据规模：
- **地区数据**：3,179个地区
- **用户数据**：10,000+用户记录  
- **订单数据**：100,000+交易记录
- **时间维度**：覆盖完整年度数据
- **品牌维度**：主流共享充电宝品牌

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## � 部署指南

### 本地开发环境

1. **启动后端API服务**
```bash
cd backend
python run.py
# 服务将在 http://localhost:5000 启动
```

2. **启动前端服务**
```bash
cd frontend
# 方式1：直接打开HTML文件
open index.html

# 方式2：使用Python内置服务器
python -m http.server 8080
# 访问 http://localhost:8080
```

### 生产环境部署

#### 使用Docker部署
```bash
# 构建镜像
docker build -t powerbank-warehouse .

# 运行容器
docker run -p 5000:5000 -p 8080:8080 powerbank-warehouse
```

#### 使用Nginx部署
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        root /path/to/frontend;
        index index.html;
    }

    location /api {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔍 API文档

### 数据接口

#### 获取品牌数据
```http
GET /api/brand-data
```

**响应示例：**
```json
{
  "brands": ["小电", "街电", "来电", "怪兽充电"],
  "revenue": [25000, 22000, 18000, 15000],
  "total": {
    "totalBrands": 4,
    "totalRevenue": 80000
  }
}
```

#### 获取用户行为数据
```http
GET /api/user-behavior-data
```

#### 获取时间趋势数据
```http
GET /api/time-data
```

#### 获取地区使用数据
```http
GET /api/region-usage-data
```

#### 获取地区热力图数据
```http
GET /api/region-heatmap-data
```

## 🛠️ 开发指南

### 添加新的图表

1. **后端添加数据接口**
```python
@app.route('/api/new-chart-data')
def get_new_chart_data():
    # 数据处理逻辑
    return jsonify(data)
```

2. **前端添加图表组件**
```javascript
// 在app.js中添加新的图表渲染函数
const renderNewChart = () => {
    // ECharts配置
    const option = {
        // 图表配置
    };
    newChart.setOption(option);
};
```

3. **HTML中添加图表容器**
```html
<div id="newChart" class="chart"></div>
```

### 数据处理脚本

项目提供了多个数据处理脚本：

- `scripts/import_data.py` - 数据导入脚本
- `scripts/clean_data.py` - 数据清洗脚本
- `scripts/generate_sample_data.py` - 示例数据生成

## 🐛 常见问题

### Q: 图表不显示怎么办？
A: 检查以下几点：
1. 确保后端API服务正常运行
2. 检查浏览器控制台是否有错误信息
3. 验证数据文件是否存在且格式正确

### Q: 如何添加新的省份数据？
A: 修改 `data/AWS/region_heatmap_data.csv` 文件，添加对应的省份和收入数据。

### Q: 如何自定义图表样式？
A: 修改 `frontend/js/app.js` 中对应图表的ECharts配置选项。

## 📚 相关文档

- [Hive操作完整记录](docs/Hive操作完整记录.md)
- [MySQL数据导入记录](docs/MySQL数据导入记录.md)
- [项目需求文档](共享充电宝数据仓库需求文档.md)

## 🎯 未来规划

- [ ] 支持实时数据流处理
- [ ] 添加机器学习预测模型
- [ ] 集成更多数据源
- [ ] 移动端APP开发
- [ ] 用户权限管理系统
- [ ] 数据导出功能
- [ ] 多语言支持

## 📞 联系方式

- **项目维护者**：共享充电宝数据仓库团队
- **邮箱**：<EMAIL>
- **项目地址**：https://gitee.com/Aurora_th/Shared-Power-Bank-Data-Warehouse
- **在线演示**：https://demo.powerbank-warehouse.com

## 🙏 致谢

感谢以下开源项目的支持：
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [ECharts](https://echarts.apache.org/) - 强大的数据可视化库
- [Flask](https://flask.palletsprojects.com/) - 轻量级Web框架
- [Pandas](https://pandas.pydata.org/) - 数据分析库

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！

📢 欢迎关注我们的项目动态，获取最新更新！
