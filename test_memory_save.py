#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试长期记忆保存功能
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.getcwd())

def test_memory_save():
    """测试记忆保存功能"""
    print("🧪 测试长期记忆保存功能")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from pathlib import Path
        import json
        from datetime import datetime
        
        # 创建长期记忆实例
        class TestLongTermMemory:
            """测试用长期记忆类"""
            
            def __init__(self, base_path: str = None):
                """初始化长期记忆"""
                self.base_path = Path(base_path) if base_path else Path.cwd() / "powerbank_memory"
                self.base_path.mkdir(exist_ok=True)
                
                self.qa_file = self.base_path / "qa_history.json"
                self.analysis_file = self.base_path / "analysis_history.json"
                
                # 确保文件存在
                self._ensure_files_exist()
                print(f"💾 长期记忆系统初始化完成: {self.base_path}")
            
            def _ensure_files_exist(self):
                """确保记忆文件存在"""
                try:
                    if not self.qa_file.exists():
                        self.qa_file.write_text("[]", encoding='utf-8')
                    if not self.analysis_file.exists():
                        self.analysis_file.write_text("[]", encoding='utf-8')
                except Exception as e:
                    print(f"⚠️ 记忆文件创建失败: {e}")
            
            def save_qa(self, question: str, answer: str, metadata: dict = None) -> bool:
                """保存问答对"""
                try:
                    # 读取现有数据
                    qa_data = []
                    if self.qa_file.exists():
                        try:
                            qa_data = json.loads(self.qa_file.read_text(encoding='utf-8'))
                        except:
                            qa_data = []
                    
                    # 添加新记录
                    new_record = {
                        "question": question,
                        "answer": answer,
                        "timestamp": datetime.now().isoformat(),
                        "metadata": metadata or {}
                    }
                    qa_data.append(new_record)
                    
                    # 保持最近1000条记录
                    if len(qa_data) > 1000:
                        qa_data = qa_data[-1000:]
                    
                    # 保存
                    self.qa_file.write_text(json.dumps(qa_data, ensure_ascii=False, indent=2), encoding='utf-8')
                    return True
                    
                except Exception as e:
                    print(f"❌ QA保存失败: {e}")
                    return False
            
            def get_qa_count(self) -> int:
                """获取QA记录数量"""
                try:
                    if not self.qa_file.exists():
                        return 0
                    qa_data = json.loads(self.qa_file.read_text(encoding='utf-8'))
                    return len(qa_data)
                except:
                    return 0
        
        # 创建测试实例
        memory = TestLongTermMemory()
        
        print(f"📊 当前QA记录数: {memory.get_qa_count()}")
        
        # 测试保存功能
        test_cases = [
            {
                "question": "各品牌市场份额如何？",
                "answer": "根据数据分析，搜电以20.3%的份额领先市场，街电19.2%排名第二...",
                "metadata": {
                    "query_type": "data_query",
                    "processing_time": 2.5,
                    "sql_query": "SELECT 品牌名称, 市场份额 FROM brand_revenue_summary",
                    "data_count": 6,
                    "has_visualization": True,
                    "success": True
                }
            },
            {
                "question": "用户年龄分布情况？",
                "answer": "用户主要集中在26-35岁年龄段，占比最高，其次是18-25岁年龄段...",
                "metadata": {
                    "query_type": "data_query",
                    "processing_time": 1.8,
                    "sql_query": "SELECT 年龄段, 用户数量 FROM user_behavior_summary",
                    "data_count": 5,
                    "has_visualization": True,
                    "success": True
                }
            },
            {
                "question": "你好，请介绍一下这个系统",
                "answer": "您好！我是共享充电宝智能数据分析系统，可以帮您分析业务数据...",
                "metadata": {
                    "query_type": "general_chat",
                    "processing_time": 0.5,
                    "success": True
                }
            }
        ]
        
        print("\n🔄 开始测试保存功能...")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}: {test_case['question'][:30]}...")
            
            success = memory.save_qa(
                test_case["question"],
                test_case["answer"],
                test_case["metadata"]
            )
            
            if success:
                print(f"  ✅ 保存成功")
            else:
                print(f"  ❌ 保存失败")
            
            time.sleep(0.5)  # 间隔0.5秒
        
        # 验证保存结果
        print(f"\n📊 保存后QA记录数: {memory.get_qa_count()}")
        
        # 检查文件内容
        if memory.qa_file.exists():
            file_size = memory.qa_file.stat().st_size
            print(f"📁 QA文件大小: {file_size} 字节")
            
            # 读取并显示最新记录
            try:
                qa_data = json.loads(memory.qa_file.read_text(encoding='utf-8'))
                if qa_data:
                    latest_record = qa_data[-1]
                    print(f"📋 最新记录:")
                    print(f"  问题: {latest_record['question'][:50]}...")
                    print(f"  时间: {latest_record['timestamp']}")
                    print(f"  类型: {latest_record['metadata'].get('query_type', '未知')}")
            except Exception as e:
                print(f"⚠️ 读取记录失败: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 长期记忆保存测试完成！")
        print(f"📁 文件位置: {memory.qa_file}")
        print("💡 现在你可以检查JSON文件，应该包含测试数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_current_files():
    """检查当前文件状态"""
    print("\n🔍 检查当前记忆文件状态")
    print("-" * 30)
    
    memory_dir = Path("powerbank_memory")
    qa_file = memory_dir / "qa_history.json"
    analysis_file = memory_dir / "analysis_history.json"
    
    for file_path in [qa_file, analysis_file]:
        if file_path.exists():
            file_size = file_path.stat().st_size
            print(f"📁 {file_path.name}: {file_size} 字节")
            
            try:
                content = json.loads(file_path.read_text(encoding='utf-8'))
                print(f"  📊 记录数: {len(content)}")
                if content:
                    print(f"  📅 最新记录时间: {content[-1].get('timestamp', '未知')}")
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
        else:
            print(f"❌ {file_path.name}: 文件不存在")

def main():
    """主函数"""
    print("🚀 长期记忆保存功能测试")
    
    # 检查当前状态
    check_current_files()
    
    # 运行测试
    success = test_memory_save()
    
    # 再次检查状态
    check_current_files()
    
    if success:
        print("\n✅ 测试成功！长期记忆保存功能正常工作")
        print("💡 建议：重新运行你的智能体系统，现在应该能正常保存记忆了")
    else:
        print("\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
