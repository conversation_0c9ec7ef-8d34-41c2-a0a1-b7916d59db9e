{"cells": [{"cell_type": "code", "execution_count": 4, "id": "736c51a3", "metadata": {}, "outputs": [], "source": ["from rich.console import Console\n", "console = Console()\n", "def custom_print(info):\n", "    console.print(info)"]}, {"cell_type": "markdown", "id": "4aa08214", "metadata": {}, "source": ["# 1.智能数据分析智能体"]}, {"cell_type": "markdown", "id": "0076bc3f", "metadata": {}, "source": ["## 1.1 导入相关依赖"]}, {"cell_type": "markdown", "id": "99e31742", "metadata": {}, "source": ["小贴士：如果导入依赖，显示依赖不存在，则需要使用 pip 命令进行安装"]}, {"cell_type": "code", "execution_count": 5, "id": "337167d9", "metadata": {}, "outputs": [], "source": ["import os\n", "import io\n", "import json\n", "import inspect\n", "import numpy as np\n", "import pandas as pd\n", "import pymysql\n", "import openai\n", "import inspect\n", "import tiktoken\n", "from openai import OpenAI\n", "from typing import List, Dict, Optional\n", "from dashscope import get_tokenizer"]}, {"cell_type": "markdown", "id": "a0a9fd70", "metadata": {}, "source": ["## 1.2 创建工具应用函数"]}, {"cell_type": "code", "execution_count": 6, "id": "17f2b755", "metadata": {}, "outputs": [], "source": ["# 创建HTTP客户端时不使用proxies参数\n", "import httpx\n", "http_client = httpx.Client(follow_redirects=True)\n", "api_key = \"sk-5617b89342844050a48268b477327b44\"  # 注意保护你的API密钥\n", "base_url = \"https://api.deepseek.com/v1\"\n", "\n", "# 使用自定义HTTP客户端创建OpenAI实例\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "ecb3d777", "metadata": {}, "outputs": [], "source": ["def auto_call(client,messages,tools=None,model='deepseek-chat'):\n", "    if tools == None :\n", "        response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages\n", "        )\n", "        return response.choices[0].message.content\n", "    else :\n", "        #函数第一次调用\n", "        first_response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages,\n", "            tools = tools,\n", "            tool_choice=\"auto\",\n", "        )\n", "        message_call = first_response.choices[0].message\n", "        tools_calls = message_call.tool_calls\n", "        if tools_calls != None:\n", "            tool_call_id = tools_calls[0].id\n", "            func_name = first_response.choices[0].message.tool_calls[0].function.name\n", "            func_args = json.loads(tools_calls[0].function.arguments)['sql']\n", "            func_result = tools_list[func_name](func_args)\n", "            messages.append(message_call)\n", "            messages.append(\n", "                {\n", "                    \"role\":\"tool\",\n", "                    \"tool_call_id\":tool_call_id,\n", "                    \"name\":func_name,\n", "                    \"content\":func_result\n", "                }\n", "            )\n", "            #二次调用\n", "            second_response = client.chat.completions.create(\n", "                model=model,\n", "                messages=messages\n", "            )\n", "            return second_response.choices[0].message.content\n", "        else:\n", "            return first_response.choices[0].message.content"]}, {"cell_type": "markdown", "id": "c0e4eab0", "metadata": {}, "source": ["## 1.3 创建数据库查询工具"]}, {"cell_type": "code", "execution_count": 8, "id": "76b021ca", "metadata": {}, "outputs": [], "source": ["def get_user_info(sql,username='root',password='123456',db='user_info'):\n", "    \"\"\"\n", "    当前函数，用于查询指定数据库下的相关表中的数据\n", "    ：param sql：参数为必要参数,字符串类型的SQL语句,\n", "    ：param username：参数为可选参数,代表:用户名,字符串类型,\n", "    ：param password：参数为可选参数,代表:用户密码,字符串类型,\n", "    ：param db：参数为可选参数,代表:需要连接的数据库名字,字符串类型,\n", "    ：return:当前SQL语句执行完的查询结果\n", "    \"\"\"\n", "    conn = pymysql.connect(\n", "        host = 'localhost',\n", "        user = username,\n", "        password = password,\n", "        db = db,\n", "        charset = 'utf8'\n", "    )\n", "    try: \n", "        with conn.cursor() as cursor:\n", "            cursor.execute(sql)\n", "            results = cursor.fetchall()\n", "    finally:\n", "        cursor.close()\n", "        conn.close()\n", "    column_names = [desc[0] for desc in cursor.description]\n", "    df = pd.DataFrame(results, columns = column_names)\n", "    return df.to_json(orient = 'records')"]}, {"cell_type": "markdown", "id": "06f01e82", "metadata": {}, "source": ["## 1.5 创建 tools"]}, {"cell_type": "code", "execution_count": 9, "id": "fe85b895", "metadata": {}, "outputs": [], "source": ["tools_list = {\"get_user_info\": get_user_info}\n", "\n", "tools = [{'type': 'function',\n", "         'function': {\n", "            'name': 'get_user_info',\n", "            'description': '当前函数，用于查询指定数据库下的相关表中的数据。输入参数为字符串类型的SQL语句，输出为当前SQL语句执行完的查询结果',\n", "            'parameters': {'type': 'object',\n", "                           'properties': {'sql': {'description': '必要参数，字符串类型的SQL语句','type': 'string'},\n", "                                          'username': {'description': '用户名,字符串类型','type': 'string'},\n", "                                          'password': {'description': '用户密码,字符串类型','type': 'string'},\n", "                                          'db': {'description': '需要连接的数据库名字,字符串类型','type': 'string'}\n", "                                         },\n", "         'required': ['sql']},\n", "        }}]"]}, {"cell_type": "markdown", "id": "0b0f8341", "metadata": {}, "source": ["## 1.6 加载外部知识"]}, {"cell_type": "code", "execution_count": 10, "id": "9ecd37b9", "metadata": {}, "outputs": [{"data": {"text/plain": ["'# 表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch 字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare 字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked 字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量，0 表示乘客没有在泰坦尼克号事故中幸存，1 表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” 等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理性别，只有 “male”（男性）和 “female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” 的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n```\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp 类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(r'C:\\\\Users\\\\<USER>\\\\Desktop\\\\data.md', 'r', encoding='utf-8') as f:\n", "    system_info = f.read()\n", "system_info "]}, {"cell_type": "markdown", "id": "08e80326", "metadata": {}, "source": ["## 1.7 短期记忆（基于数量）"]}, {"cell_type": "code", "execution_count": 11, "id": "6da74fc1", "metadata": {}, "outputs": [], "source": ["class ShortMemoryBaseCount:\n", "    def __init__(self, \n", "                 messages=None,\n", "                 count_threshold=0,\n", "                 counts=0,\n", "                 model='deepseek-chat'):\n", "        if messages is None:\n", "            self.messages = []\n", "        else:\n", "            self.messages = messages\n", "        self.count_threshold = count_threshold\n", "        self.counts = counts\n", "        self.model = model\n", "    def _based_on_count(self):\n", "        if self.counts > self.count_threshold:\n", "            if type(self.messages[1]) is not dict and \"tool_call_id\" in self.messages[2]:\n", "                del self.messages[1]\n", "                del self.messages[1]\n", "                diff = self.counts - self.count_threshold\n", "                self.counts -= diff\n", "                del self.messages[1:(1 + diff)]\n", "            else:\n", "                diff = self.counts - self.count_threshold\n", "                self.counts -= diff\n", "                del self.messages[1:(1 + diff)]\n", "    def append_message(self, message:dict):\n", "        self.counts += 1\n", "        self.messages.append(message)\n", "        self._based_on_count()\n", "    def test_append_message(self, message:dict):\n", "        self.messages.append(message)\n", "        self._based_on_count()\n", "    def get_messages(self):\n", "        return self.messages"]}, {"cell_type": "markdown", "id": "72426353", "metadata": {}, "source": ["## 1.8 长期记忆"]}, {"cell_type": "code", "execution_count": 12, "id": "d3419198", "metadata": {}, "outputs": [], "source": ["class LongTermMemory:\n", "    def __init__(self):\n", "        self.encoder = get_tokenizer('qwen-turbo')\n", "    \n", "    def create_folder(self, folder_path: str) -> None:\n", "        \"\"\"创建文件夹（自动处理路径存在情况）\"\"\"\n", "        try:\n", "            os.makedirs(folder_path, exist_ok=True)\n", "        except PermissionError as e:\n", "            print(f\"Permission denied creating folder: {e}\")\n", "        except OSError as e:\n", "            print(f\"OS error creating folder: {e}\")\n", "    \n", "    def create_file(self, file_path: str) -> bool:\n", "        \"\"\"创建空文件（返回是否成功）\"\"\"\n", "        try:\n", "            folder = os.path.dirname(file_path)\n", "            if folder:\n", "                self.create_folder(folder)\n", "            open(file_path, 'a', encoding='utf-8').close()\n", "            return True\n", "        except IOError as e:\n", "            print(f\"Error creating file: {e}\")\n", "            return False\n", "    \n", "    def append_qa(self, file_path: str, question: str, answer: str) -> bool:\n", "        \"\"\"安全追加QA对（自动创建缺失路径）\"\"\"\n", "        try:\n", "            qa = {\"Q\": question, \"A\": answer}\n", "            dir_path = os.path.dirname(file_path)\n", "            if dir_path:\n", "                self.create_folder(dir_path)\n", "            with open(file_path, 'a+', encoding='utf-8') as f:\n", "                f.write(json.dumps(qa) + \"\\n\")\n", "            return True\n", "        except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, json.JSONDecodeError) as e:\n", "            print(f\"Error appending QA: {e}\")\n", "            return False\n", "    \n", "    def read_all_qa(self, file_path: str) -> List[Dict]:\n", "        \"\"\"安全读取全部QA（处理文件不存在情况）\"\"\"\n", "        try:\n", "            if not os.path.exists(file_path):\n", "                return []\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                return [json.loads(line) for line in f if line.strip()]\n", "        except (FileNot<PERSON><PERSON>nd<PERSON><PERSON>r, json.JSONDecodeError) as e:\n", "            print(f\"Error reading QA: {e}\")\n", "            return []\n", "    \n", "    def read_recent_qa(self, file_path: str, k: int) -> List[Dict]:\n", "        \"\"\"安全读取最近k个QA（自动处理无效k值）\"\"\"\n", "        qa_list = self.read_all_qa(file_path)\n", "        try:\n", "            return qa_list[-abs(k):] if qa_list else []\n", "        except TypeError:\n", "            return []\n", "    \n", "    def get_qa_count(self, file_path: str) -> int:\n", "        \"\"\"安全获取QA数量\"\"\"\n", "        return len(self.read_all_qa(file_path))\n", "    \n", "    def get_token_count(self, file_path: str) -> int:\n", "        \"\"\"安全获取Token数量（处理读取错误）\"\"\"\n", "        try:\n", "            if not os.path.exists(file_path):\n", "                return 0\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                return len(self.encoder.encode(f.read()))\n", "        except (<PERSON><PERSON><PERSON><PERSON>, PermissionError) as e:\n", "            print(f\"Error counting tokens: {e}\")\n", "            return 0"]}, {"cell_type": "markdown", "id": "c6ce3efd", "metadata": {}, "source": ["## 1.9 创建多轮对话函数"]}, {"cell_type": "code", "execution_count": 13, "id": "ed44b24f", "metadata": {}, "outputs": [], "source": ["def auto_chat(client,\n", "              user_input,\n", "              system_base_info=\"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\",\n", "              system_extend_info=\"\",\n", "              tools = None,\n", "              model='deepseek-chat'):\n", "    system_message = {\"role\":\"system\",\"content\":system_base_info + \"%s\" %system_extend_info}\n", "    user_message={\"role\":\"user\",\"content\":user_input}\n", "    global short_memory\n", "    long_memory = LongTermMemory()\n", "    short_memory = ShortMemoryBaseCount(count_threshold=20)\n", "    short_memory.append_message(system_message)\n", "    short_memory.append_message(user_message)\n", "    while True:           \n", "        print(f\"用户输入的问题是: {user_input}\")\n", "        result = auto_call(client,short_memory.get_messages(),tools=tools,model=model)\n", "        print(f\"大语言模型返回结果: {result}\")\n", "        while True:\n", "            flag = input('输入[1]则保存到长期记忆，输入[2]则重新输入问题让大模型回答')\n", "            if flag == '1':\n", "                long_memory.append_qa(  r'C:\\\\Users\\\\<USER>\\\\Desktop\\\\long_memoty.txt' , \n", "                                        question='Q:' + user_input, \n", "                                        answer='A:' + result)\n", "                short_memory.append_message({\"role\": \"assistant\", \"content\": result})\n", "                break\n", "            else:\n", "                print('    您对当前的答案不满意，请重新输入问题：')\n", "                user_input = input('    重新输入您的问题：')\n", "                messages = short_memory.get_messages()\n", "                messages[-1][\"content\"] = user_input\n", "                result = auto_call(client,short_memory.get_messages(),tools=tools,model=model)\n", "                print(f\"模型回答: {result}\")\n", "        user_input = input(\"您还有其他问题？(输入0结束对话): \")\n", "        short_memory.append_message({\"role\":\"user\",\"content\":user_input})\n", "        if user_input == \"0\":\n", "            break"]}, {"cell_type": "markdown", "id": "962751ee", "metadata": {}, "source": ["测试多轮对话函数"]}, {"cell_type": "code", "execution_count": 13, "id": "bb905cb4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["用户输入的问题是: 你好呀\n", "大语言模型返回结果: 你好！很高兴见到你！有什么关于数据分析的问题可以帮您解答吗？\n"]}], "source": ["user_input = \"你好呀\"\n", "auto_chat(client,user_input=user_input,system_extend_info=\"\",tools=tools)"]}, {"cell_type": "code", "execution_count": 14, "id": "abf506cb", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Q': 'Q:你好呀', 'A': 'A:你好！很高兴见到你！有什么关于数据分析的问题可以帮您解答吗？'}]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["LongTermMemory().read_all_qa(r'C:\\\\Users\\\\<USER>\\\\Desktop\\\\long_memoty.txt')"]}, {"cell_type": "markdown", "id": "cdf005f8", "metadata": {}, "source": ["# 2.代码解释器智能体"]}, {"cell_type": "markdown", "id": "5720fbc0", "metadata": {}, "source": ["## 2.1 创建表转本地变量工具"]}, {"cell_type": "markdown", "id": "0cb262a7", "metadata": {}, "source": ["创建外部工具函数，通过SQL查询结果，转换成DataFrame数据类型的全局变量"]}, {"cell_type": "markdown", "id": "337e0e4f", "metadata": {}, "source": ["测试查看：1.1"]}, {"cell_type": "code", "execution_count": null, "id": "1758b36a", "metadata": {}, "outputs": [], "source": ["def dataset_to_df(sql,python_df,host='localhost',user='root',password='123456',db='user_info',charset='utf8'):\n", "    \"\"\"\n", "    当前函数，用于将MySQL数据库(db)中的表转换成Python本地环境变量，Pandas中的DataFrame格式。\n", "    :param sql：参数为必要参数,代表：SQL查询语句，用于提取MySQL中user_info数据库中的某张表，字符串类型。\n", "    :param python_df: 参数为必选参数,代表:将db数据库下某张表转换为Python本地环境变量的名字,字符串类型。\n", "    :param host: 参数为可选参数,代表:主机地址,字符串类型。\n", "    :param user: 参数为可选参数,代表:用户名,字符串类型。\n", "    :param password: 参数为可选参数,代表:密码,字符串类型。\n", "    :param db：参数为可选参数,代表:需要连接的数据库名字,字符串类型。\n", "    :param charset,参数为可选参数,代表:默认字符集,字符串类型。\n", "    :return：无返回值\n", "    \"\"\"\n", "    connection = pymysql.connect(\n", "            host=host,\n", "            user=user,\n", "            password=password,\n", "            db=db,\n", "            charset=charset\n", "        )\n", "    globals()[python_df] = pd.read_sql(sql, connection)"]}, {"cell_type": "code", "execution_count": 15, "id": "25d4161b", "metadata": {}, "outputs": [], "source": ["sql = \"SELECT * FROM user_base_info LIMIT 10\""]}, {"cell_type": "code", "execution_count": 16, "id": "6739a052", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON><PERSON>, Mr. <PERSON> </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">Harris\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"male\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">22</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"1\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Cumings, Mrs. John Bradley </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">(<PERSON>)\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"female\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"1\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Heikkinen, </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">Miss. Laina\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"female\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Futrelle, Mrs. Jacques </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\"><PERSON> (<PERSON>)\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"female\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">35</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"1\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Allen, Mr. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\"><PERSON>\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"male\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">35</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Moran, Mr. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">James\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"male\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"McCarthy, Mr. Timothy </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">J\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"male\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">54</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Palsson, Master. Gosta </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">Leonard\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"male\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"3\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"1\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Johnson, Mrs. Oscar W (Elisabeth </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">V<PERSON><PERSON><PERSON>)\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"female\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"2\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Nasser, Mrs. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\"><PERSON> (<PERSON>)\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"female\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"1\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m1\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON>, Mr. <PERSON> \u001b[0m\n", "\u001b[32m<PERSON><PERSON><PERSON>\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"male\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m22\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"1\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m2\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> \u001b[0m\n", "\u001b[32m(\u001b[0m\u001b[32mFlorence <PERSON>\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"female\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m38\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"1\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m3\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON><PERSON><PERSON>, \u001b[0m\n", "\u001b[32mMiss. <PERSON><PERSON>\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"female\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m26\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"0\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m4\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON><PERSON>, Mrs. <PERSON> \u001b[0m\n", "\u001b[32mHeath \u001b[0m\u001b[32m(\u001b[0m\u001b[32m<PERSON><PERSON> <PERSON>\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"female\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m35\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"1\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m5\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON>, Mr. \u001b[0m\n", "\u001b[32m<PERSON><PERSON><PERSON>\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"male\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m35\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"0\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m6\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON>, Mr. \u001b[0m\n", "\u001b[32mJames\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"male\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m30\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"0\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m7\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON>, Mr. <PERSON> \u001b[0m\n", "\u001b[32mJ\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"male\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m54\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"0\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m8\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON>, <PERSON>. <PERSON> \u001b[0m\n", "\u001b[32m<PERSON><PERSON>ard\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"male\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m2\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"3\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"1\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m9\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON>, Mrs. <PERSON> \u001b[0m\u001b[32m(\u001b[0m\u001b[32mElisabeth \u001b[0m\n", "\u001b[32m<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"female\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m27\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"0\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"2\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m10\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON>, Mrs. \u001b[0m\n", "\u001b[32m<PERSON><PERSON><PERSON><PERSON> \u001b[0m\u001b[32m(\u001b[0m\u001b[32m<PERSON><PERSON><PERSON>\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"female\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m14\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"1\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(get_user_info(sql))"]}, {"cell_type": "code", "execution_count": 17, "id": "c3df5dc6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_7160\\3272147142.py:20: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  globals()[python_df] = pd.read_sql(sql, connection)\n"]}], "source": ["dataset_to_df(sql,'user_df')"]}, {"cell_type": "code", "execution_count": 18, "id": "fac25c57", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>passenger_id</th>\n", "      <th>name</th>\n", "      <th>sex</th>\n", "      <th>age</th>\n", "      <th>sib_sp</th>\n", "      <th>parch</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...</td>\n", "      <td>female</td>\n", "      <td>38</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>35</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>54</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   passenger_id                                               name     sex  \\\n", "0             1                            <PERSON><PERSON>, Mr. <PERSON>    male   \n", "1             2  <PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...  female   \n", "2             3                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female   \n", "3             4       <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "4             5                           <PERSON>, Mr. <PERSON>    male   \n", "5             6                                   <PERSON>, Mr. <PERSON>    male   \n", "6             7                            <PERSON>, Mr. <PERSON>    male   \n", "7             8                     <PERSON><PERSON>, <PERSON><PERSON><PERSON>    male   \n", "8             9  <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "9            10                <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "\n", "   age sib_sp parch  \n", "0   22      1     0  \n", "1   38      1     0  \n", "2   26      0     0  \n", "3   35      1     0  \n", "4   35      0     0  \n", "5   30      0     0  \n", "6   54      0     0  \n", "7    2      3     1  \n", "8   27      0     2  \n", "9   14      1     0  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["user_df"]}, {"cell_type": "code", "execution_count": 19, "id": "3a4e7429", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 10 entries, 0 to 9\n", "Data columns (total 6 columns):\n", " #   Column        Non-Null Count  Dtype \n", "---  ------        --------------  ----- \n", " 0   passenger_id  10 non-null     int64 \n", " 1   name          10 non-null     object\n", " 2   sex           10 non-null     object\n", " 3   age           10 non-null     int64 \n", " 4   sib_sp        10 non-null     object\n", " 5   parch         10 non-null     object\n", "dtypes: int64(2), object(4)\n", "memory usage: 612.0+ bytes\n"]}], "source": ["user_df.info()"]}, {"cell_type": "markdown", "id": "d312fdd3", "metadata": {}, "source": ["## 2.2 配置tools"]}, {"cell_type": "code", "execution_count": 20, "id": "4999f3cc", "metadata": {}, "outputs": [], "source": ["tools_list = {\"dataset_to_df\":dataset_to_df}\n", "tools = [{'type': 'function',\n", "         'function':{\n", "            'name': 'dataset_to_df',\n", "            'description': '当前函数，用于将MySQL数据库(db)中的表转换成Python本地环境变量，Pandas中的DataFrame格式',\n", "            'parameters': {'type': 'object',\n", "                           'properties': {'sql':{'description': '必选参数，字符串类型，'\n", "                                                 '代表：SQL查询语句，用于提取MySQL中user_info数据库中的某张表','type': 'string'},\n", "                                          'python_df':{'description': '必选参数，字符串类型'\n", "                                                       '代表:将db数据库下某张表转换为Python本地环境变量的名字','type': 'string'},\n", "                                          'host':{'description': '可选参数，字符串类型'\n", "                                                       '代表:主机地址','type': 'string'},\n", "                                          'user':{'description': '可选参数，字符串类型'\n", "                                                       '代表:用户名','type': 'string'},\n", "                                          'password':{'description': '可选参数，字符串类型'\n", "                                                       '代表:密码','type': 'string'},\n", "                                          'db':{'description': '可选参数，字符串类型'\n", "                                                       '代表:需要连接的数据库名字','type': 'string'},\n", "                                          'charset':{'description': '可选参数，字符串类型'\n", "                                                       '代表:默认字符集','type': 'string'}             \n", "                                         },\n", "         'required': ['sql', 'python_df']}\n", "        }}]"]}, {"cell_type": "markdown", "id": "e1bd0661", "metadata": {}, "source": ["## 2.3 测试工具是否可用"]}, {"cell_type": "code", "execution_count": 21, "id": "2ba2cf5b", "metadata": {}, "outputs": [], "source": ["from rich.console import Console\n", "\n", "console = Console()\n", "\n", "def custom_print(info):\n", "    console.print(info)"]}, {"cell_type": "code", "execution_count": 22, "id": "191da126", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_47658514-e604-4f11-ab01-e37f5f4ebd9e'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT * FROM user_base_info\",\"python_df\":\"user_base_info\",\"db\":\"user_info\"}'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'dataset_to_df'</span>\n", "            <span style=\"font-weight: bold\">)</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">]</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "        \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mid\u001b[0m=\u001b[32m'call_0_47658514-e604-4f11-ab01-e37f5f4ebd9e'\u001b[0m,\n", "            \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT * FROM user_base_info\",\"python_df\":\"user_base_info\",\"db\":\"user_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                \u001b[33mname\u001b[0m=\u001b[32m'dataset_to_df'\u001b[0m\n", "            \u001b[1m)\u001b[0m,\n", "            \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "            \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m]\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "        model='deepseek-chat',\n", "        messages=[\n", "            {\"role\":\"user\", \"content\":\"把user_info数据库中的user_base_info转换为Python本地变量\"}\n", "        ],\n", "        tools=tools,\n", "        tool_choice=\"auto\",\n", "    )\n", "custom_print(response.choices[0].message)"]}, {"cell_type": "markdown", "id": "21e9f14d", "metadata": {}, "source": ["## 2.4 创建Python代码解释器"]}, {"cell_type": "markdown", "id": "75019205", "metadata": {}, "source": ["当前函数的功能，输入Python代码，执行后，生成全局变量。"]}, {"cell_type": "markdown", "id": "349a5f0f", "metadata": {}, "source": ["测试，查看1.2"]}, {"cell_type": "code", "execution_count": 23, "id": "c22e8ce8", "metadata": {}, "outputs": [], "source": ["def python_interpreter(code):\n", "    \"\"\"\n", "    当前函数，用于执行code代码，将指定数据库中的数据表进行数据处理（数据处理不限于：数据查询、数据探索、数据清洗、数据分析、数据可视化等）\n", "    :param code: 参数为必选参数,代表:需要执行的python代码（当前代码对DataFrame进行各种数据操作）。\n", "    :return:返回当前代码执行完的结果\n", "    \"\"\"\n", "    before = set(globals().keys())\n", "    try:\n", "        exec(code, globals())\n", "    except Exception as exception:\n", "        return str(exception)\n", "    after = set(globals().keys())\n", "    diff_vars = after - before\n", "    if diff_vars:\n", "        return {var: globals()[var] for var in diff_vars}"]}, {"cell_type": "markdown", "id": "12fc3785", "metadata": {}, "source": ["## 2.5 配置tools"]}, {"cell_type": "code", "execution_count": null, "id": "f64613b7", "metadata": {}, "outputs": [], "source": ["tools_list = {\"python_inter\":python_interpreter}\n", "tools = [{'type': 'function',\n", "         'function':{\n", "            'name': 'python_inter',\n", "            'description': '当前函数，用于执行code代码，将指定数据库中的数据表进行数据处理（数据处理不限于：数据查询、数据探索、数据清洗、数据分析、数据可视化等）',\n", "            'parameters': {'type': 'object',\n", "                           'properties': {'py_code':{'description': '必要参数，字符串类型，'\n", "                                                      '代表:需要执行的python代码（当前代码对DataFrame进行各种数据操作）','type': 'string'}\n", "                                         },\n", "         'required': ['code']}\n", "        }}]"]}, {"cell_type": "markdown", "id": "ec606548", "metadata": {}, "source": ["## 2.6 测试工具是否可用"]}, {"cell_type": "markdown", "id": "ab84854d", "metadata": {}, "source": ["测试：是否能正确读取本地环境变量"]}, {"cell_type": "code", "execution_count": 25, "id": "7d5d6f9d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>passenger_id</th>\n", "      <th>name</th>\n", "      <th>sex</th>\n", "      <th>age</th>\n", "      <th>sib_sp</th>\n", "      <th>parch</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...</td>\n", "      <td>female</td>\n", "      <td>38</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>35</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>54</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   passenger_id                                               name     sex  \\\n", "0             1                            <PERSON><PERSON>, Mr. <PERSON>    male   \n", "1             2  <PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...  female   \n", "2             3                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female   \n", "3             4       <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "4             5                           <PERSON>, Mr. <PERSON>    male   \n", "5             6                                   <PERSON>, Mr. <PERSON>    male   \n", "6             7                            <PERSON>, Mr. <PERSON>    male   \n", "7             8                     <PERSON><PERSON>, <PERSON><PERSON><PERSON>    male   \n", "8             9  <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "9            10                <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "\n", "   age sib_sp parch  \n", "0   22      1     0  \n", "1   38      1     0  \n", "2   26      0     0  \n", "3   35      1     0  \n", "4   35      0     0  \n", "5   30      0     0  \n", "6   54      0     0  \n", "7    2      3     1  \n", "8   27      0     2  \n", "9   14      1     0  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["user_df"]}, {"cell_type": "code", "execution_count": 26, "id": "f7ab0629", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_616bfc03-8a13-47b6-a088-d197716a683d'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"py_code\":\"print(user_df.head(5))\"}'</span>, <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'python_inter'</span><span style=\"font-weight: bold\">)</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">]</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "        \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mid\u001b[0m=\u001b[32m'call_0_616bfc03-8a13-47b6-a088-d197716a683d'\u001b[0m,\n", "            \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"py_code\":\"print\u001b[0m\u001b[32m(\u001b[0m\u001b[32muser_df.head\u001b[0m\u001b[32m(\u001b[0m\u001b[32m5\u001b[0m\u001b[32m)\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m, \u001b[33mname\u001b[0m=\u001b[32m'python_inter'\u001b[0m\u001b[1m)\u001b[0m,\n", "            \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "            \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m]\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "        model='deepseek-chat',\n", "        messages=[\n", "            {\"role\": \"user\", \"content\": \"user_info数据库中user_base_info数据表的数据，保存在pyhton本地环境中的名字为user_df\"},\n", "            {\"role\": \"user\", \"content\": \"现在要求你打印user_df数据集前5条到控制台\"}\n", "        ],\n", "        tools=tools,\n", "        tool_choice=\"auto\",\n", "    )\n", "custom_print(response.choices[0].message)"]}, {"cell_type": "code", "execution_count": 27, "id": "2ecc6b30", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'str'>\n", "<class 'dict'>\n"]}, {"data": {"text/html": ["<style>pre { line-height: 125%; }\n", "td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", ".output_html .hll { background-color: #ffffcc }\n", ".output_html { background: #f8f8f8; }\n", ".output_html .c { color: #3D7B7B; font-style: italic } /* Comment */\n", ".output_html .err { border: 1px solid #FF0000 } /* Error */\n", ".output_html .k { color: #008000; font-weight: bold } /* Keyword */\n", ".output_html .o { color: #666666 } /* Operator */\n", ".output_html .ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */\n", ".output_html .cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */\n", ".output_html .cp { color: #9C6500 } /* Comment.Preproc */\n", ".output_html .cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */\n", ".output_html .c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */\n", ".output_html .cs { color: #3D7B7B; font-style: italic } /* Comment.Special */\n", ".output_html .gd { color: #A00000 } /* Generic.Deleted */\n", ".output_html .ge { font-style: italic } /* Generic.Emph */\n", ".output_html .gr { color: #E40000 } /* Generic.Error */\n", ".output_html .gh { color: #000080; font-weight: bold } /* Generic.Heading */\n", ".output_html .gi { color: #008400 } /* Generic.Inserted */\n", ".output_html .go { color: #717171 } /* Generic.Output */\n", ".output_html .gp { color: #000080; font-weight: bold } /* Generic.Prompt */\n", ".output_html .gs { font-weight: bold } /* Generic.Strong */\n", ".output_html .gu { color: #800080; font-weight: bold } /* Generic.Subheading */\n", ".output_html .gt { color: #0044DD } /* Generic.Traceback */\n", ".output_html .kc { color: #008000; font-weight: bold } /* Keyword.Constant */\n", ".output_html .kd { color: #008000; font-weight: bold } /* Keyword.Declaration */\n", ".output_html .kn { color: #008000; font-weight: bold } /* Keyword.Namespace */\n", ".output_html .kp { color: #008000 } /* Keyword.Pseudo */\n", ".output_html .kr { color: #008000; font-weight: bold } /* Keyword.Reserved */\n", ".output_html .kt { color: #B00040 } /* Keyword.Type */\n", ".output_html .m { color: #666666 } /* Literal.Number */\n", ".output_html .s { color: #BA2121 } /* Literal.String */\n", ".output_html .na { color: #687822 } /* Name.Attribute */\n", ".output_html .nb { color: #008000 } /* Name.Builtin */\n", ".output_html .nc { color: #0000FF; font-weight: bold } /* Name.Class */\n", ".output_html .no { color: #880000 } /* Name.Constant */\n", ".output_html .nd { color: #AA22FF } /* Name.Decorator */\n", ".output_html .ni { color: #717171; font-weight: bold } /* Name.Entity */\n", ".output_html .ne { color: #CB3F38; font-weight: bold } /* Name.Exception */\n", ".output_html .nf { color: #0000FF } /* Name.Function */\n", ".output_html .nl { color: #767600 } /* Name.Label */\n", ".output_html .nn { color: #0000FF; font-weight: bold } /* Name.Namespace */\n", ".output_html .nt { color: #008000; font-weight: bold } /* Name.Tag */\n", ".output_html .nv { color: #19177C } /* Name.Variable */\n", ".output_html .ow { color: #AA22FF; font-weight: bold } /* Operator.Word */\n", ".output_html .w { color: #bbbbbb } /* Text.Whitespace */\n", ".output_html .mb { color: #666666 } /* Literal.Number.Bin */\n", ".output_html .mf { color: #666666 } /* Literal.Number.Float */\n", ".output_html .mh { color: #666666 } /* Literal.Number.Hex */\n", ".output_html .mi { color: #666666 } /* Literal.Number.Integer */\n", ".output_html .mo { color: #666666 } /* Literal.Number.Oct */\n", ".output_html .sa { color: #BA2121 } /* Literal.String.Affix */\n", ".output_html .sb { color: #BA2121 } /* Literal.String.Backtick */\n", ".output_html .sc { color: #BA2121 } /* Literal.String.Char */\n", ".output_html .dl { color: #BA2121 } /* Literal.String.Delimiter */\n", ".output_html .sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */\n", ".output_html .s2 { color: #BA2121 } /* Literal.String.Double */\n", ".output_html .se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */\n", ".output_html .sh { color: #BA2121 } /* Literal.String.Heredoc */\n", ".output_html .si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */\n", ".output_html .sx { color: #008000 } /* Literal.String.Other */\n", ".output_html .sr { color: #A45A77 } /* Literal.String.Regex */\n", ".output_html .s1 { color: #BA2121 } /* Literal.String.Single */\n", ".output_html .ss { color: #19177C } /* Literal.String.Symbol */\n", ".output_html .bp { color: #008000 } /* Name.Builtin.Pseudo */\n", ".output_html .fm { color: #0000FF } /* Name.Function.Magic */\n", ".output_html .vc { color: #19177C } /* Name.Variable.Class */\n", ".output_html .vg { color: #19177C } /* Name.Variable.Global */\n", ".output_html .vi { color: #19177C } /* Name.Variable.Instance */\n", ".output_html .vm { color: #19177C } /* Name.Variable.Magic */\n", ".output_html .il { color: #666666 } /* Literal.Number.Integer.Long */</style><div class=\"highlight\"><pre><span></span>print(user_df.head(5))\n", "</pre></div>\n"], "text/latex": ["\\begin{Verbatim}[commandchars=\\\\\\{\\}]\n", "print(user\\PYZus{}df.head(5))\n", "\\end{Verbatim}\n"], "text/plain": ["print(user_df.head(5))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Code, Markdown\n", "print(type(response.choices[0].message.tool_calls[0].function.arguments))\n", "code = json.loads(response.choices[0].message.tool_calls[0].function.arguments)\n", "print(type(code))\n", "code_str = code.get(\"py_code\")\n", "display(Code(code_str))"]}, {"cell_type": "code", "execution_count": 28, "id": "d956148d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   passenger_id                                               name     sex  \\\n", "0             1                            <PERSON><PERSON>, Mr. <PERSON>    male   \n", "1             2  <PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...  female   \n", "2             3                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female   \n", "3             4       <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "4             5                           <PERSON>, Mr. <PERSON>    male   \n", "\n", "   age sib_sp parch  \n", "0   22      1     0  \n", "1   38      1     0  \n", "2   26      0     0  \n", "3   35      1     0  \n", "4   35      0     0  \n"]}], "source": ["python_interpreter(code_str)"]}, {"cell_type": "markdown", "id": "c1b545d7", "metadata": {}, "source": ["测试：稍微复杂的场景，不是查看数据集前几条数据，而是查看数据集的结构，看看大模型是否能正确处理"]}, {"cell_type": "code", "execution_count": 29, "id": "f5004a10", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>pre { line-height: 125%; }\n", "td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", ".output_html .hll { background-color: #ffffcc }\n", ".output_html { background: #f8f8f8; }\n", ".output_html .c { color: #3D7B7B; font-style: italic } /* Comment */\n", ".output_html .err { border: 1px solid #FF0000 } /* Error */\n", ".output_html .k { color: #008000; font-weight: bold } /* Keyword */\n", ".output_html .o { color: #666666 } /* Operator */\n", ".output_html .ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */\n", ".output_html .cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */\n", ".output_html .cp { color: #9C6500 } /* Comment.Preproc */\n", ".output_html .cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */\n", ".output_html .c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */\n", ".output_html .cs { color: #3D7B7B; font-style: italic } /* Comment.Special */\n", ".output_html .gd { color: #A00000 } /* Generic.Deleted */\n", ".output_html .ge { font-style: italic } /* Generic.Emph */\n", ".output_html .gr { color: #E40000 } /* Generic.Error */\n", ".output_html .gh { color: #000080; font-weight: bold } /* Generic.Heading */\n", ".output_html .gi { color: #008400 } /* Generic.Inserted */\n", ".output_html .go { color: #717171 } /* Generic.Output */\n", ".output_html .gp { color: #000080; font-weight: bold } /* Generic.Prompt */\n", ".output_html .gs { font-weight: bold } /* Generic.Strong */\n", ".output_html .gu { color: #800080; font-weight: bold } /* Generic.Subheading */\n", ".output_html .gt { color: #0044DD } /* Generic.Traceback */\n", ".output_html .kc { color: #008000; font-weight: bold } /* Keyword.Constant */\n", ".output_html .kd { color: #008000; font-weight: bold } /* Keyword.Declaration */\n", ".output_html .kn { color: #008000; font-weight: bold } /* Keyword.Namespace */\n", ".output_html .kp { color: #008000 } /* Keyword.Pseudo */\n", ".output_html .kr { color: #008000; font-weight: bold } /* Keyword.Reserved */\n", ".output_html .kt { color: #B00040 } /* Keyword.Type */\n", ".output_html .m { color: #666666 } /* Literal.Number */\n", ".output_html .s { color: #BA2121 } /* Literal.String */\n", ".output_html .na { color: #687822 } /* Name.Attribute */\n", ".output_html .nb { color: #008000 } /* Name.Builtin */\n", ".output_html .nc { color: #0000FF; font-weight: bold } /* Name.Class */\n", ".output_html .no { color: #880000 } /* Name.Constant */\n", ".output_html .nd { color: #AA22FF } /* Name.Decorator */\n", ".output_html .ni { color: #717171; font-weight: bold } /* Name.Entity */\n", ".output_html .ne { color: #CB3F38; font-weight: bold } /* Name.Exception */\n", ".output_html .nf { color: #0000FF } /* Name.Function */\n", ".output_html .nl { color: #767600 } /* Name.Label */\n", ".output_html .nn { color: #0000FF; font-weight: bold } /* Name.Namespace */\n", ".output_html .nt { color: #008000; font-weight: bold } /* Name.Tag */\n", ".output_html .nv { color: #19177C } /* Name.Variable */\n", ".output_html .ow { color: #AA22FF; font-weight: bold } /* Operator.Word */\n", ".output_html .w { color: #bbbbbb } /* Text.Whitespace */\n", ".output_html .mb { color: #666666 } /* Literal.Number.Bin */\n", ".output_html .mf { color: #666666 } /* Literal.Number.Float */\n", ".output_html .mh { color: #666666 } /* Literal.Number.Hex */\n", ".output_html .mi { color: #666666 } /* Literal.Number.Integer */\n", ".output_html .mo { color: #666666 } /* Literal.Number.Oct */\n", ".output_html .sa { color: #BA2121 } /* Literal.String.Affix */\n", ".output_html .sb { color: #BA2121 } /* Literal.String.Backtick */\n", ".output_html .sc { color: #BA2121 } /* Literal.String.Char */\n", ".output_html .dl { color: #BA2121 } /* Literal.String.Delimiter */\n", ".output_html .sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */\n", ".output_html .s2 { color: #BA2121 } /* Literal.String.Double */\n", ".output_html .se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */\n", ".output_html .sh { color: #BA2121 } /* Literal.String.Heredoc */\n", ".output_html .si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */\n", ".output_html .sx { color: #008000 } /* Literal.String.Other */\n", ".output_html .sr { color: #A45A77 } /* Literal.String.Regex */\n", ".output_html .s1 { color: #BA2121 } /* Literal.String.Single */\n", ".output_html .ss { color: #19177C } /* Literal.String.Symbol */\n", ".output_html .bp { color: #008000 } /* Name.Builtin.Pseudo */\n", ".output_html .fm { color: #0000FF } /* Name.Function.Magic */\n", ".output_html .vc { color: #19177C } /* Name.Variable.Class */\n", ".output_html .vg { color: #19177C } /* Name.Variable.Global */\n", ".output_html .vi { color: #19177C } /* Name.Variable.Instance */\n", ".output_html .vm { color: #19177C } /* Name.Variable.Magic */\n", ".output_html .il { color: #666666 } /* Literal.Number.Integer.Long */</style><div class=\"highlight\"><pre><span></span>user_df.info()\n", "</pre></div>\n"], "text/latex": ["\\begin{Verbatim}[commandchars=\\\\\\{\\}]\n", "user\\PYZus{}df.info()\n", "\\end{Verbatim}\n"], "text/plain": ["user_df.info()"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 10 entries, 0 to 9\n", "Data columns (total 6 columns):\n", " #   Column        Non-Null Count  Dtype \n", "---  ------        --------------  ----- \n", " 0   passenger_id  10 non-null     int64 \n", " 1   name          10 non-null     object\n", " 2   sex           10 non-null     object\n", " 3   age           10 non-null     int64 \n", " 4   sib_sp        10 non-null     object\n", " 5   parch         10 non-null     object\n", "dtypes: int64(2), object(4)\n", "memory usage: 612.0+ bytes\n"]}], "source": ["from IPython.display import display, Code, Markdown\n", "response = client.chat.completions.create(\n", "        model='deepseek-chat',\n", "        messages=[\n", "            {\"role\": \"user\", \"content\": \"user_info数据库中user_base_info数据表的数据，保存在pyhton本地环境中的名字为user_df\"},\n", "            {\"role\": \"user\", \"content\": \"现在要求你打印user_df数据集每个字段的数据类型信息\"}\n", "        ],\n", "        tools=tools,\n", "        tool_choice=\"auto\",\n", "    )\n", "code = json.loads(response.choices[0].message.tool_calls[0].function.arguments)\n", "code_str = code.get(\"py_code\")\n", "display(Code(code_str))\n", "python_interpreter(code_str)"]}, {"cell_type": "markdown", "id": "ac562246", "metadata": {}, "source": ["测试：更加复杂的测试，不但要做数据查询，还要让大模型将查询结果，保存到本地变量中"]}, {"cell_type": "code", "execution_count": 30, "id": "ecae1a23", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>pre { line-height: 125%; }\n", "td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", ".output_html .hll { background-color: #ffffcc }\n", ".output_html { background: #f8f8f8; }\n", ".output_html .c { color: #3D7B7B; font-style: italic } /* Comment */\n", ".output_html .err { border: 1px solid #FF0000 } /* Error */\n", ".output_html .k { color: #008000; font-weight: bold } /* Keyword */\n", ".output_html .o { color: #666666 } /* Operator */\n", ".output_html .ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */\n", ".output_html .cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */\n", ".output_html .cp { color: #9C6500 } /* Comment.Preproc */\n", ".output_html .cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */\n", ".output_html .c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */\n", ".output_html .cs { color: #3D7B7B; font-style: italic } /* Comment.Special */\n", ".output_html .gd { color: #A00000 } /* Generic.Deleted */\n", ".output_html .ge { font-style: italic } /* Generic.Emph */\n", ".output_html .gr { color: #E40000 } /* Generic.Error */\n", ".output_html .gh { color: #000080; font-weight: bold } /* Generic.Heading */\n", ".output_html .gi { color: #008400 } /* Generic.Inserted */\n", ".output_html .go { color: #717171 } /* Generic.Output */\n", ".output_html .gp { color: #000080; font-weight: bold } /* Generic.Prompt */\n", ".output_html .gs { font-weight: bold } /* Generic.Strong */\n", ".output_html .gu { color: #800080; font-weight: bold } /* Generic.Subheading */\n", ".output_html .gt { color: #0044DD } /* Generic.Traceback */\n", ".output_html .kc { color: #008000; font-weight: bold } /* Keyword.Constant */\n", ".output_html .kd { color: #008000; font-weight: bold } /* Keyword.Declaration */\n", ".output_html .kn { color: #008000; font-weight: bold } /* Keyword.Namespace */\n", ".output_html .kp { color: #008000 } /* Keyword.Pseudo */\n", ".output_html .kr { color: #008000; font-weight: bold } /* Keyword.Reserved */\n", ".output_html .kt { color: #B00040 } /* Keyword.Type */\n", ".output_html .m { color: #666666 } /* Literal.Number */\n", ".output_html .s { color: #BA2121 } /* Literal.String */\n", ".output_html .na { color: #687822 } /* Name.Attribute */\n", ".output_html .nb { color: #008000 } /* Name.Builtin */\n", ".output_html .nc { color: #0000FF; font-weight: bold } /* Name.Class */\n", ".output_html .no { color: #880000 } /* Name.Constant */\n", ".output_html .nd { color: #AA22FF } /* Name.Decorator */\n", ".output_html .ni { color: #717171; font-weight: bold } /* Name.Entity */\n", ".output_html .ne { color: #CB3F38; font-weight: bold } /* Name.Exception */\n", ".output_html .nf { color: #0000FF } /* Name.Function */\n", ".output_html .nl { color: #767600 } /* Name.Label */\n", ".output_html .nn { color: #0000FF; font-weight: bold } /* Name.Namespace */\n", ".output_html .nt { color: #008000; font-weight: bold } /* Name.Tag */\n", ".output_html .nv { color: #19177C } /* Name.Variable */\n", ".output_html .ow { color: #AA22FF; font-weight: bold } /* Operator.Word */\n", ".output_html .w { color: #bbbbbb } /* Text.Whitespace */\n", ".output_html .mb { color: #666666 } /* Literal.Number.Bin */\n", ".output_html .mf { color: #666666 } /* Literal.Number.Float */\n", ".output_html .mh { color: #666666 } /* Literal.Number.Hex */\n", ".output_html .mi { color: #666666 } /* Literal.Number.Integer */\n", ".output_html .mo { color: #666666 } /* Literal.Number.Oct */\n", ".output_html .sa { color: #BA2121 } /* Literal.String.Affix */\n", ".output_html .sb { color: #BA2121 } /* Literal.String.Backtick */\n", ".output_html .sc { color: #BA2121 } /* Literal.String.Char */\n", ".output_html .dl { color: #BA2121 } /* Literal.String.Delimiter */\n", ".output_html .sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */\n", ".output_html .s2 { color: #BA2121 } /* Literal.String.Double */\n", ".output_html .se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */\n", ".output_html .sh { color: #BA2121 } /* Literal.String.Heredoc */\n", ".output_html .si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */\n", ".output_html .sx { color: #008000 } /* Literal.String.Other */\n", ".output_html .sr { color: #A45A77 } /* Literal.String.Regex */\n", ".output_html .s1 { color: #BA2121 } /* Literal.String.Single */\n", ".output_html .ss { color: #19177C } /* Literal.String.Symbol */\n", ".output_html .bp { color: #008000 } /* Name.Builtin.Pseudo */\n", ".output_html .fm { color: #0000FF } /* Name.Function.Magic */\n", ".output_html .vc { color: #19177C } /* Name.Variable.Class */\n", ".output_html .vg { color: #19177C } /* Name.Variable.Global */\n", ".output_html .vi { color: #19177C } /* Name.Variable.Instance */\n", ".output_html .vm { color: #19177C } /* Name.Variable.Magic */\n", ".output_html .il { color: #666666 } /* Literal.Number.Integer.Long */</style><div class=\"highlight\"><pre><span></span><span class=\"nx\">user_df_info</span><span class=\"w\"> </span><span class=\"p\">=</span><span class=\"w\"> </span><span class=\"nx\">user_df</span><span class=\"p\">.</span><span class=\"nx\">dtypes</span>\n", "</pre></div>\n"], "text/latex": ["\\begin{Verbatim}[commandchars=\\\\\\{\\}]\n", "\\PY{n+nx}{user\\PYZus{}df\\PYZus{}info}\\PY{+w}{ }\\PY{p}{=}\\PY{+w}{ }\\PY{n+nx}{user\\PYZus{}df}\\PY{p}{.}\\PY{n+nx}{dtypes}\n", "\\end{Verbatim}\n"], "text/plain": ["user_df_info = user_df.dtypes"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'user_df_info': passenger_id     int64\n", " name            object\n", " sex             object\n", " age              int64\n", " sib_sp          object\n", " parch           object\n", " dtype: object}"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import display, Code, Markdown\n", "response = client.chat.completions.create(\n", "        model='deepseek-chat',\n", "        messages=[\n", "            {\"role\": \"user\", \"content\": \"user_info数据库中user_base_info数据表的数据，保存在pyhton本地环境中的名字为user_df\"},\n", "            {\"role\": \"user\", \"content\": \"现在要求你把user_df数据集每个字段的数据类型信息保存到python本次环境中，变量名字为：user_df_info\"}\n", "        ],\n", "        tools=tools,\n", "        tool_choice=\"auto\",\n", "    )\n", "code = json.loads(response.choices[0].message.tool_calls[0].function.arguments)\n", "code_str = code.get(\"py_code\")\n", "display(Code(code_str))\n", "python_interpreter(code_str)"]}, {"cell_type": "code", "execution_count": 31, "id": "18e5cbe0", "metadata": {}, "outputs": [{"data": {"text/plain": ["passenger_id     int64\n", "name            object\n", "sex             object\n", "age              int64\n", "sib_sp          object\n", "parch           object\n", "dtype: object"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["user_df_info"]}, {"cell_type": "markdown", "id": "5ca79f3b", "metadata": {}, "source": ["测试：更加复杂的测试，现在不但让大模型能够对本地数据进行数据分析，还需要进行数据可视化"]}, {"cell_type": "code", "execution_count": 32, "id": "f544ec72", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>pre { line-height: 125%; }\n", "td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", ".output_html .hll { background-color: #ffffcc }\n", ".output_html { background: #f8f8f8; }\n", ".output_html .c { color: #3D7B7B; font-style: italic } /* Comment */\n", ".output_html .err { border: 1px solid #FF0000 } /* Error */\n", ".output_html .k { color: #008000; font-weight: bold } /* Keyword */\n", ".output_html .o { color: #666666 } /* Operator */\n", ".output_html .ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */\n", ".output_html .cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */\n", ".output_html .cp { color: #9C6500 } /* Comment.Preproc */\n", ".output_html .cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */\n", ".output_html .c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */\n", ".output_html .cs { color: #3D7B7B; font-style: italic } /* Comment.Special */\n", ".output_html .gd { color: #A00000 } /* Generic.Deleted */\n", ".output_html .ge { font-style: italic } /* Generic.Emph */\n", ".output_html .gr { color: #E40000 } /* Generic.Error */\n", ".output_html .gh { color: #000080; font-weight: bold } /* Generic.Heading */\n", ".output_html .gi { color: #008400 } /* Generic.Inserted */\n", ".output_html .go { color: #717171 } /* Generic.Output */\n", ".output_html .gp { color: #000080; font-weight: bold } /* Generic.Prompt */\n", ".output_html .gs { font-weight: bold } /* Generic.Strong */\n", ".output_html .gu { color: #800080; font-weight: bold } /* Generic.Subheading */\n", ".output_html .gt { color: #0044DD } /* Generic.Traceback */\n", ".output_html .kc { color: #008000; font-weight: bold } /* Keyword.Constant */\n", ".output_html .kd { color: #008000; font-weight: bold } /* Keyword.Declaration */\n", ".output_html .kn { color: #008000; font-weight: bold } /* Keyword.Namespace */\n", ".output_html .kp { color: #008000 } /* Keyword.Pseudo */\n", ".output_html .kr { color: #008000; font-weight: bold } /* Keyword.Reserved */\n", ".output_html .kt { color: #B00040 } /* Keyword.Type */\n", ".output_html .m { color: #666666 } /* Literal.Number */\n", ".output_html .s { color: #BA2121 } /* Literal.String */\n", ".output_html .na { color: #687822 } /* Name.Attribute */\n", ".output_html .nb { color: #008000 } /* Name.Builtin */\n", ".output_html .nc { color: #0000FF; font-weight: bold } /* Name.Class */\n", ".output_html .no { color: #880000 } /* Name.Constant */\n", ".output_html .nd { color: #AA22FF } /* Name.Decorator */\n", ".output_html .ni { color: #717171; font-weight: bold } /* Name.Entity */\n", ".output_html .ne { color: #CB3F38; font-weight: bold } /* Name.Exception */\n", ".output_html .nf { color: #0000FF } /* Name.Function */\n", ".output_html .nl { color: #767600 } /* Name.Label */\n", ".output_html .nn { color: #0000FF; font-weight: bold } /* Name.Namespace */\n", ".output_html .nt { color: #008000; font-weight: bold } /* Name.Tag */\n", ".output_html .nv { color: #19177C } /* Name.Variable */\n", ".output_html .ow { color: #AA22FF; font-weight: bold } /* Operator.Word */\n", ".output_html .w { color: #bbbbbb } /* Text.Whitespace */\n", ".output_html .mb { color: #666666 } /* Literal.Number.Bin */\n", ".output_html .mf { color: #666666 } /* Literal.Number.Float */\n", ".output_html .mh { color: #666666 } /* Literal.Number.Hex */\n", ".output_html .mi { color: #666666 } /* Literal.Number.Integer */\n", ".output_html .mo { color: #666666 } /* Literal.Number.Oct */\n", ".output_html .sa { color: #BA2121 } /* Literal.String.Affix */\n", ".output_html .sb { color: #BA2121 } /* Literal.String.Backtick */\n", ".output_html .sc { color: #BA2121 } /* Literal.String.Char */\n", ".output_html .dl { color: #BA2121 } /* Literal.String.Delimiter */\n", ".output_html .sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */\n", ".output_html .s2 { color: #BA2121 } /* Literal.String.Double */\n", ".output_html .se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */\n", ".output_html .sh { color: #BA2121 } /* Literal.String.Heredoc */\n", ".output_html .si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */\n", ".output_html .sx { color: #008000 } /* Literal.String.Other */\n", ".output_html .sr { color: #A45A77 } /* Literal.String.Regex */\n", ".output_html .s1 { color: #BA2121 } /* Literal.String.Single */\n", ".output_html .ss { color: #19177C } /* Literal.String.Symbol */\n", ".output_html .bp { color: #008000 } /* Name.Builtin.Pseudo */\n", ".output_html .fm { color: #0000FF } /* Name.Function.Magic */\n", ".output_html .vc { color: #19177C } /* Name.Variable.Class */\n", ".output_html .vg { color: #19177C } /* Name.Variable.Global */\n", ".output_html .vi { color: #19177C } /* Name.Variable.Instance */\n", ".output_html .vm { color: #19177C } /* Name.Variable.Magic */\n", ".output_html .il { color: #666666 } /* Literal.Number.Integer.Long */</style><div class=\"highlight\"><pre><span></span><span class=\"kn\">import</span> <span class=\"nn\">matplotlib.pyplot</span> <span class=\"k\">as</span> <span class=\"nn\">plt</span>\n", "\n", "<span class=\"c1\"># 统计性别分布</span>\n", "<span class=\"n\">sex_counts</span> <span class=\"o\">=</span> <span class=\"n\">user_df</span><span class=\"p\">[</span><span class=\"s1\">&#39;sex&#39;</span><span class=\"p\">]</span><span class=\"o\">.</span><span class=\"n\">value_counts</span><span class=\"p\">()</span>\n", "\n", "<span class=\"c1\"># 设置中文字体</span>\n", "<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">rcParams</span><span class=\"p\">[</span><span class=\"s1\">&#39;font.sans-serif&#39;</span><span class=\"p\">]</span> <span class=\"o\">=</span> <span class=\"p\">[</span><span class=\"s1\">&#39;SimHei&#39;</span><span class=\"p\">]</span>  <span class=\"c1\"># 用来正常显示中文标签</span>\n", "<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">rcParams</span><span class=\"p\">[</span><span class=\"s1\">&#39;axes.unicode_minus&#39;</span><span class=\"p\">]</span> <span class=\"o\">=</span> <span class=\"kc\">False</span>  <span class=\"c1\"># 用来正常显示负号</span>\n", "\n", "<span class=\"c1\"># 绘制饼图</span>\n", "<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">figure</span><span class=\"p\">(</span><span class=\"n\">figsize</span><span class=\"o\">=</span><span class=\"p\">(</span><span class=\"mi\">8</span><span class=\"p\">,</span> <span class=\"mi\">8</span><span class=\"p\">))</span>\n", "<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">pie</span><span class=\"p\">(</span><span class=\"n\">sex_counts</span><span class=\"p\">,</span> <span class=\"n\">labels</span><span class=\"o\">=</span><span class=\"n\">sex_counts</span><span class=\"o\">.</span><span class=\"n\">index</span><span class=\"p\">,</span> <span class=\"n\">autopct</span><span class=\"o\">=</span><span class=\"s1\">&#39;</span><span class=\"si\">%1.1f%%</span><span class=\"s1\">&#39;</span><span class=\"p\">,</span> <span class=\"n\">startangle</span><span class=\"o\">=</span><span class=\"mi\">90</span><span class=\"p\">)</span>\n", "<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">title</span><span class=\"p\">(</span><span class=\"s1\">&#39;性别分布&#39;</span><span class=\"p\">)</span>\n", "<span class=\"n\">plt</span><span class=\"o\">.</span><span class=\"n\">show</span><span class=\"p\">()</span>\n", "</pre></div>\n"], "text/latex": ["\\begin{Verbatim}[commandchars=\\\\\\{\\}]\n", "\\PY{k+kn}{import} \\PY{n+nn}{matplotlib}\\PY{n+nn}{.}\\PY{n+nn}{pyplot} \\PY{k}{as} \\PY{n+nn}{plt}\n", "\n", "\\PY{c+c1}{\\PYZsh{} 统计性别分布}\n", "\\PY{n}{sex\\PYZus{}counts} \\PY{o}{=} \\PY{n}{user\\PYZus{}df}\\PY{p}{[}\\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{sex}\\PY{l+s+s1}{\\PYZsq{}}\\PY{p}{]}\\PY{o}{.}\\PY{n}{value\\PYZus{}counts}\\PY{p}{(}\\PY{p}{)}\n", "\n", "\\PY{c+c1}{\\PYZsh{} 设置中文字体}\n", "\\PY{n}{plt}\\PY{o}{.}\\PY{n}{rcParams}\\PY{p}{[}\\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{font.sans\\PYZhy{}serif}\\PY{l+s+s1}{\\PYZsq{}}\\PY{p}{]} \\PY{o}{=} \\PY{p}{[}\\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{SimHei}\\PY{l+s+s1}{\\PYZsq{}}\\PY{p}{]}  \\PY{c+c1}{\\PYZsh{} 用来正常显示中文标签}\n", "\\PY{n}{plt}\\PY{o}{.}\\PY{n}{rcParams}\\PY{p}{[}\\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{axes.unicode\\PYZus{}minus}\\PY{l+s+s1}{\\PYZsq{}}\\PY{p}{]} \\PY{o}{=} \\PY{k+kc}{False}  \\PY{c+c1}{\\PYZsh{} 用来正常显示负号}\n", "\n", "\\PY{c+c1}{\\PYZsh{} 绘制饼图}\n", "\\PY{n}{plt}\\PY{o}{.}\\PY{n}{figure}\\PY{p}{(}\\PY{n}{figsize}\\PY{o}{=}\\PY{p}{(}\\PY{l+m+mi}{8}\\PY{p}{,} \\PY{l+m+mi}{8}\\PY{p}{)}\\PY{p}{)}\n", "\\PY{n}{plt}\\PY{o}{.}\\PY{n}{pie}\\PY{p}{(}\\PY{n}{sex\\PYZus{}counts}\\PY{p}{,} \\PY{n}{labels}\\PY{o}{=}\\PY{n}{sex\\PYZus{}counts}\\PY{o}{.}\\PY{n}{index}\\PY{p}{,} \\PY{n}{autopct}\\PY{o}{=}\\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+si}{\\PYZpc{}1.1f}\\PY{l+s+si}{\\PYZpc{}\\PYZpc{}}\\PY{l+s+s1}{\\PYZsq{}}\\PY{p}{,} \\PY{n}{startangle}\\PY{o}{=}\\PY{l+m+mi}{90}\\PY{p}{)}\n", "\\PY{n}{plt}\\PY{o}{.}\\PY{n}{title}\\PY{p}{(}\\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{性别分布}\\PY{l+s+s1}{\\PYZsq{}}\\PY{p}{)}\n", "\\PY{n}{plt}\\PY{o}{.}\\PY{n}{show}\\PY{p}{(}\\PY{p}{)}\n", "\\end{Verbatim}\n"], "text/plain": ["import matplotlib.pyplot as plt\n", "\n", "# 统计性别分布\n", "sex_counts = user_df['sex'].value_counts()\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "\n", "# 绘制饼图\n", "plt.figure(figsize=(8, 8))\n", "plt.pie(sex_counts, labels=sex_counts.index, autopct='%1.1f%%', startangle=90)\n", "plt.title('性别分布')\n", "plt.show()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'plt': <module 'matplotlib.pyplot' from 'c:\\\\Users\\\\<USER>\\\\.conda\\\\envs\\\\pkq3\\\\Lib\\\\site-packages\\\\matplotlib\\\\pyplot.py'>,\n", " 'sex_counts': sex\n", " male      5\n", " female    5\n", " Name: count, dtype: int64}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import display, Code, Markdown\n", "response = client.chat.completions.create(\n", "        model='deepseek-chat',\n", "        messages=[\n", "            {\"role\": \"user\", \"content\": \"user_info数据库中user_base_info数据表的数据，保存在pyhton本地环境中的名字为user_df\"},\n", "            {\"role\": \"user\", \"content\": \"现在要求你把user_df数据集中sex这个字段给我绘制一个饼图，我要看看男生和女生的分布情况，需要支持中文显示\"}\n", "        ],\n", "        tools=tools,\n", "        tool_choice=\"auto\",\n", "    )\n", "code = json.loads(response.choices[0].message.tool_calls[0].function.arguments)\n", "code_str = code.get(\"py_code\")\n", "display(Code(code_str))\n", "python_interpreter(code_str)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}