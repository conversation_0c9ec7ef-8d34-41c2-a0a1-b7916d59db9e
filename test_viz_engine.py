#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版可视化引擎 - 用于测试
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import time
import os
from typing import Dict, List

class SimpleVisualizationEngine:
    """简化版可视化引擎"""
    
    def __init__(self):
        """初始化"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 确保输出目录存在
        os.makedirs('powerbank_memory', exist_ok=True)
        
        print("📊 简化版可视化引擎初始化完成")
    
    def create_chart(self, data: List[Dict], chart_type: str, title: str) -> Dict:
        """创建图表"""
        try:
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            
            if chart_type == "柱状图":
                self._create_bar_chart(df, ax, title)
            elif chart_type == "饼图":
                self._create_pie_chart(df, ax, title)
            elif chart_type == "折线图":
                self._create_line_chart(df, ax, title)
            else:
                self._create_bar_chart(df, ax, title)  # 默认柱状图
            
            # 保存图表
            chart_path = f"powerbank_memory/chart_{chart_type}_{int(time.time())}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            return {
                "success": True,
                "chart_type": chart_type,
                "path": chart_path,
                "message": f"{chart_type}生成成功"
            }
            
        except Exception as e:
            return {
                "success": False,
                "chart_type": chart_type,
                "path": None,
                "message": f"图表生成失败: {str(e)}"
            }
    
    def _create_bar_chart(self, df: pd.DataFrame, ax, title: str):
        """创建柱状图"""
        # 寻找合适的列
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            x_col = categorical_cols[0]
            y_col = numeric_cols[0]
            
            bars = ax.bar(df[x_col], df[y_col], 
                         color=plt.cm.Set3(np.linspace(0, 1, len(df))))
            
            ax.set_xlabel(x_col)
            ax.set_ylabel(y_col)
            ax.set_title(title, fontsize=14, fontweight='bold')
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height:.0f}', ha='center', va='bottom')
            
            plt.xticks(rotation=45)
    
    def _create_pie_chart(self, df: pd.DataFrame, ax, title: str):
        """创建饼图"""
        # 寻找合适的列
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            labels = df[categorical_cols[0]]
            sizes = df[numeric_cols[-1]]  # 使用最后一个数值列（通常是百分比）
            
            colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, autopct='%1.1f%%',
                                             colors=colors, startangle=90)
            
            ax.set_title(title, fontsize=14, fontweight='bold')
    
    def _create_line_chart(self, df: pd.DataFrame, ax, title: str):
        """创建折线图"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) >= 1:
            # 使用索引作为x轴，第一个数值列作为y轴
            y_col = numeric_cols[0]
            ax.plot(df.index, df[y_col], marker='o', linewidth=2, markersize=6)
            
            ax.set_xlabel('序号')
            ax.set_ylabel(y_col)
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.grid(True, alpha=0.3)

def test_simple_engine():
    """测试简化引擎"""
    print("🧪 测试简化版可视化引擎...")
    
    # 测试数据
    test_data = [
        {"品牌名称": "思腾智电", "订单数量": 4963, "市场份额": 13.20},
        {"品牌名称": "怪兽充电", "订单数量": 5100, "市场份额": 13.60},
        {"品牌名称": "搜电", "订单数量": 5076, "市场份额": 20.30},
        {"品牌名称": "街电", "订单数量": 4800, "市场份额": 19.20}
    ]
    
    engine = SimpleVisualizationEngine()
    
    # 测试不同图表
    tests = [
        ("柱状图", "品牌订单数量对比"),
        ("饼图", "品牌市场份额分布")
    ]
    
    for chart_type, title in tests:
        result = engine.create_chart(test_data, chart_type, title)
        if result["success"]:
            print(f"✅ {chart_type}测试成功: {result['path']}")
        else:
            print(f"❌ {chart_type}测试失败: {result['message']}")

if __name__ == "__main__":
    test_simple_engine()
