#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享充电宝数据仓库 - 后端API服务
提供数据可视化所需的RESTful API接口
"""

import os
import pandas as pd
from flask import Flask, jsonify, request
from flask_cors import CORS
import json
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 允许跨域访问

# 数据文件路径配置
DATA_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'AWS')

def load_csv_data(filename):
    """加载CSV数据文件"""
    try:
        file_path = os.path.join(DATA_PATH, filename)
        if os.path.exists(file_path):
            df = pd.read_csv(file_path, encoding='utf-8')
            return df
        else:
            print(f"文件不存在: {file_path}")
            return None
    except Exception as e:
        print(f"读取文件 {filename} 时出错: {str(e)}")
        return None

@app.route('/')
def index():
    """API根路径"""
    return jsonify({
        'message': '共享充电宝数据仓库API服务',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat(),
        'endpoints': [
            '/api/brand-revenue',
            '/api/user-behavior', 
            '/api/time-summary',
            '/api/region-usage',
            '/api/region-heatmap'
        ]
    })

@app.route('/api/brand-revenue')
def get_brand_revenue():
    """获取品牌收入汇总数据"""
    try:
        df = load_csv_data('brand_revenue_summary.csv')
        if df is None:
            return jsonify({'error': '数据文件不存在'}), 404
        
        # 转换为前端需要的格式
        data = {
            'brands': df['品牌名称'].tolist(),
            'orders': df['订单数量'].tolist(),
            'revenue': df['总收入'].tolist(),
            'avgPrice': df['平均单价'].tolist(),
            'marketShare': df['市场份额'].tolist(),
            'total': {
                'totalOrders': int(df['订单数量'].sum()),
                'totalRevenue': float(df['总收入'].sum()),
                'avgMarketPrice': float(df['平均单价'].mean())
            }
        }
        
        return jsonify({
            'success': True,
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取品牌收入数据失败: {str(e)}'
        }), 500

@app.route('/api/user-behavior')
def get_user_behavior():
    """获取用户行为汇总数据"""
    try:
        df = load_csv_data('user_behavior_summary.csv')
        if df is None:
            return jsonify({'error': '数据文件不存在'}), 404
        
        # 计算统计信息
        usage_stats = df['使用次数'].describe()
        consumption_stats = df['总消费'].describe()
        
        # 按使用次数分组统计
        usage_groups = pd.cut(df['使用次数'], bins=[0, 15, 20, 25, 30], labels=['低频用户', '中频用户', '高频用户', '超高频用户'])
        usage_distribution = usage_groups.value_counts().to_dict()
        
        data = {
            'userIds': df['用户ID'].tolist(),
            'usageCount': df['使用次数'].tolist(),
            'totalConsumption': df['总消费'].tolist(),
            'statistics': {
                'totalUsers': len(df),
                'avgUsage': float(usage_stats['mean']),
                'avgConsumption': float(consumption_stats['mean']),
                'maxUsage': int(usage_stats['max']),
                'maxConsumption': float(consumption_stats['max'])
            },
            'distribution': {k: int(v) for k, v in usage_distribution.items() if pd.notna(k)}
        }
        
        return jsonify({
            'success': True,
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取用户行为数据失败: {str(e)}'
        }), 500

@app.route('/api/time-summary')
def get_time_summary():
    """获取时间趋势汇总数据"""
    try:
        df = load_csv_data('time_summary.csv')
        if df is None:
            return jsonify({'error': '数据文件不存在'}), 404

        # 过滤掉空行
        df = df.dropna()

        data = {
            'years': df['年份'].tolist(),
            'records': df['记录数'].tolist(),
            'total': {
                'totalYears': len(df),
                'totalRecords': int(df['记录数'].sum()),
                'avgRecordsPerYear': float(df['记录数'].mean())
            }
        }

        return jsonify({
            'success': True,
            'data': data,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取时间趋势数据失败: {str(e)}'
        }), 500

@app.route('/api/region-usage')
def get_region_usage():
    """获取地区使用汇总数据"""
    try:
        df = load_csv_data('region_usage_summary.csv')
        if df is None:
            return jsonify({'error': '数据文件不存在'}), 404
        
        # 按收入排序，取前20名
        df_sorted = df.sort_values('总收入', ascending=False).head(20)
        
        data = {
            'regionIds': df_sorted['地区ID'].tolist(),
            'revenue': df_sorted['总收入'].tolist(),
            'total': {
                'totalRegions': len(df),
                'totalRevenue': float(df['总收入'].sum()),
                'avgRevenue': float(df['总收入'].mean())
            }
        }
        
        return jsonify({
            'success': True,
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取地区使用数据失败: {str(e)}'
        }), 500

@app.route('/api/region-heatmap')
def get_region_heatmap():
    """获取地区热力图数据"""
    try:
        df = load_csv_data('region_heatmap_data.csv')
        if df is None:
            return jsonify({'error': '数据文件不存在'}), 404
        
        # 按省份汇总收入
        province_revenue = df.groupby('省份')['总收入'].sum().sort_values(ascending=False)
        
        # 按城市汇总收入（取前30名）
        city_revenue = df.groupby(['省份', '城市'])['总收入'].sum().sort_values(ascending=False).head(30)
        
        data = {
            'provinces': province_revenue.index.tolist(),
            'provinceRevenue': province_revenue.values.tolist(),
            'cities': [f"{idx[0]}-{idx[1]}" for idx in city_revenue.index],
            'cityRevenue': city_revenue.values.tolist(),
            'rawData': df[['省份', '城市', '总收入']].to_dict('records'),
            'total': {
                'totalProvinces': len(province_revenue),
                'totalCities': len(df),
                'totalRevenue': float(df['总收入'].sum())
            }
        }
        
        return jsonify({
            'success': True,
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取地区热力图数据失败: {str(e)}'
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'error': '接口不存在',
        'message': '请检查API路径是否正确'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'success': False,
        'error': '服务器内部错误',
        'message': '请联系管理员'
    }), 500

if __name__ == '__main__':
    print("🚀 共享充电宝数据仓库API服务启动中...")
    print(f"📁 数据路径: {DATA_PATH}")
    print("🌐 服务地址: http://localhost:5000")
    print("📖 API文档: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
