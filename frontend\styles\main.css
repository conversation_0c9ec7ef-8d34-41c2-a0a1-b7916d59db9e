/* 共享充电宝数据仓库 - 主样式文件 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 页面头部样式 */
.dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.title {
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.title .icon {
    font-size: 2rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.header-info {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.time {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
}

.status {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.status.online {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.status.offline {
    background: rgba(244, 67, 54, 0.2);
    color: #F44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

/* 主要内容区域 */
.dashboard-main {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* 加载和错误状态 */
.loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: white;
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.retry-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.retry-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* 图表容器 */
.charts-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.chart-row {
    width: 100%;
}

.chart-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    padding: 1.5rem 2rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.card-header h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.card-stats {
    display: flex;
    gap: 1.5rem;
}

.card-stats span {
    background: rgba(255, 255, 255, 0.8);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    color: #555;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.chart-content {
    padding: 1rem;
}

.chart {
    width: 100%;
    height: 400px;
    min-height: 400px;
}

/* 页面底部 */
.dashboard-footer {
    background: rgba(0, 0, 0, 0.1);
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    padding: 1rem;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .title {
        font-size: 1.5rem;
    }
    
    .dashboard-main {
        padding: 1rem;
    }
    
    .card-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .card-stats {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .chart {
        height: 300px;
        min-height: 300px;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 1.2rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-header h3 {
        font-size: 1.1rem;
    }
    
    .chart {
        height: 250px;
        min-height: 250px;
    }
}

/* 动画效果 */
.chart-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
