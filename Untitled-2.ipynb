{"cells": [{"cell_type": "code", "execution_count": null, "id": "a95a04a3", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 🧠 模块1: 共享充电宝智能记忆系统\n", "# 作用: 专门为共享充电宝数据分析优化的多模态记忆管理\n", "# ===================================================================\n", "\n", "import os\n", "import json\n", "import pymysql\n", "import pandas as pd\n", "from openai import OpenAI\n", "import httpx\n", "from rich.console import Console\n", "from typing import List, Dict, Optional, Union, Any\n", "from datetime import datetime\n", "from dashscope import get_tokenizer\n", "\n", "console = Console()\n", "\n", "def custom_print(info):\n", "    \"\"\"美化输出函数\"\"\"\n", "    console.print(info)\n", "\n", "class PowerBankIntelligentMemory:\n", "    \"\"\"\n", "    共享充电宝智能记忆管理器\n", "    \n", "    专门针对共享充电宝业务场景设计:\n", "    1. 🧠 智能上下文管理 - 基于Token和数量双重控制\n", "    2. 📊 数据查询记忆 - 记住SQL查询和结果模式\n", "    3. 🎯 业务场景识别 - 自动识别用户、品牌、地区等分析类型\n", "    4. 💡 智能提示生成 - 基于历史查询提供相关建议\n", "    5. 🔄 动态优化清理 - 保留重要业务上下文\n", "    \"\"\"\n", "    \n", "    def __init__(self, \n", "                 count_threshold: int = 25,\n", "                 token_threshold: int = 4000,\n", "                 use_token_mode: bool = True,\n", "                 model: str = \"qwen-turbo\"):\n", "        \"\"\"\n", "        初始化共享充电宝智能记忆\n", "        \n", "        Args:\n", "            count_threshold: 消息数量阈值 (针对充电宝业务优化)\n", "            token_threshold: Token数量阈值 (支持复杂SQL和分析)\n", "            use_token_mode: 是否使用Token模式\n", "            model: 用于Token计算的模型\n", "        \"\"\"\n", "        self.messages = []\n", "        self.count_threshold = count_threshold\n", "        self.token_threshold = token_threshold\n", "        self.use_token_mode = use_token_mode\n", "        self.current_count = 0\n", "        self.current_tokens = 0\n", "        \n", "        # 共享充电宝业务特定记忆\n", "        self.query_history = []  # SQL查询历史\n", "        self.business_context = {\n", "            \"current_analysis_type\": None,  # 当前分析类型：用户/品牌/地区/时间\n", "            \"focused_tables\": [],  # 当前关注的表\n", "            \"key_metrics\": [],  # 关键指标\n", "            \"last_results\": None  # 最近的查询结果\n", "        }\n", "        \n", "        # 业务场景模板\n", "        self.business_scenarios = {\n", "            \"用户分析\": [\"user_table\", \"user_behavior_summary\"],\n", "            \"品牌分析\": [\"brand_revenue_summary\", \"order_table\"],\n", "            \"地区分析\": [\"region_table\", \"region_heatmap_data\", \"region_usage_summary\"],\n", "            \"时间分析\": [\"time_table\", \"time_summary\", \"order_table\"],\n", "            \"综合分析\": [\"order_table\", \"user_table\", \"region_table\", \"time_table\"]\n", "        }\n", "        \n", "        # 初始化Token编码器\n", "        if use_token_mode:\n", "            try:\n", "                self.tokenizer = get_tokenizer(model)\n", "                custom_print(f\"✅ 智能Token模式已启用，模型: {model}\")\n", "            except Exception as e:\n", "                custom_print(f\"⚠️ Token编码器初始化失败，切换到数量模式: {e}\")\n", "                self.use_token_mode = False\n", "        \n", "        custom_print(f\"🧠 共享充电宝智能记忆初始化完成\")\n", "        custom_print(f\"📊 记忆模式: {'Token智能模式' if self.use_token_mode else '数量模式'}\")\n", "        custom_print(f\"🎯 业务场景: {len(self.business_scenarios)}种分析类型\")\n", "    \n", "    def _calculate_tokens(self, content: str) -> int:\n", "        \"\"\"计算文本的Token数量 - 优化版\"\"\"\n", "        if not self.use_token_mode or not hasattr(self, 'tokenizer'):\n", "            # 针对中文和SQL的词数估算优化\n", "            chinese_chars = len([c for c in content if '\\u4e00' <= c <= '\\u9fff'])\n", "            english_words = len(content.replace('，', ' ').replace('。', ' ').split())\n", "            return chinese_chars + english_words\n", "        \n", "        try:\n", "            return len(self.tokenizer.encode(content))\n", "        except Exception:\n", "            return len(content.split())\n", "    \n", "    def _get_message_content(self, message: Dict) -> str:\n", "        \"\"\"提取消息内容用于Token计算 - 增强版\"\"\"\n", "        if isinstance(message, dict):\n", "            if 'content' in message:\n", "                return str(message['content'])\n", "            elif 'sql_query' in message:\n", "                # 特殊处理SQL查询消息\n", "                return f\"SQL: {message['sql_query']}\"\n", "            elif 'business_insight' in message:\n", "                # 特殊处理业务洞察消息\n", "                return f\"洞察: {message['business_insight']}\"\n", "            elif 'tool_calls' in message and message['tool_calls']:\n", "                return json.dumps(message['tool_calls'])\n", "            elif 'name' in message:\n", "                return str(message.get('content', ''))\n", "            else:\n", "                return json.dumps(message)\n", "        return str(message)\n", "    \n", "    def _detect_business_scenario(self, message: Dict) -> str:\n", "        \"\"\"智能检测业务场景类型\"\"\"\n", "        content = self._get_message_content(message).lower()\n", "        \n", "        # 关键词映射\n", "        scenario_keywords = {\n", "            \"用户分析\": [\"用户\", \"性别\", \"年龄\", \"职业\", \"消费\", \"行为\", \"男女\"],\n", "            \"品牌分析\": [\"品牌\", \"收入\", \"市场份额\", \"订单\", \"竞争\", \"排名\"],\n", "            \"地区分析\": [\"地区\", \"省份\", \"城市\", \"区域\", \"分布\", \"热力图\"],\n", "            \"时间分析\": [\"时间\", \"趋势\", \"月份\", \"年度\", \"季度\", \"周期\"],\n", "            \"综合分析\": [\"对比\", \"关联\", \"综合\", \"整体\", \"全面\"]\n", "        }\n", "        \n", "        max_score = 0\n", "        detected_scenario = \"综合分析\"\n", "        \n", "        for scenario, keywords in scenario_keywords.items():\n", "            score = sum(1 for keyword in keywords if keyword in content)\n", "            if score > max_score:\n", "                max_score = score\n", "                detected_scenario = scenario\n", "        \n", "        return detected_scenario\n", "    \n", "    def append_message(self, message: Dict):\n", "        \"\"\"\n", "        添加消息到智能记忆\n", "        \n", "        增强功能:\n", "        1. 自动检测业务场景\n", "        2. 记录SQL查询历史\n", "        3. 更新业务上下文\n", "        4. 智能清理策略\n", "        \"\"\"\n", "        # 检测业务场景\n", "        scenario = self._detect_business_scenario(message)\n", "        self.business_context[\"current_analysis_type\"] = scenario\n", "        self.business_context[\"focused_tables\"] = self.business_scenarios.get(scenario, [])\n", "        \n", "        # 记录SQL查询\n", "        if 'sql_query' in message:\n", "            self.query_history.append({\n", "                \"timestamp\": datetime.now().isoformat(),\n", "                \"sql\": message['sql_query'],\n", "                \"scenario\": scenario\n", "            })\n", "            # 只保留最近20条查询历史\n", "            if len(self.query_history) > 20:\n", "                self.query_history = self.query_history[-20:]\n", "        \n", "        # 记录查询结果\n", "        if 'query_result' in message:\n", "            self.business_context[\"last_results\"] = message['query_result']\n", "        \n", "        # 计算消息大小\n", "        content = self._get_message_content(message)\n", "        message_tokens = self._calculate_tokens(content)\n", "        \n", "        # 添加业务标签\n", "        enhanced_message = message.copy()\n", "        enhanced_message['business_scenario'] = scenario\n", "        enhanced_message['timestamp'] = datetime.now().isoformat()\n", "        \n", "        # 添加消息\n", "        self.messages.append(enhanced_message)\n", "        self.current_count += 1\n", "        self.current_tokens += message_tokens\n", "        \n", "        # 检查是否需要清理\n", "        if self.use_token_mode:\n", "            if self.current_tokens > self.token_threshold:\n", "                self._smart_cleanup_by_tokens()\n", "        else:\n", "            if self.current_count > self.count_threshold:\n", "                self._smart_cleanup_by_count()\n", "        \n", "        custom_print(f\"📝 消息已添加 - 场景: {scenario}\")\n", "        custom_print(f\"📊 当前状态: {self.current_count}条/{self.current_tokens}tokens\")\n", "    \n", "    def _smart_cleanup_by_tokens(self):\n", "        \"\"\"基于Token的智能清理 - 业务优化版\"\"\"\n", "        custom_print(\"🧹 开始智能Token清理...\")\n", "        \n", "        # 保留策略优化\n", "        system_messages = [msg for msg in self.messages if msg.get('role') == 'system']\n", "        \n", "        # 保留重要的业务消息\n", "        important_messages = []\n", "        recent_messages = self.messages[-8:]  # 保留最近8条\n", "        \n", "        # 保留包含SQL查询的消息\n", "        sql_messages = [msg for msg in self.messages if 'sql_query' in msg][-3:]  # 最近3条SQL\n", "        \n", "        # 保留当前业务场景相关的消息\n", "        current_scenario = self.business_context.get(\"current_analysis_type\")\n", "        scenario_messages = [msg for msg in self.messages \n", "                           if msg.get('business_scenario') == current_scenario][-2:]\n", "        \n", "        # 合并去重\n", "        preserved_messages = system_messages\n", "        for msg_list in [recent_messages, sql_messages, scenario_messages]:\n", "            for msg in msg_list:\n", "                if msg not in preserved_messages:\n", "                    preserved_messages.append(msg)\n", "        \n", "        # 按时间戳排序\n", "        preserved_messages.sort(key=lambda x: x.get('timestamp', ''))\n", "        \n", "        self.messages = preserved_messages\n", "        self.current_count = len(self.messages)\n", "        self.current_tokens = sum(self._calculate_tokens(self._get_message_content(msg)) \n", "                                for msg in self.messages)\n", "        \n", "        custom_print(f\"✅ 智能清理完成 - 剩余: {self.current_count}条/{self.current_tokens}tokens\")\n", "        custom_print(f\"🎯 保留场景: {current_scenario}\")\n", "    \n", "    def _smart_cleanup_by_count(self):\n", "        \"\"\"基于数量的智能清理 - 业务优化版\"\"\"\n", "        custom_print(\"🧹 开始智能数量清理...\")\n", "        \n", "        # 保留系统消息\n", "        system_messages = [msg for msg in self.messages if msg.get('role') == 'system']\n", "        other_messages = [msg for msg in self.messages if msg.get('role') != 'system']\n", "        \n", "        # 智能保留策略\n", "        keep_count = self.count_threshold // 2\n", "        \n", "        # 优先保留SQL查询和业务洞察消息\n", "        priority_messages = [msg for msg in other_messages \n", "                           if 'sql_query' in msg or 'business_insight' in msg]\n", "        recent_messages = other_messages[-keep_count:]\n", "        \n", "        # 合并去重\n", "        preserved_other = []\n", "        for msg in priority_messages + recent_messages:\n", "            if msg not in preserved_other:\n", "                preserved_other.append(msg)\n", "        \n", "        # 如果还是太多，只保留最近的\n", "        if len(preserved_other) > keep_count:\n", "            preserved_other = preserved_other[-keep_count:]\n", "        \n", "        self.messages = system_messages + preserved_other\n", "        self.current_count = len(self.messages)\n", "        \n", "        custom_print(f\"✅ 智能清理完成 - 剩余: {self.current_count}条消息\")\n", "    \n", "    def get_messages(self) -> List[Dict]:\n", "        \"\"\"获取当前所有消息\"\"\"\n", "        return self.messages.copy()\n", "    \n", "    def get_business_context(self) -> Dict:\n", "        \"\"\"获取当前业务上下文\"\"\"\n", "        return {\n", "            \"current_scenario\": self.business_context[\"current_analysis_type\"],\n", "            \"focused_tables\": self.business_context[\"focused_tables\"],\n", "            \"recent_queries\": self.query_history[-5:] if self.query_history else [],\n", "            \"memory_stats\": self.get_memory_stats()\n", "        }\n", "    \n", "    def get_memory_stats(self) -> Dict:\n", "        \"\"\"获取记忆统计信息 - 增强版\"\"\"\n", "        return {\n", "            \"message_count\": self.current_count,\n", "            \"token_count\": self.current_tokens,\n", "            \"memory_mode\": \"智能Token模式\" if self.use_token_mode else \"数量模式\",\n", "            \"threshold\": self.token_threshold if self.use_token_mode else self.count_threshold,\n", "            \"usage_rate\": f\"{(self.current_tokens/self.token_threshold*100):.1f}%\" if self.use_token_mode \n", "                         else f\"{(self.current_count/self.count_threshold*100):.1f}%\",\n", "            \"current_scenario\": self.business_context[\"current_analysis_type\"],\n", "            \"sql_queries_count\": len(self.query_history),\n", "            \"focused_tables\": len(self.business_context[\"focused_tables\"])\n", "        }\n", "    \n", "    def get_smart_suggestions(self) -> List[str]:\n", "        \"\"\"基于当前上下文生成智能建议\"\"\"\n", "        current_scenario = self.business_context.get(\"current_analysis_type\")\n", "        \n", "        suggestions_map = {\n", "            \"用户分析\": [\n", "                \"分析不同性别用户的消费习惯\",\n", "                \"查看各职业用户的使用频次\",\n", "                \"对比不同年龄段的消费能力\",\n", "                \"找出高价值用户群体\"\n", "            ],\n", "            \"品牌分析\": [\n", "                \"对比各品牌的市场份额\",\n", "                \"分析品牌收入排名变化\",\n", "                \"查看品牌在不同地区的表现\",\n", "                \"评估品牌定价策略效果\"\n", "            ],\n", "            \"地区分析\": [\n", "                \"查看各省份收入分布\",\n", "                \"分析城市级别的使用热度\",\n", "                \"对比不同地区的用户偏好\",\n", "                \"识别潜力市场区域\"\n", "            ],\n", "            \"时间分析\": [\n", "                \"分析业务增长趋势\",\n", "                \"查看季节性使用模式\",\n", "                \"对比不同时间段的表现\",\n", "                \"预测未来发展趋势\"\n", "            ]\n", "        }\n", "        \n", "        return suggestions_map.get(current_scenario, [\n", "            \"开始数据探索分析\",\n", "            \"查看整体业务概况\",\n", "            \"进行多维度对比分析\"\n", "        ])\n", "    \n", "    def clear_memory(self, keep_system: bool = True, keep_business_context: bool = True):\n", "        \"\"\"清空记忆 - 增强版\"\"\"\n", "        if keep_system:\n", "            system_messages = [msg for msg in self.messages if msg.get('role') == 'system']\n", "            self.messages = system_messages\n", "            self.current_count = len(system_messages)\n", "            self.current_tokens = sum(self._calculate_tokens(self._get_message_content(msg)) \n", "                                    for msg in system_messages)\n", "        else:\n", "            self.messages = []\n", "            self.current_count = 0\n", "            self.current_tokens = 0\n", "        \n", "        if not keep_business_context:\n", "            self.query_history = []\n", "            self.business_context = {\n", "                \"current_analysis_type\": None,\n", "                \"focused_tables\": [],\n", "                \"key_metrics\": [],\n", "                \"last_results\": None\n", "            }\n", "        \n", "        custom_print(\"🗑️ 智能记忆已清空\")\n", "        if keep_business_context:\n", "            custom_print(\"💼 业务上下文已保留\")\n", "\n", "custom_print(\"✅ 模块1: 共享充电宝智能记忆系统 - 加载完成\")\n", "custom_print(\"🎯 支持场景: 用户分析、品牌分析、地区分析、时间分析、综合分析\")\n", "custom_print(\"🧠 智能功能: 场景识别、上下文管理、查询记忆、建议生成\")"]}, {"cell_type": "code", "execution_count": null, "id": "353bd5bb", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 📚 模块2: 长期记忆与专家文档系统 - 优化版\n", "# 作用: 动态模板管理、业务规则验证、知识链接服务\n", "# ===================================================================\n", "\n", "class PowerBankExpertKnowledge:\n", "    \"\"\"\n", "    共享充电宝专家知识库 - 精简优化版\n", "    \n", "    核心功能:\n", "    1. 🎯 智能分析模板生成 - SQL模板和可视化建议\n", "    2. 🔍 业务规则验证 - 数据质量和逻辑检查\n", "    3. 🔗 表关系映射 - 标准关联查询模式\n", "    4. 📖 外部知识链接 - 避免重复存储\n", "    \"\"\"\n", "    \n", "    def __init__(self, expert_file_path: str = \"expert_knowledge.md\"):\n", "        self.expert_file_path = expert_file_path\n", "        self.analysis_templates = self._load_analysis_templates()\n", "        self.business_rules = self._load_business_rules()\n", "        self.table_relationships = self._load_table_relationships()\n", "        self.query_patterns = self._load_query_patterns()\n", "        custom_print(\"📚 专家知识库已加载 - 模板驱动模式\")\n", "    \n", "    def get_domain_knowledge(self) -> str:\n", "        \"\"\"动态读取外部专家知识文档\"\"\"\n", "        try:\n", "            with open(self.expert_file_path, 'r', encoding='utf-8') as f:\n", "                return f.read()\n", "        except FileNotFoundError:\n", "            return \"\"\"\n", "# 共享充电宝行业基础知识\n", "\n", "## 核心业务指标\n", "- 订单数量、总收入、平均单价、市场份额\n", "- 用户行为、地域分布、时间趋势\n", "\n", "## 数据架构\n", "- 维度表: user_table, region_table, time_table\n", "- 事实表: order_table  \n", "- 汇总表: brand_revenue_summary, user_behavior_summary等\n", "\n", "请创建 expert_knowledge.md 文件获取完整知识库。\n", "\"\"\"\n", "    \n", "    def _load_analysis_templates(self) -> Dict:\n", "        \"\"\"加载智能分析模板\"\"\"\n", "        return {\n", "            \"用户分析\": {\n", "                \"description\": \"用户画像和行为分析模板\",\n", "                \"sql_templates\": {\n", "                    \"性别分布\": \"SELECT 性别, COUNT(*) as 用户数, ROUND(COUNT(*)*100.0/(SELECT COUNT(*) FROM user_table), 2) as 占比 FROM user_table GROUP BY 性别\",\n", "                    \"年龄分布\": \"SELECT CASE WHEN 年龄 < 25 THEN '25岁以下' WHEN 年龄 < 35 THEN '25-35岁' WHEN 年龄 < 45 THEN '35-45岁' ELSE '45岁以上' END as 年龄段, COUNT(*) as 用户数 FROM user_table GROUP BY 年龄段 ORDER BY 用户数 DESC\",\n", "                    \"职业分析\": \"SELECT 职业, COUNT(*) as 用户数 FROM user_table GROUP BY 职业 ORDER BY 用户数 DESC LIMIT 10\",\n", "                    \"用户价值\": \"SELECT * FROM user_behavior_summary ORDER BY 总消费 DESC LIMIT 20\"\n", "                },\n", "                \"visualization_types\": [\"饼图\", \"柱状图\", \"年龄金字塔\", \"用户价值矩阵\"],\n", "                \"key_insights\": [\"用户性别比例\", \"主要年龄群体\", \"职业分布特征\", \"高价值用户特征\"]\n", "            },\n", "            \n", "            \"品牌分析\": {\n", "                \"description\": \"品牌竞争和市场份额分析\",\n", "                \"sql_templates\": {\n", "                    \"市场份额\": \"SELECT 品牌, 订单数量, 总收入, 市场份额 FROM brand_revenue_summary ORDER BY 市场份额 DESC\",\n", "                    \"品牌对比\": \"SELECT 品牌, COUNT(*) as 订单数, SUM(单价) as 总收入, AVG(单价) as 平均单价 FROM order_table GROUP BY 品牌 ORDER BY 总收入 DESC\",\n", "                    \"定价策略\": \"SELECT 品牌, MIN(单价) as 最低价, MAX(单价) as 最高价, AVG(单价) as 平均价, STDDEV(单价) as 价格波动 FROM order_table GROUP BY 品牌\",\n", "                    \"品牌增长\": \"SELECT o.品牌, t.年月, COUNT(*) as 月订单数, SUM(o.单价) as 月收入 FROM order_table o JOIN time_table t ON o.时间ID = t.时间ID GROUP BY o.品牌, t.年月 ORDER BY o.品牌, t.年月\"\n", "                },\n", "                \"visualization_types\": [\"市场份额饼图\", \"品牌收入对比\", \"定价分布箱线图\", \"增长趋势线\"],\n", "                \"key_insights\": [\"市场领导者\", \"定价差异化\", \"增长趋势\", \"竞争格局\"]\n", "            },\n", "            \n", "            \"地区分析\": {\n", "                \"description\": \"地域分布和区域运营分析\",\n", "                \"sql_templates\": {\n", "                    \"省份排名\": \"SELECT r.省份, COUNT(*) as 使用次数, SUM(o.单价) as 总收入 FROM order_table o JOIN region_table r ON o.地区ID = r.地区ID GROUP BY r.省份 ORDER BY 总收入 DESC\",\n", "                    \"城市热度\": \"SELECT r.城市, COUNT(*) as 使用频次, COUNT(DISTINCT o.用户ID) as 用户数 FROM order_table o JOIN region_table r ON o.地区ID = r.地区ID GROUP BY r.城市 ORDER BY 使用频次 DESC LIMIT 20\",\n", "                    \"热力图数据\": \"SELECT * FROM region_heatmap_data WHERE 使用频次 > 10 ORDER BY 总收入 DESC\",\n", "                    \"区域汇总\": \"SELECT * FROM region_usage_summary ORDER BY 总收入 DESC LIMIT 30\"\n", "                },\n", "                \"visualization_types\": [\"热力图\", \"地区排名条形图\", \"省份对比雷达图\", \"城市分布散点图\"],\n", "                \"key_insights\": [\"核心市场区域\", \"增长潜力区域\", \"用户密度分布\", \"区域收入贡献\"]\n", "            },\n", "            \n", "            \"时间分析\": {\n", "                \"description\": \"时间趋势和周期性分析\",\n", "                \"sql_templates\": {\n", "                    \"月度趋势\": \"SELECT t.年月, COUNT(*) as 订单数, SUM(o.单价) as 收入, COUNT(DISTINCT o.用户ID) as 活跃用户 FROM order_table o JOIN time_table t ON o.时间ID = t.时间ID GROUP BY t.年月 ORDER BY t.年月\",\n", "                    \"年度对比\": \"SELECT t.年份, COUNT(*) as 年订单数, SUM(o.单价) as 年收入 FROM order_table o JOIN time_table t ON o.时间ID = t.时间ID GROUP BY t.年份 ORDER BY t.年份\",\n", "                    \"增长率\": \"SELECT 年月, 记录数, 总收入, LAG(总收入) OVER (ORDER BY 年月) as 上月收入, ROUND((总收入 - LAG(总收入) OVER (ORDER BY 年月)) / LAG(总收入) OVER (ORDER BY 年月) * 100, 2) as 增长率 FROM time_summary ORDER BY 年月\",\n", "                    \"时间汇总\": \"SELECT * FROM time_summary ORDER BY 年份, 年月\"\n", "                },\n", "                \"visualization_types\": [\"趋势线图\", \"季节性分析\", \"增长率曲线\", \"年度对比柱状图\"],\n", "                \"key_insights\": [\"业务增长趋势\", \"季节性模式\", \"用户活跃度变化\", \"收入波动分析\"]\n", "            },\n", "            \n", "            \"综合分析\": {\n", "                \"description\": \"多维度交叉分析模板\",\n", "                \"sql_templates\": {\n", "                    \"用户地区分布\": \"SELECT r.省份, u.性别, COUNT(*) as 用户数 FROM order_table o JOIN user_table u ON o.用户ID = u.用户ID JOIN region_table r ON o.地区ID = r.地区ID GROUP BY r.省份, u.性别 ORDER BY r.省份, 用户数 DESC\",\n", "                    \"品牌地区表现\": \"SELECT o.品牌, r.省份, COUNT(*) as 订单数, SUM(o.单价) as 收入 FROM order_table o JOIN region_table r ON o.地区ID = r.地区ID GROUP BY o.品牌, r.省份 ORDER BY 收入 DESC\",\n", "                    \"时间品牌趋势\": \"SELECT t.年月, o.品牌, COUNT(*) as 订单数 FROM order_table o JOIN time_table t ON o.时间ID = t.时间ID GROUP BY t.年月, o.品牌 ORDER BY t.年月, 订单数 DESC\",\n", "                    \"全维度汇总\": \"SELECT o.品牌, r.省份, u.性别, COUNT(*) as 订单数, AVG(o.单价) as 平均单价 FROM order_table o JOIN user_table u ON o.用户ID = u.用户ID JOIN region_table r ON o.地区ID = r.地区ID GROUP BY o.品牌, r.省份, u.性别 HAVING 订单数 > 5 ORDER BY 订单数 DESC\"\n", "                },\n", "                \"visualization_types\": [\"多维数据透视表\", \"交叉分析热力图\", \"综合仪表板\", \"关联分析网络图\"],\n", "                \"key_insights\": [\"多维关联模式\", \"交叉影响因素\", \"综合业务表现\", \"深度洞察发现\"]\n", "            }\n", "        }\n", "    \n", "    def _load_business_rules(self) -> Dict:\n", "        \"\"\"加载业务规则和验证逻辑\"\"\"\n", "        return {\n", "            \"data_quality_rules\": {\n", "                \"user_table\": {\n", "                    \"required_fields\": [\"用户ID\", \"姓名\", \"性别\"],\n", "                    \"validation_rules\": {\n", "                        \"年龄\": \"年龄 BETWEEN 0 AND 120\",\n", "                        \"性别\": \"性别 IN ('男', '女')\",\n", "                        \"用户ID\": \"用户ID IS NOT NULL AND 用户ID > 0\"\n", "                    }\n", "                },\n", "                \"order_table\": {\n", "                    \"required_fields\": [\"订单ID\", \"用户ID\", \"地区ID\", \"时间ID\", \"单价\", \"品牌\"],\n", "                    \"validation_rules\": {\n", "                        \"单价\": \"单价 > 0 AND 单价 < 1000\",\n", "                        \"品牌\": \"品牌 IS NOT NULL AND LENGTH(品牌) > 0\",\n", "                        \"订单ID\": \"订单ID IS NOT NULL AND 订单ID > 0\"\n", "                    }\n", "                },\n", "                \"region_table\": {\n", "                    \"required_fields\": [\"地区ID\", \"省份\", \"城市\"],\n", "                    \"validation_rules\": {\n", "                        \"经度\": \"经度 BETWEEN -180 AND 180\",\n", "                        \"纬度\": \"纬度 BETWEEN -90 AND 90\",\n", "                        \"省份\": \"省份 IS NOT NULL AND LENGTH(省份) > 0\"\n", "                    }\n", "                }\n", "            },\n", "            \n", "            \"business_logic_rules\": {\n", "                \"market_share_calculation\": {\n", "                    \"formula\": \"市场份额 = (品牌收入 / 总收入) * 100\",\n", "                    \"sql\": \"ROUND(SUM(单价) * 100.0 / (SELECT SUM(单价) FROM order_table), 2) as 市场份额\"\n", "                },\n", "                \"user_value_segmentation\": {\n", "                    \"high_value\": \"总消费 > (SELECT AVG(总消费) * 2 FROM user_behavior_summary)\",\n", "                    \"medium_value\": \"总消费 BETWEEN (SELECT AVG(总消费) FROM user_behavior_summary) AND (SELECT AVG(总消费) * 2 FROM user_behavior_summary)\",\n", "                    \"low_value\": \"总消费 < (SELECT AVG(总消费) FROM user_behavior_summary)\"\n", "                },\n", "                \"regional_classification\": {\n", "                    \"hotspot\": \"使用频次 > (SELECT AVG(使用频次) * 1.5 FROM region_usage_summary)\",\n", "                    \"potential\": \"使用频次 BETWEEN (SELECT AVG(使用频次) * 0.8 FROM region_usage_summary) AND (SELECT AVG(使用频次) * 1.5 FROM region_usage_summary)\",\n", "                    \"developing\": \"使用频次 < (SELECT AVG(使用频次) * 0.8 FROM region_usage_summary)\"\n", "                }\n", "            },\n", "            \n", "            \"analysis_guidelines\": {\n", "                \"sample_size_requirements\": {\n", "                    \"minimum_records\": 100,\n", "                    \"confidence_threshold\": 0.95,\n", "                    \"statistical_significance\": \"样本量应大于100条记录以确保统计显著性\"\n", "                },\n", "                \"visualization_rules\": {\n", "                    \"pie_chart\": \"分类数量不超过8个，最小占比不低于2%\",\n", "                    \"bar_chart\": \"适用于分类对比，建议不超过20个分类\",\n", "                    \"line_chart\": \"适用于时间序列，至少需要5个时间点\",\n", "                    \"heatmap\": \"适用于地理分析，需要经纬度坐标\"\n", "                }\n", "            }\n", "        }\n", "    \n", "    def _load_table_relationships(self) -> Dict:\n", "        \"\"\"加载表关系和连接模式\"\"\"\n", "        return {\n", "            \"primary_relationships\": {\n", "                \"order_user\": {\n", "                    \"join_condition\": \"order_table.用户ID = user_table.用户ID\",\n", "                    \"relationship_type\": \"多对一\",\n", "                    \"description\": \"订单表关联用户表，获取用户属性信息\"\n", "                },\n", "                \"order_region\": {\n", "                    \"join_condition\": \"order_table.地区ID = region_table.地区ID\", \n", "                    \"relationship_type\": \"多对一\",\n", "                    \"description\": \"订单表关联地区表，获取地理位置信息\"\n", "                },\n", "                \"order_time\": {\n", "                    \"join_condition\": \"order_table.时间ID = time_table.时间ID\",\n", "                    \"relationship_type\": \"多对一\", \n", "                    \"description\": \"订单表关联时间表，获取时间维度信息\"\n", "                }\n", "            },\n", "            \n", "            \"summary_table_sources\": {\n", "                \"brand_revenue_summary\": {\n", "                    \"source_tables\": [\"order_table\"],\n", "                    \"aggregation_level\": \"品牌\",\n", "                    \"key_metrics\": [\"订单数量\", \"总收入\", \"平均单价\", \"市场份额\"]\n", "                },\n", "                \"user_behavior_summary\": {\n", "                    \"source_tables\": [\"order_table\", \"user_table\"],\n", "                    \"aggregation_level\": \"用户\",\n", "                    \"key_metrics\": [\"使用次数\", \"总消费\", \"平均消费\", \"最高消费\"]\n", "                },\n", "                \"region_usage_summary\": {\n", "                    \"source_tables\": [\"order_table\", \"region_table\"],\n", "                    \"aggregation_level\": \"地区\",\n", "                    \"key_metrics\": [\"使用次数\", \"总收入\", \"平均单价\", \"用户数量\"]\n", "                },\n", "                \"time_summary\": {\n", "                    \"source_tables\": [\"order_table\", \"time_table\"],\n", "                    \"aggregation_level\": \"时间\",\n", "                    \"key_metrics\": [\"记录数\", \"总收入\", \"平均单价\", \"活跃用户数\"]\n", "                }\n", "            },\n", "            \n", "            \"common_join_patterns\": {\n", "                \"full_dimension_join\": \"\"\"\n", "                    SELECT o.*, u.姓名, u.性别, u.年龄, r.省份, r.城市, t.年月\n", "                    FROM order_table o\n", "                    JOIN user_table u ON o.用户ID = u.用户ID\n", "                    JOIN region_table r ON o.地区ID = r.地区ID  \n", "                    JOIN time_table t ON o.时间ID = t.时间ID\n", "                \"\"\",\n", "                \"brand_analysis_join\": \"\"\"\n", "                    SELECT o.品牌, r.省份, COUNT(*) as 订单数, SUM(o.单价) as 收入\n", "                    FROM order_table o\n", "                    JOIN region_table r ON o.地区ID = r.地区ID\n", "                    GROUP BY o.品牌, r.省份\n", "                \"\"\",\n", "                \"user_profile_join\": \"\"\"\n", "                    SELECT u.*, ubs.使用次数, ubs.总消费, ubs.平均消费\n", "                    FROM user_table u\n", "                    LEFT JOIN user_behavior_summary ubs ON u.用户ID = ubs.用户ID\n", "                \"\"\"\n", "            }\n", "        }\n", "    \n", "    def _load_query_patterns(self) -> Dict:\n", "        \"\"\"加载常用查询模式\"\"\"\n", "        return {\n", "            \"aggregation_patterns\": {\n", "                \"count_by_category\": \"SELECT {category}, COUNT(*) as 数量 FROM {table} GROUP BY {category} ORDER BY 数量 DESC\",\n", "                \"sum_by_category\": \"SELECT {category}, SUM({metric}) as 总计 FROM {table} GROUP BY {category} ORDER BY 总计 DESC\",\n", "                \"avg_by_category\": \"SELECT {category}, AVG({metric}) as 平均值 FROM {table} GROUP BY {category} ORDER BY 平均值 DESC\",\n", "                \"top_n_analysis\": \"SELECT {fields} FROM {table} ORDER BY {metric} DESC LIMIT {n}\"\n", "            },\n", "            \n", "            \"time_series_patterns\": {\n", "                \"monthly_trend\": \"SELECT t.年月, COUNT(*) as 数量 FROM {fact_table} f JOIN time_table t ON f.时间ID = t.时间ID GROUP BY t.年月 ORDER BY t.年月\",\n", "                \"yearly_comparison\": \"SELECT t.年份, SUM({metric}) as 年度总计 FROM {fact_table} f JOIN time_table t ON f.时间ID = t.时间ID GROUP BY t.年份 ORDER BY t.年份\",\n", "                \"growth_rate\": \"SELECT 年月, 总收入, LAG(总收入) OVER (ORDER BY 年月) as 上期, ROUND((总收入 - LAG(总收入) OVER (ORDER BY 年月)) / LAG(总收入) OVER (ORDER BY 年月) * 100, 2) as 增长率 FROM time_summary\"\n", "            },\n", "            \n", "            \"ranking_patterns\": {\n", "                \"top_performers\": \"SELECT *, ROW_NUMBER() OVER (ORDER BY {metric} DESC) as 排名 FROM {table}\",\n", "                \"percentile_analysis\": \"SELECT *, NTILE(4) OVER (ORDER BY {metric}) as 四分位数 FROM {table}\",\n", "                \"comparative_ranking\": \"SELECT *, RANK() OVER (PARTITION BY {category} ORDER BY {metric} DESC) as 分类排名 FROM {table}\"\n", "            }\n", "        }\n", "    \n", "    def get_analysis_template(self, analysis_type: str) -> Dict:\n", "        \"\"\"获取指定类型的分析模板\"\"\"\n", "        return self.analysis_templates.get(analysis_type, {})\n", "    \n", "    def get_sql_template(self, analysis_type: str, template_name: str) -> str:\n", "        \"\"\"获取特定的SQL模板\"\"\"\n", "        templates = self.analysis_templates.get(analysis_type, {}).get(\"sql_templates\", {})\n", "        return templates.get(template_name, \"\")\n", "    \n", "    def validate_business_rule(self, rule_category: str, rule_name: str) -> str:\n", "        \"\"\"获取业务规则验证SQL\"\"\"\n", "        rules = self.business_rules.get(rule_category, {})\n", "        return rules.get(rule_name, \"\")\n", "    \n", "    def get_join_pattern(self, pattern_name: str) -> str:\n", "        \"\"\"获取标准连接模式\"\"\"\n", "        return self.table_relationships.get(\"common_join_patterns\", {}).get(pattern_name, \"\")\n", "    \n", "    def suggest_visualization(self, analysis_type: str) -> List[str]:\n", "        \"\"\"建议可视化类型\"\"\"\n", "        template = self.analysis_templates.get(analysis_type, {})\n", "        return template.get(\"visualization_types\", [\"表格\"])\n", "    \n", "    def get_key_insights_guide(self, analysis_type: str) -> List[str]:\n", "        \"\"\"获取关键洞察指导\"\"\"\n", "        template = self.analysis_templates.get(analysis_type, {})\n", "        return template.get(\"key_insights\", [])\n", "\n", "class PowerBankLongTermMemory:\n", "    \"\"\"\n", "    共享充电宝长期记忆管理 - 优化版\n", "    \n", "    功能:\n", "    1. 📝 持久化对话历史和查询记录\n", "    2. 🎯 场景化记忆存储和检索\n", "    3. 📊 智能统计和分析建议\n", "    4. 🔄 记忆优化和清理管理\n", "    \"\"\"\n", "    \n", "    def __init__(self, base_path: str = r'C:\\Users\\<USER>\\Desktop\\powerbank_memory'):\n", "        self.base_path = Path(base_path)\n", "        self.base_path.mkdir(parents=True, exist_ok=True)\n", "        \n", "        # 文件路径配置\n", "        self.conversation_file = self.base_path / \"conversations.jsonl\"\n", "        self.query_file = self.base_path / \"queries.jsonl\" \n", "        self.scenario_file = self.base_path / \"scenarios.jsonl\"\n", "        self.insights_file = self.base_path / \"insights.jsonl\"\n", "        \n", "        # 初始化专家知识\n", "        self.expert_knowledge = PowerBankExpertKnowledge()\n", "        \n", "        custom_print(f\"💾 长期记忆系统已初始化: {base_path}\")\n", "        custom_print(f\"📁 记忆文件: {len(list(self.base_path.glob('*.jsonl')))} 个\")\n", "    \n", "    def save_conversation(self, conversation_data: Dict):\n", "        \"\"\"保存对话记录\"\"\"\n", "        try:\n", "            record = {\n", "                \"timestamp\": datetime.now().isoformat(),\n", "                \"session_id\": conversation_data.get(\"session_id\", \"unknown\"),\n", "                \"user_input\": conversation_data.get(\"user_input\", \"\"),\n", "                \"response\": conversation_data.get(\"response\", \"\"),\n", "                \"analysis_type\": conversation_data.get(\"analysis_type\", \"unknown\"),\n", "                \"success\": conversation_data.get(\"success\", False)\n", "            }\n", "            \n", "            with open(self.conversation_file, 'a', encoding='utf-8') as f:\n", "                f.write(json.dumps(record, ensure_ascii=False) + '\\n')\n", "                \n", "        except Exception as e:\n", "            custom_print(f\"⚠️ 保存对话记录失败: {e}\")\n", "    \n", "    def save_query_result(self, query_data: Dict):\n", "        \"\"\"保存查询结果\"\"\"\n", "        try:\n", "            record = {\n", "                \"timestamp\": datetime.now().isoformat(),\n", "                \"question\": query_data.get(\"question\", \"\"),\n", "                \"sql_query\": query_data.get(\"sql_query\", \"\"),\n", "                \"execution_time\": query_data.get(\"execution_time\", 0),\n", "                \"row_count\": query_data.get(\"row_count\", 0),\n", "                \"success\": query_data.get(\"success\", False),\n", "                \"analysis_type\": query_data.get(\"analysis_type\", \"unknown\")\n", "            }\n", "            \n", "            with open(self.query_file, 'a', encoding='utf-8') as f:\n", "                f.write(json.dumps(record, ensure_ascii=False) + '\\n')\n", "                \n", "        except Exception as e:\n", "            custom_print(f\"⚠️ 保存查询记录失败: {e}\")\n", "    \n", "    def get_memory_stats(self) -> Dict:\n", "        \"\"\"获取记忆统计信息\"\"\"\n", "        stats = {\n", "            \"conversation_count\": 0,\n", "            \"query_count\": 0,\n", "            \"scenario_count\": 0,\n", "            \"insights_count\": 0,\n", "            \"memory_size_mb\": 0\n", "        }\n", "        \n", "        try:\n", "            # 统计各类记录数量\n", "            for file_path, key in [\n", "                (self.conversation_file, \"conversation_count\"),\n", "                (self.query_file, \"query_count\"), \n", "                (self.scenario_file, \"scenario_count\"),\n", "                (self.insights_file, \"insights_count\")\n", "            ]:\n", "                if file_path.exists():\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        stats[key] = sum(1 for line in f if line.strip())\n", "            \n", "            # 计算总文件大小\n", "            total_size = sum(f.stat().st_size for f in self.base_path.glob('*.jsonl') if f.exists())\n", "            stats[\"memory_size_mb\"] = round(total_size / (1024 * 1024), 2)\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"⚠️ 获取记忆统计失败: {e}\")\n", "        \n", "        return stats\n", "    \n", "    def get_expert_knowledge(self, topic: str = None) -> str:\n", "        \"\"\"获取专家知识\"\"\"\n", "        if topic and topic in self.expert_knowledge.analysis_templates:\n", "            template = self.expert_knowledge.get_analysis_template(topic)\n", "            return json.dumps(template, ensure_ascii=False, indent=2)\n", "        return self.expert_knowledge.get_domain_knowledge()\n", "    \n", "    def clear_old_memories(self, days_to_keep: int = 30):\n", "        \"\"\"清理旧记忆\"\"\"\n", "        try:\n", "            cutoff_date = datetime.now() - timedelta(days=days_to_keep)\n", "            cutoff_str = cutoff_date.isoformat()\n", "            \n", "            for file_path in [self.conversation_file, self.query_file, self.scenario_file]:\n", "                if file_path.exists():\n", "                    # 读取所有记录\n", "                    records = []\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        for line in f:\n", "                            if line.strip():\n", "                                try:\n", "                                    record = json.loads(line)\n", "                                    if record.get(\"timestamp\", \"\") > cutoff_str:\n", "                                        records.append(record)\n", "                                except:\n", "                                    continue\n", "                    \n", "                    # 重写文件\n", "                    with open(file_path, 'w', encoding='utf-8') as f:\n", "                        for record in records:\n", "                            f.write(json.dumps(record, ensure_ascii=False) + '\\n')\n", "            \n", "            custom_print(f\"🧹 已清理 {days_to_keep} 天前的记忆\")\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"⚠️ 清理记忆失败: {e}\")\n", "\n", "custom_print(\"✅ 模块2: 长期记忆与专家文档系统 - 优化完成\")\n", "custom_print(\"🎯 核心功能: 智能模板、业务规则、知识链接、记忆管理\")\n", "custom_print(\"📚 模板支持: 用户分析、品牌分析、地区分析、时间分析、综合分析\")\n", "custom_print(\"🔍 业务规则: 数据质量验证、业务逻辑检查、分析指导\")"]}, {"cell_type": "code", "execution_count": null, "id": "e0922f68", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 模块3: 共享充电宝智能查询引擎\n", "# ===================================================================\n", "\n", "class PowerBankIntelligentQueryEngine:\n", "    \n", "    def __init__(self, db_config: Dict, api_config: Dict):\n", "        self.db_config = db_config\n", "        self.api_config = api_config\n", "        self.client = self._init_openai_client()\n", "        self.connection = None\n", "        self.table_schemas = {}\n", "        self.query_cache = {}\n", "        self.performance_stats = {\n", "            \"total_queries\": 0,\n", "            \"cache_hits\": 0,\n", "            \"avg_response_time\": 0,\n", "            \"error_count\": 0\n", "        }\n", "        \n", "        self.dimension_tables = [\"user_table\", \"region_table\", \"time_table\"]\n", "        self.fact_tables = [\"order_table\"]\n", "        self.summary_tables = [\"brand_revenue_summary\", \"user_behavior_summary\", \n", "                              \"time_summary\", \"region_usage_summary\", \"region_heatmap_data\"]\n", "        self.expected_tables = self.dimension_tables + self.fact_tables + self.summary_tables\n", "        \n", "        self._init_field_mapping()\n", "        self._init_database()\n", "        self._load_table_schemas()\n", "        self._init_business_templates()\n", "\n", "    def verify_nine_table_structure(self):\n", "        \"\"\"验证9表结构完整性 - 核心方法\"\"\"\n", "        try:\n", "            if not self.connection:\n", "                return {\n", "                    \"structure_complete\": <PERSON><PERSON><PERSON>,\n", "                    \"total_found\": 0,\n", "                    \"missing_tables\": self.expected_tables,\n", "                    \"message\": \"数据库连接失败\",\n", "                    \"completion_rate\": \"0/9\"\n", "                }\n", "            \n", "            cursor = self.connection.cursor()\n", "            cursor.execute(\"SHOW TABLES\")\n", "            existing_tables = [table[0] for table in cursor.fetchall()]\n", "            cursor.close()\n", "            \n", "            # 检查每个期望的表\n", "            found_tables = []\n", "            missing_tables = []\n", "            \n", "            for table in self.expected_tables:\n", "                if table in existing_tables:\n", "                    found_tables.append(table)\n", "                else:\n", "                    missing_tables.append(table)\n", "            \n", "            structure_complete = len(missing_tables) == 0\n", "            found_count = len(found_tables)\n", "            total_expected = len(self.expected_tables)\n", "            \n", "            return {\n", "                \"structure_complete\": structure_complete,\n", "                \"total_found\": found_count,\n", "                \"found_tables\": found_tables,\n", "                \"missing_tables\": missing_tables,\n", "                \"existing_tables\": existing_tables,\n", "                \"expected_tables\": self.expected_tables,\n", "                \"completion_rate\": f\"{found_count}/{total_expected}\",\n", "                \"message\": f\"数据库结构检查完成: 找到 {found_count}/{total_expected} 张表\"\n", "            }\n", "            \n", "        except Exception as e:\n", "            return {\n", "                \"structure_complete\": <PERSON><PERSON><PERSON>,\n", "                \"total_found\": 0,\n", "                \"missing_tables\": self.expected_tables,\n", "                \"error\": str(e),\n", "                \"completion_rate\": \"0/9\",\n", "                \"message\": f\"结构验证失败: {e}\"\n", "            }\n", "\n", "    def _init_field_mapping(self):\n", "        self.field_mapping = {\n", "            \"user_table\": {\n", "                \"用户ID\": \"user_id\", \"姓名\": \"name\", \"年龄\": \"age\",\n", "                \"性别\": \"gender\", \"地址\": \"address\", \"职业\": \"occupation\"\n", "            },\n", "            \"order_table\": {\n", "                \"订单ID\": \"order_id\", \"用户ID\": \"user_id\", \"地区ID\": \"region_id\",\n", "                \"时间ID\": \"time_id\", \"开始时间\": \"start_time\", \"结束时间\": \"end_time\",\n", "                \"单价\": \"price\", \"品牌\": \"brand\"\n", "            },\n", "            \"region_table\": {\n", "                \"地区ID\": \"region_id\", \"省份\": \"province\", \"城市\": \"city\",\n", "                \"经度\": \"longitude\", \"纬度\": \"latitude\"\n", "            },\n", "            \"time_table\": {\n", "                \"时间ID\": \"time_id\", \"年\": \"year\", \"月\": \"month\",\n", "                \"日\": \"day\", \"年月\": \"year_month\"\n", "            },\n", "            \"brand_revenue_summary\": {\n", "                \"品牌\": \"brand\", \"订单数量\": \"order_count\", \"总收入\": \"total_revenue\",\n", "                \"平均单价\": \"avg_price\", \"市场份额\": \"market_share\"\n", "            },\n", "            \"user_behavior_summary\": {\n", "                \"用户ID\": \"user_id\", \"姓名\": \"name\", \"总消费\": \"total_consumption\",\n", "                \"使用次数\": \"usage_count\", \"平均消费\": \"avg_consumption\"\n", "            },\n", "            \"region_usage_summary\": {\n", "                \"省份\": \"province\", \"总收入\": \"total_revenue\",\n", "                \"使用次数\": \"usage_count\", \"用户数量\": \"user_count\"\n", "            },\n", "            \"region_heatmap_data\": {\n", "                \"省份\": \"province\", \"城市\": \"city\", \"经度\": \"longitude\",\n", "                \"纬度\": \"latitude\", \"总收入\": \"total_revenue\"\n", "            },\n", "            \"time_summary\": {\n", "                \"年月\": \"year_month\", \"总收入\": \"total_revenue\",\n", "                \"记录数\": \"record_count\", \"活跃用户数\": \"active_users\"\n", "            }\n", "        }\n", "        \n", "        self.reverse_field_mapping = {}\n", "        for table, fields in self.field_mapping.items():\n", "            self.reverse_field_mapping[table] = {v: k for k, v in fields.items()}\n", "\n", "    def _init_openai_client(self):\n", "        try:\n", "            import httpx\n", "            http_client = httpx.Client(follow_redirects=True, timeout=30)\n", "            \n", "            return OpenAI(\n", "                api_key=self.api_config['api_key'],\n", "                base_url=self.api_config.get('base_url', 'https://api.deepseek.com/v1'),\n", "                http_client=http_client\n", "            )\n", "        except Exception as e:\n", "            custom_print(f\"OpenAI客户端初始化失败: {e}\")\n", "            return None\n", "\n", "    def _init_database(self):\n", "        try:\n", "            import pymysql\n", "            self.connection = pymysql.connect(\n", "                host=self.db_config['host'],\n", "                user=self.db_config['user'],\n", "                password=self.db_config['password'],\n", "                database=self.db_config['database'],\n", "                charset='utf8mb4',\n", "                autocommit=True,\n", "                connect_timeout=10,\n", "                read_timeout=30\n", "            )\n", "            custom_print(\"数据库连接成功\")\n", "        except Exception as e:\n", "            custom_print(f\"数据库连接失败: {e}\")\n", "            self.connection = None\n", "\n", "    def _load_table_schemas(self):\n", "        if not self.connection:\n", "            return\n", "            \n", "        try:\n", "            cursor = self.connection.cursor()\n", "            \n", "            for table_name in self.expected_tables:\n", "                try:\n", "                    cursor.execute(f\"DESCRIBE {table_name}\")\n", "                    columns = cursor.fetchall()\n", "                    \n", "                    cursor.execute(f\"SELECT COUNT(*) FROM {table_name}\")\n", "                    row_count = cursor.fetchone()[0]\n", "                    \n", "                    cursor.execute(f\"SELECT * FROM {table_name} LIMIT 3\")\n", "                    sample_data = cursor.fetchall()\n", "                    \n", "                    cursor.execute(f\"SELECT * FROM {table_name} LIMIT 0\")\n", "                    column_names = [desc[0] for desc in cursor.description]\n", "                    \n", "                    self.table_schemas[table_name] = {\n", "                        'columns': columns,\n", "                        'row_count': row_count,\n", "                        'sample_data': sample_data,\n", "                        'column_names': column_names\n", "                    }\n", "                    \n", "                except Exception as e:\n", "                    custom_print(f\"表 {table_name} 加载失败: {e}\")\n", "                    \n", "            custom_print(f\"已加载 {len(self.table_schemas)} 个表的结构信息\")\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"表结构加载失败: {e}\")\n", "\n", "    def _init_business_templates(self):\n", "        self.business_templates = {\n", "            \"品牌分析\": \"SELECT 品牌, 订单数量, 总收入, 市场份额 FROM brand_revenue_summary ORDER BY 市场份额 DESC\",\n", "            \"地区分析\": \"SELECT 省份, 总收入, 使用次数, 用户数量 FROM region_usage_summary ORDER BY 总收入 DESC\",\n", "            \"时间趋势\": \"SELECT 年月, 总收入, 活跃用户数 FROM time_summary ORDER BY 年月\",\n", "            \"用户行为\": \"SELECT 姓名, 总消费, 使用次数, 平均消费 FROM user_behavior_summary ORDER BY 总消费 DESC\"\n", "        }\n", "\n", "    def natural_language_to_sql(self, question: str, expert_knowledge: str = \"\") -> str:\n", "        try:\n", "            if \"男女\" in question and (\"比例\" in question or \"分布\" in question):\n", "                return \"\"\"\n", "                SELECT \n", "                    CASE \n", "                        WHEN 性别 = '男' OR 性别 = 'M' OR 性别 = 'male' THEN '男性'\n", "                        WHEN 性别 = '女' OR 性别 = 'F' OR 性别 = 'female' THEN '女性'\n", "                        ELSE '其他'\n", "                    END as 性别分类,\n", "                    COUNT(*) as 用户数量,\n", "                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_table WHERE 性别 IS NOT NULL), 2) as 比例\n", "                FROM user_table \n", "                WHERE 性别 IS NOT NULL\n", "                GROUP BY 性别分类\n", "                ORDER BY 用户数量 DESC\n", "                \"\"\"\n", "            \n", "            schema_info = self._get_enhanced_database_info_with_mapping()\n", "            \n", "            prompt = f\"\"\"你是专业的SQL查询生成专家，请根据以下数据库结构生成准确的SQL查询：\n", "\n", "数据库架构说明:\n", "{schema_info}\n", "\n", "重要提醒：必须使用中文字段名，数据库表中的列名都是中文。\n", "例如：SELECT 用户ID, 姓名, 性别 FROM user_table\n", "\n", "用户问题: {question}\n", "\n", "专家知识参考: {expert_knowledge}\n", "\n", "SQL生成要求:\n", "1. 字段名：必须使用中文字段名\n", "2. 表名：使用英文表名\n", "3. 查询优化：优先使用汇总表提升性能\n", "4. 数据完整性：添加必要的WHERE条件过滤空值\n", "5. 结果排序：添加合理的ORDER BY子句\n", "\n", "请直接返回SQL语句，不要包含任何解释文字：\"\"\"\n", "\n", "            response = self.client.chat.completions.create(\n", "                model=self.api_config.get('model', 'deepseek-chat'),\n", "                messages=[{\"role\": \"user\", \"content\": prompt}],\n", "                temperature=0.1,\n", "                max_tokens=800\n", "            )\n", "            \n", "            sql_query = self._extract_sql_from_response(response.choices[0].message.content)\n", "            validated_sql = self._validate_and_fix_field_mapping(sql_query)\n", "            \n", "            return validated_sql\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"SQL生成失败: {e}\")\n", "            return f\"-- SQL生成失败: {str(e)}\"\n", "\n", "    def _get_enhanced_database_info_with_mapping(self) -> str:\n", "        info_parts = []\n", "        \n", "        for table_name, schema in self.table_schemas.items():\n", "            info_parts.append(f\"\\n{table_name} ({schema.get('row_count', 0)}条记录)\")\n", "            \n", "            info_parts.append(\"字段列表:\")\n", "            for col in schema['columns']:\n", "                field_name, field_type = col[0], col[1]\n", "                info_parts.append(f\"- {field_name} ({field_type})\")\n", "            \n", "            if table_name in self.field_mapping:\n", "                info_parts.append(\"字段映射:\")\n", "                for chinese_name, english_name in self.field_mapping[table_name].items():\n", "                    info_parts.append(f\"- {chinese_name} ↔ {english_name}\")\n", "            \n", "            if schema.get('sample_data'):\n", "                info_parts.append(\"示例数据:\")\n", "                for i, row in enumerate(schema['sample_data'][:2]):\n", "                    row_data = dict(zip(schema['column_names'], row))\n", "                    info_parts.append(f\"  {i+1}. {row_data}\")\n", "        \n", "        return \"\\n\".join(info_parts)\n", "\n", "    def _validate_and_fix_field_mapping(self, sql_query: str) -> str:\n", "        try:\n", "            import re\n", "            fixed_sql = sql_query\n", "            \n", "            for table_name, mapping in self.reverse_field_mapping.items():\n", "                for english_field, chinese_field in mapping.items():\n", "                    pattern = r'\\b' + re.escape(english_field) + r'\\b'\n", "                    fixed_sql = re.sub(pattern, chinese_field, fixed_sql, flags=re.IGNORECASE)\n", "            \n", "            if fixed_sql != sql_query:\n", "                custom_print(\"已自动修复SQL中的英文字段名为中文字段名\")\n", "            \n", "            return fixed_sql\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"字段映射修复失败: {e}\")\n", "            return sql_query\n", "\n", "    def execute_query(self, sql_query: str):\n", "        try:\n", "            start_time = time.time()\n", "            \n", "            cache_key = hash(sql_query)\n", "            if cache_key in self.query_cache:\n", "                self.performance_stats[\"cache_hits\"] += 1\n", "                return self.query_cache[cache_key]\n", "            \n", "            if not self._check_connection():\n", "                self._reconnect_database()\n", "            \n", "            cursor = self.connection.cursor()\n", "            cursor.execute(sql_query)\n", "            \n", "            columns = [desc[0] for desc in cursor.description]\n", "            rows = cursor.fetchall()\n", "            \n", "            results = []\n", "            for row in rows:\n", "                row_dict = {}\n", "                for i, value in enumerate(row):\n", "                    if isinstance(value, decimal.Decimal):\n", "                        row_dict[columns[i]] = float(value)\n", "                    elif isinstance(value, datetime.datetime):\n", "                        row_dict[columns[i]] = value.strftime('%Y-%m-%d %H:%M:%S')\n", "                    elif isinstance(value, datetime.date):\n", "                        row_dict[columns[i]] = value.strftime('%Y-%m-%d')\n", "                    else:\n", "                        row_dict[columns[i]] = value\n", "                results.append(row_dict)\n", "            \n", "            self.query_cache[cache_key] = results\n", "            \n", "            execution_time = time.time() - start_time\n", "            self.performance_stats[\"total_queries\"] += 1\n", "            self.performance_stats[\"avg_response_time\"] = (\n", "                (self.performance_stats[\"avg_response_time\"] * (self.performance_stats[\"total_queries\"] - 1) + execution_time) \n", "                / self.performance_stats[\"total_queries\"]\n", "            )\n", "            \n", "            return results\n", "            \n", "        except Exception as e:\n", "            self.performance_stats[\"error_count\"] += 1\n", "            custom_print(f\"查询执行失败: {e}\")\n", "            return f\"查询执行失败: {str(e)}\"\n", "\n", "    def generate_business_insight(self, question: str, data: List[Dict], expert_knowledge: str = \"\") -> str:\n", "        try:\n", "            if not data or len(data) == 0:\n", "                return \"无法生成洞察：查询结果为空\"\n", "            \n", "            data_summary = self._create_data_summary(data)\n", "            \n", "            prompt = f\"\"\"作为共享充电宝行业的资深数据分析师，请基于以下查询结果生成专业的业务洞察：\n", "\n", "查询问题: {question}\n", "数据摘要: {data_summary}\n", "专家知识参考: {expert_knowledge}\n", "\n", "请按以下结构生成洞察报告：\n", "1. 数据解读 - 关键数字和趋势分析\n", "2. 业务洞察 - 数据背后的业务含义、市场机会和风险点\n", "3. 行动建议 - 具体可执行的建议、优化策略和方向\n", "\n", "请确保分析专业、实用，避免空泛的描述：\"\"\"\n", "\n", "            response = self.client.chat.completions.create(\n", "                model=self.api_config.get('model', 'deepseek-chat'),\n", "                messages=[{\"role\": \"user\", \"content\": prompt}],\n", "                temperature=0.3,\n", "                max_tokens=1000\n", "            )\n", "            \n", "            return response.choices[0].message.content\n", "            \n", "        except Exception as e:\n", "            return f\"洞察生成失败: {str(e)}\"\n", "\n", "    def _extract_sql_from_response(self, response: str) -> str:\n", "        import re\n", "        \n", "        sql_match = re.search(r'```(?:sql)?\\s*(.*?)\\s*```', response, re.DOTALL | re.IGNORECASE)\n", "        if sql_match:\n", "            return sql_match.group(1).strip()\n", "        \n", "        lines = [line.strip() for line in response.split('\\n') if line.strip()]\n", "        return '\\n'.join(lines)\n", "\n", "    def _create_data_summary(self, data: List[Dict]) -> str:\n", "        if not data:\n", "            return \"数据为空\"\n", "        \n", "        summary_parts = [f\"数据总量: {len(data)}条记录\"]\n", "        \n", "        numeric_fields = []\n", "        for key, value in data[0].items():\n", "            if isinstance(value, (int, float)) and key not in ['用户ID', '订单ID', '地区ID', '时间ID']:\n", "                numeric_fields.append(key)\n", "        \n", "        for field in numeric_fields[:3]:\n", "            values = [row[field] for row in data if row[field] is not None]\n", "            if values:\n", "                summary_parts.append(f\"{field}: 最大值{max(values)}, 最小值{min(values)}, 平均值{sum(values)/len(values):.2f}\")\n", "        \n", "        summary_parts.append(\"示例数据:\")\n", "        for i, row in enumerate(data[:3]):\n", "            summary_parts.append(f\"  {i+1}. {row}\")\n", "        \n", "        return \"\\n\".join(summary_parts)\n", "\n", "    def _check_connection(self) -> bool:\n", "        try:\n", "            if self.connection:\n", "                self.connection.ping(reconnect=False)\n", "                return True\n", "        except:\n", "            pass\n", "        return False\n", "\n", "    def _reconnect_database(self):\n", "        try:\n", "            self._init_database()\n", "            custom_print(\"数据库重连成功\")\n", "        except Exception as e:\n", "            custom_print(f\"数据库重连失败: {e}\")\n", "\n", "    def get_database_info(self) -> str:\n", "        info_parts = []\n", "        for table_name, schema in self.table_schemas.items():\n", "            info_parts.append(f\"\\n{table_name} ({schema.get('row_count', 0)}条记录)\")\n", "            info_parts.append(\"字段列表:\")\n", "            for col in schema['columns']:\n", "                info_parts.append(f\"- {col[0]} ({col[1]})\")\n", "            \n", "            if schema.get('sample_data'):\n", "                info_parts.append(\"示例数据:\")\n", "                for i, row in enumerate(schema['sample_data'][:2]):\n", "                    row_data = dict(zip(schema['column_names'], row))\n", "                    info_parts.append(f\"  {i+1}. {row_data}\")\n", "        \n", "        return \"\\n\".join(info_parts)\n", "\n", "    def get_performance_stats(self) -> Dict:\n", "        return {\n", "            **self.performance_stats,\n", "            \"cache_size\": len(self.query_cache),\n", "            \"recent_queries\": list(self.query_cache.keys())[-5:] if self.query_cache else []\n", "        }\n", "\n", "    def get_field_mapping_info(self) -> Dict:\n", "        return {\n", "            \"field_mapping\": self.field_mapping,\n", "            \"reverse_mapping\": self.reverse_field_mapping,\n", "            \"supported_tables\": list(self.field_mapping.keys()),\n", "            \"mapping_status\": \"已初始化\" if hasattr(self, 'field_mapping') else \"未初始化\"\n", "        }\n", "\n", "    def generate_sql_query(self, question: str, context: str = \"\") -> Dict:\n", "        start_time = time.time()\n", "        \n", "        try:\n", "            sql_query = self.natural_language_to_sql(question, context)\n", "            response_time = time.time() - start_time\n", "            \n", "            return {\n", "                \"success\": True,\n", "                \"sql_query\": sql_query,\n", "                \"response_time\": response_time,\n", "                \"error\": None\n", "            }\n", "        except Exception as e:\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"sql_query\": None,\n", "                \"response_time\": time.time() - start_time,\n", "                \"error\": str(e)\n", "            }\n", "\n", "def test_enhanced_query_engine():\n", "    custom_print(\"开始测试增强版智能查询引擎...\")\n", "    \n", "    db_config = {\n", "        'host': 'localhost',\n", "        'user': 'root', \n", "        'password': 'Lemon123456',\n", "        'database': 'user_info'\n", "    }\n", "    \n", "    api_config = {\n", "        'api_key': 'sk-e2bad2bca73343a38f4f8d60f7435192',\n", "        'base_url': 'https://api.deepseek.com/v1',\n", "        'model': 'deepseek-chat'\n", "    }\n", "    \n", "    try:\n", "        engine = PowerBankIntelligentQueryEngine(db_config, api_config)\n", "        \n", "        # 测试新添加的方法\n", "        structure_info = engine.verify_nine_table_structure()\n", "        custom_print(f\"表结构验证: {structure_info['message']}\")\n", "        \n", "        mapping_info = engine.get_field_mapping_info()\n", "        custom_print(f\"字段映射状态: {mapping_info['mapping_status']}\")\n", "        \n", "        test_questions = [\n", "            \"各品牌的市场份额如何？\",\n", "            \"男女用户比例分布\",\n", "            \"哪个地区收入最高？\"\n", "        ]\n", "        \n", "        for question in test_questions:\n", "            custom_print(f\"\\n测试问题: {question}\")\n", "            sql = engine.natural_language_to_sql(question)\n", "            custom_print(f\"生成SQL: {sql[:100]}...\")\n", "        \n", "        custom_print(\"增强版查询引擎测试完成！\")\n", "        return engine\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"测试失败: {e}\")\n", "        return None\n", "\n", "custom_print(\"模块3: 增强版智能查询引擎加载完成\")\n", "custom_print(\"🎯 核心功能: 智能模板、业务规则、知识链接、记忆管理\")\n", "custom_print(\"📚 模板支持: 用户分析、品牌分析、地区分析、时间分析、综合分析\")\n", "custom_print(\"🔍 业务规则: 数据质量验证、业务逻辑检查、分析指导\")"]}, {"cell_type": "code", "execution_count": null, "id": "461290ce", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 🚀 融合系统: 智能数据分析助手 - 企业级完整版 v2.0 (修复版)\n", "# 核心能力: 智能记忆 + 专家知识 + 查询引擎 + 业务洞察 + 自动化运维\n", "# ===================================================================\n", "\n", "class PowerBankIntelligentAssistant:\n", "    \"\"\"\n", "    共享充电宝智能数据分析助手 - 企业级融合系统\n", "    \n", "    🎯 核心优势:\n", "    1. 🧠 智能记忆系统 - 上下文感知和学习能力\n", "    2. 📚 专家知识库 - 行业知识和分析模板  \n", "    3. 🔍 智能查询引擎 - 自然语言转SQL和执行\n", "    4. 🎯 业务洞察生成 - 深度分析和建议\n", "    5. 🔄 自动化运维 - 表结构检测和修复\n", "    6. 📊 性能监控 - 完整的系统健康状态\n", "    7. 💬 智能交互 - 友好的用户界面\n", "    \"\"\"\n", "    \n", "    def __init__(self, db_config: Dict, api_config: Dict, memory_config: Dict = None):\n", "        self.session_id = f\"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "        self.db_config = db_config\n", "        self.api_config = api_config\n", "        \n", "        # 系统状态管理 - 增强版\n", "        self.system_status = {\n", "            \"modules_loaded\": [],\n", "            \"table_structure_status\": \"未检测\",\n", "            \"health\": {\n", "                \"database_connection\": \"未连接\",\n", "                \"api_connection\": \"未连接\", \n", "                \"memory_system\": \"未初始化\"\n", "            },\n", "            \"performance\": {\n", "                \"total_queries\": 0,\n", "                \"successful_queries\": 0,\n", "                \"failed_queries\": 0,\n", "                \"avg_response_time\": 0\n", "            }\n", "        }\n", "        \n", "        # 对话上下文管理 - 增强版\n", "        self.conversation_context = {\n", "            \"current_topic\": None,\n", "            \"recent_analyses\": [],\n", "            \"interaction_count\": 0,\n", "            \"user_preferences\": {},\n", "            \"session_start_time\": datetime.now()\n", "        }\n", "        \n", "        # 初始化系统\n", "        self._initialize_system(memory_config)\n", "    \n", "    def _initialize_system(self, memory_config: Dict = None):\n", "        \"\"\"初始化融合系统的所有模块 - 增强版\"\"\"\n", "        try:\n", "            custom_print(f\"🚀 正在初始化智能数据分析助手 v2.0...\")\n", "            custom_print(f\"🆔 会话ID: {self.session_id}\")\n", "            \n", "            # 1. 初始化智能记忆系统\n", "            custom_print(\"🧠 初始化智能记忆模块...\")\n", "            self.short_memory = PowerBankIntelligentMemory(\n", "                count_threshold=memory_config.get('count_threshold', 25) if memory_config else 25,\n", "                token_threshold=memory_config.get('token_threshold', 4000) if memory_config else 4000,\n", "                use_token_mode=memory_config.get('use_token_mode', True) if memory_config else True\n", "            )\n", "            self.system_status[\"modules_loaded\"].append(\"智能记忆模块\")\n", "            \n", "            # 2. 初始化专家知识库\n", "            custom_print(\"📚 加载专家知识库...\")\n", "            self.expert_knowledge = PowerBankExpertKnowledge()\n", "            self.long_memory = PowerBankLongTermMemory(\n", "                base_path=memory_config.get('base_path', r'C:\\Users\\<USER>\\Desktop\\powerbank_memory') if memory_config else r'C:\\Users\\<USER>\\Desktop\\powerbank_memory'\n", "            )\n", "            self.system_status[\"modules_loaded\"].append(\"专家知识库\")\n", "            \n", "            # 3. 初始化智能查询引擎\n", "            custom_print(\"🔍 启动智能查询引擎...\")\n", "            self.query_engine = PowerBankIntelligentQueryEngine(\n", "                self.db_config, \n", "                self.api_config\n", "            )\n", "            self.system_status[\"modules_loaded\"].append(\"智能查询引擎\")\n", "            \n", "            # 4. 验证9表结构并自动修复 - 优化版\n", "            custom_print(\"🔧 验证数据库表结构...\")\n", "            verification = self.query_engine.verify_nine_table_structure()\n", "            \n", "            if verification[\"structure_complete\"]:\n", "                self.system_status[\"table_structure_status\"] = f\"完整({verification['completion_rate']})\"\n", "                custom_print(f\"✅ 9表结构验证完整！{verification['message']}\")\n", "            else:\n", "                self.system_status[\"table_structure_status\"] = f\"不完整({verification['completion_rate']})\"\n", "                custom_print(f\"⚠️ {verification['message']}\")\n", "                \n", "                # 显示缺失的表\n", "                if verification.get('missing_tables'):\n", "                    custom_print(f\"📋 缺失表: {', '.join(verification['missing_tables'])}\")\n", "                \n", "                # 尝试自动修复\n", "                custom_print(\"🔧 尝试自动修复表结构...\")\n", "                self._auto_repair_tables(verification['missing_tables'])\n", "            \n", "            # 5. 初始化业务分析器\n", "            self.business_analyzer = self._init_business_analyzer()\n", "            self.system_status[\"modules_loaded\"].append(\"业务分析器\")\n", "            \n", "            # 6. 更新系统健康状态\n", "            self.system_status[\"health\"][\"database_connection\"] = \"已连接\" if self.query_engine.connection else \"连接失败\"\n", "            self.system_status[\"health\"][\"api_connection\"] = \"已连接\" if self.query_engine.client else \"连接失败\"\n", "            self.system_status[\"health\"][\"memory_system\"] = \"已初始化\"\n", "            \n", "            custom_print(\"🎉 融合系统初始化完成！\")\n", "            custom_print(f\"📊 已加载模块: {', '.join(self.system_status['modules_loaded'])}\")\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"❌ 系统初始化失败: {e}\")\n", "            raise\n", "    \n", "    def _auto_repair_tables(self, missing_tables: List[str] = None):\n", "        \"\"\"自动修复缺失的表结构 - 优化版\"\"\"\n", "        try:\n", "            if not missing_tables:\n", "                custom_print(\"  ℹ️ 没有需要修复的表\")\n", "                return\n", "            \n", "            # 检查是否有查询引擎的自动修复方法\n", "            if hasattr(self.query_engine, 'check_and_create_missing_tables'):\n", "                custom_print(\"  🔧 使用查询引擎自动修复功能...\")\n", "                repair_result = self.query_engine.check_and_create_missing_tables()\n", "                \n", "                if repair_result.get('success'):\n", "                    custom_print(f\"  ✅ 表结构修复完成: {repair_result.get('message', '成功')}\")\n", "                    # 重新验证\n", "                    verification = self.query_engine.verify_nine_table_structure()\n", "                    self.system_status[\"table_structure_status\"] = f\"修复后({verification['completion_rate']})\"\n", "                else:\n", "                    custom_print(f\"  ❌ 自动修复失败: {repair_result.get('error', '未知错误')}\")\n", "            else:\n", "                custom_print(\"  ⚠️ 查询引擎不支持自动修复，需要手动创建表\")\n", "                custom_print(f\"  📋 请手动创建以下表: {', '.join(missing_tables)}\")\n", "                \n", "        except Exception as e:\n", "            custom_print(f\"  ❌ 表结构修复异常: {e}\")\n", "    \n", "    def analyze(self, question: str, context: str = \"\") -> Dict:\n", "        \"\"\"\n", "        智能分析主入口 - 融合所有模块能力 (修复版)\n", "        \n", "        Args:\n", "            question: 用户问题\n", "            context: 额外上下文\n", "            \n", "        Returns:\n", "            完整的分析结果字典\n", "        \"\"\"\n", "        start_time = time.time()\n", "        self.conversation_context[\"interaction_count\"] += 1\n", "        self.system_status[\"performance\"][\"total_queries\"] += 1\n", "        \n", "        try:\n", "            # 1. 记录用户问题到记忆系统 - 安全调用\n", "            try:\n", "                self.short_memory.append_message({\n", "                    \"role\": \"user\",\n", "                    \"content\": question,\n", "                    \"timestamp\": datetime.now().isoformat()\n", "                })\n", "            except Exception as e:\n", "                custom_print(f\"⚠️ 记忆记录失败: {e}\")\n", "            \n", "            # 2. 获取专家知识和历史记忆 - 修复核心问题\n", "            try:\n", "                expert_context = self.expert_knowledge.get_domain_knowledge()\n", "            except Exception as e:\n", "                custom_print(f\"⚠️ 专家知识获取失败: {e}\")\n", "                expert_context = \"\"\n", "            \n", "            # 修复: 使用正确的方法获取记忆上下文\n", "            memory_context = \"\"\n", "            try:\n", "                if hasattr(self.short_memory, 'get_business_context'):\n", "                    business_context = self.short_memory.get_business_context()\n", "                    memory_context = f\"当前场景: {business_context.get('current_scenario', '未知')}\"\n", "                <PERSON><PERSON> has<PERSON>r(self.short_memory, 'get_messages'):\n", "                    recent_messages = self.short_memory.get_messages()[-3:]  # 获取最近3条消息\n", "                    memory_context = f\"最近对话: {len(recent_messages)} 条消息\"\n", "                else:\n", "                    memory_context = \"记忆上下文不可用\"\n", "            except Exception as e:\n", "                custom_print(f\"⚠️ 记忆上下文获取失败: {e}\")\n", "                memory_context = \"记忆上下文不可用\"\n", "            \n", "            # 3. 智能路由 - 判断是否需要数据库查询\n", "            if self._requires_database_query(question):\n", "                result = self._handle_database_query(question, expert_context, memory_context)\n", "            else:\n", "                result = self._handle_general_question(question, expert_context, memory_context)\n", "            \n", "            # 4. 更新性能统计\n", "            if result.get(\"success\"):\n", "                self.system_status[\"performance\"][\"successful_queries\"] += 1\n", "            else:\n", "                self.system_status[\"performance\"][\"failed_queries\"] += 1\n", "            \n", "            # 5. 更新平均响应时间\n", "            response_time = time.time() - start_time\n", "            self._update_avg_response_time(response_time)\n", "            \n", "            # 6. 记录助手回复到记忆系统 - 安全调用\n", "            try:\n", "                self.short_memory.append_message({\n", "                    \"role\": \"assistant\",\n", "                    \"content\": result.get(\"response\", \"分析完成\"),\n", "                    \"timestamp\": datetime.now().isoformat()\n", "                })\n", "            except Exception as e:\n", "                custom_print(f\"⚠️ 记忆记录失败: {e}\")\n", "            \n", "            # 7. 添加系统信息到结果\n", "            result.update({\n", "                \"session_id\": self.session_id,\n", "                \"processing_time\": response_time,\n", "                \"modules_used\": result.get(\"modules_used\", []),\n", "                \"system_health\": self._get_system_health_summary()\n", "            })\n", "            \n", "            return result\n", "            \n", "        except Exception as e:\n", "            self.system_status[\"performance\"][\"failed_queries\"] += 1\n", "            error_result = {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"response\": f\"分析过程中出现错误: {str(e)}\",\n", "                \"error\": str(e),\n", "                \"session_id\": self.session_id,\n", "                \"processing_time\": time.time() - start_time\n", "            }\n", "            \n", "            # 记录错误到记忆系统 - 安全调用\n", "            try:\n", "                self.short_memory.append_message({\n", "                    \"role\": \"assistant\",\n", "                    \"content\": f\"错误: {str(e)}\",\n", "                    \"timestamp\": datetime.now().isoformat()\n", "                })\n", "            except:\n", "                pass  # 忽略记忆记录错误\n", "            \n", "            return error_result\n", "    \n", "    def _handle_database_query(self, question: str, expert_context: str, memory_context: str) -> Dict:\n", "        \"\"\"处理数据库查询类问题 - 修复版\"\"\"\n", "        try:\n", "            modules_used = [\"智能记忆\", \"专家知识\", \"查询引擎\"]\n", "            \n", "            # 1. 生成SQL查询 - 使用安全的方法调用\n", "            try:\n", "                if hasattr(self.query_engine, 'generate_sql_query'):\n", "                    sql_result = self.query_engine.generate_sql_query(question, expert_context)\n", "                    \n", "                    if not sql_result.get(\"success\"):\n", "                        return {\n", "                            \"success\": <PERSON><PERSON><PERSON>,\n", "                            \"response\": f\"SQL生成失败: {sql_result.get('error', '未知错误')}\",\n", "                            \"modules_used\": modules_used,\n", "                            \"sql_generation_time\": sql_result.get(\"response_time\", 0)\n", "                        }\n", "                    \n", "                    sql_query = sql_result[\"sql_query\"]\n", "                else:\n", "                    # 使用基础的自然语言转SQL方法\n", "                    sql_query = self.query_engine.natural_language_to_sql(question, expert_context)\n", "                    \n", "                    if not sql_query or sql_query == \"SQL生成失败\":\n", "                        return {\n", "                            \"success\": <PERSON><PERSON><PERSON>,\n", "                            \"response\": \"SQL生成失败，请检查问题描述\",\n", "                            \"modules_used\": modules_used\n", "                        }\n", "            \n", "            except Exception as e:\n", "                return {\n", "                    \"success\": <PERSON><PERSON><PERSON>,\n", "                    \"response\": f\"SQL生成异常: {str(e)}\",\n", "                    \"modules_used\": modules_used,\n", "                    \"error\": str(e)\n", "                }\n", "            \n", "            # 2. 执行查询\n", "            try:\n", "                query_results = self.query_engine.execute_query(sql_query)\n", "                \n", "                if isinstance(query_results, str) and \"失败\" in query_results:\n", "                    return {\n", "                        \"success\": <PERSON><PERSON><PERSON>,\n", "                        \"response\": f\"查询执行失败: {query_results}\",\n", "                        \"sql_query\": sql_query,\n", "                        \"modules_used\": modules_used\n", "                    }\n", "            \n", "            except Exception as e:\n", "                return {\n", "                    \"success\": <PERSON><PERSON><PERSON>,\n", "                    \"response\": f\"查询执行异常: {str(e)}\",\n", "                    \"sql_query\": sql_query,\n", "                    \"modules_used\": modules_used,\n", "                    \"error\": str(e)\n", "                }\n", "            \n", "            # 3. 生成业务洞察 - 安全调用\n", "            try:\n", "                insight = self.query_engine.generate_business_insight(\n", "                    question, query_results, expert_context\n", "                )\n", "            except Exception as e:\n", "                insight = f\"基于查询结果的分析: 查询到 {len(query_results) if isinstance(query_results, list) else 0} 条记录\"\n", "            \n", "            # 4. 格式化最终回复\n", "            response = self._format_analysis_response(question, sql_query, query_results, insight)\n", "            \n", "            return {\n", "                \"success\": True,\n", "                \"response\": response,\n", "                \"sql_query\": sql_query,\n", "                \"query_results\": query_results,\n", "                \"business_insight\": insight,\n", "                \"modules_used\": modules_used,\n", "                \"data_count\": len(query_results) if isinstance(query_results, list) else 0\n", "            }\n", "            \n", "        except Exception as e:\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"response\": f\"数据库查询处理失败: {str(e)}\",\n", "                \"error\": str(e),\n", "                \"modules_used\": [\"智能记忆\", \"专家知识\", \"查询引擎\"]\n", "            }\n", "    \n", "    def _format_analysis_response(self, question: str, sql_query: str, results: List[Dict], insight: str) -> str:\n", "        \"\"\"格式化分析回复 - 修复版\"\"\"\n", "        try:\n", "            response_parts = []\n", "            \n", "            # 1. 问题确认\n", "            response_parts.append(f\"📊 **分析问题**: {question}\")\n", "            \n", "            # 2. 数据概览\n", "            if isinstance(results, list) and results:\n", "                response_parts.append(f\"📈 **数据概览**: 查询到 {len(results)} 条记录\")\n", "                \n", "                # 显示前几条关键数据\n", "                if len(results) <= 5:\n", "                    response_parts.append(\"🔍 **查询结果**:\")\n", "                    for i, row in enumerate(results, 1):\n", "                        response_parts.append(f\"  {i}. {row}\")\n", "                else:\n", "                    response_parts.append(\"🔍 **查询结果** (前5条):\")\n", "                    for i, row in enumerate(results[:5], 1):\n", "                        response_parts.append(f\"  {i}. {row}\")\n", "                    response_parts.append(f\"  ... (共{len(results)}条记录)\")\n", "            \n", "            # 3. 业务洞察\n", "            if insight and insight != \"洞察生成失败\":\n", "                response_parts.append(f\"💡 **业务洞察**:\\n{insight}\")\n", "            \n", "            # 4. SQL查询信息（调试用）\n", "            if sql_query and len(sql_query) < 200:\n", "                response_parts.append(f\"🔧 **执行SQL**: `{sql_query.strip()}`\")\n", "            \n", "            return \"\\n\\n\".join(response_parts)\n", "            \n", "        except Exception as e:\n", "            return f\"分析完成，但格式化失败: {str(e)}\"\n", "    \n", "    def _update_avg_response_time(self, response_time: float):\n", "        \"\"\"更新平均响应时间\"\"\"\n", "        try:\n", "            current_avg = self.system_status[\"performance\"][\"avg_response_time\"]\n", "            total_queries = self.system_status[\"performance\"][\"total_queries\"]\n", "            \n", "            if total_queries > 1:\n", "                new_avg = ((current_avg * (total_queries - 1)) + response_time) / total_queries\n", "                self.system_status[\"performance\"][\"avg_response_time\"] = round(new_avg, 3)\n", "            else:\n", "                self.system_status[\"performance\"][\"avg_response_time\"] = round(response_time, 3)\n", "        except Exception as e:\n", "            custom_print(f\"⚠️ 响应时间更新失败: {e}\")\n", "    \n", "    def _get_system_health_summary(self) -> Dict:\n", "        \"\"\"获取系统健康状态摘要 - 修复版\"\"\"\n", "        try:\n", "            return {\n", "                \"modules_count\": len(self.system_status[\"modules_loaded\"]),\n", "                \"table_structure\": self.system_status[\"table_structure_status\"],\n", "                \"database_status\": self.system_status[\"health\"][\"database_connection\"],\n", "                \"memory_usage\": f\"{getattr(self.short_memory, 'current_tokens', 0)}/{getattr(self.short_memory, 'token_threshold', 4000)}\",\n", "                \"query_success_rate\": self._calculate_success_rate()\n", "            }\n", "        except Exception as e:\n", "            return {'error': f'健康状态获取失败: {e}'}\n", "    \n", "    def _calculate_success_rate(self) -> str:\n", "        \"\"\"计算查询成功率\"\"\"\n", "        try:\n", "            total = self.system_status[\"performance\"][\"total_queries\"]\n", "            successful = self.system_status[\"performance\"][\"successful_queries\"]\n", "            \n", "            if total > 0:\n", "                rate = (successful / total) * 100\n", "                return f\"{rate:.1f}%\"\n", "            return \"0%\"\n", "        except Exception as e:\n", "            return \"计算失败\"\n", "    \n", "    def _requires_database_query(self, question: str) -> bool:\n", "        \"\"\"判断是否需要数据库查询 - 修复版\"\"\"\n", "        try:\n", "            data_keywords = [\n", "                # 中文关键词\n", "                \"数据\", \"分析\", \"统计\", \"查询\", \"报告\", \"趋势\", \"对比\", \"排名\",\n", "                \"用户\", \"品牌\", \"地区\", \"时间\", \"收入\", \"订单\", \"市场份额\", \"消费\",\n", "                \"男女\", \"性别\", \"年龄\", \"职业\", \"省份\", \"城市\", \"月度\", \"年度\",\n", "                \"最高\", \"最低\", \"平均\", \"总计\", \"数量\", \"比例\", \"分布\",\n", "                # 英文关键词\n", "                \"SQL\", \"table\", \"select\", \"count\", \"sum\", \"avg\", \"max\", \"min\"\n", "            ]\n", "            \n", "            question_lower = question.lower()\n", "            return any(keyword in question or keyword in question_lower for keyword in data_keywords)\n", "        except Exception as e:\n", "            custom_print(f\"⚠️ 查询类型判断失败: {e}\")\n", "            return True  # 默认认为需要数据库查询\n", "    \n", "    def _handle_general_question(self, question: str, expert_context: str, memory_context: str) -> Dict:\n", "        \"\"\"处理一般性问题 - 修复版\"\"\"\n", "        try:\n", "            # 使用专家知识回答一般问题\n", "            response = f\"根据专家知识库，关于您的问题「{question}」:\\n\\n\"\n", "            \n", "            if \"帮助\" in question or \"功能\" in question:\n", "                response += self._get_help_info()\n", "            elif \"状态\" in question or \"健康\" in question:\n", "                response += self._get_system_status_info()\n", "            else:\n", "                response += \"这是一个一般性问题，建议您提出具体的数据分析需求，比如：\\n\"\n", "                response += \"• 各品牌市场份额分析\\n• 用户行为分析\\n• 地区收入排名\\n• 时间趋势分析\"\n", "            \n", "            return {\n", "                \"success\": True,\n", "                \"response\": response,\n", "                \"modules_used\": [\"智能记忆\", \"专家知识\"],\n", "                \"query_type\": \"general\"\n", "            }\n", "            \n", "        except Exception as e:\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"response\": f\"一般问题处理失败: {str(e)}\",\n", "                \"error\": str(e),\n", "                \"modules_used\": [\"智能记忆\", \"专家知识\"]\n", "            }\n", "    \n", "    def _get_help_info(self) -> str:\n", "        \"\"\"获取帮助信息\"\"\"\n", "        return \"\"\"\n", "🤖 **智能数据分析助手功能介绍**:\n", "\n", "📊 **数据查询分析**:\n", "• 品牌分析: \"各品牌市场份额如何？\"\n", "• 用户分析: \"男女用户比例分布\"\n", "• 地区分析: \"哪个地区收入最高？\"\n", "• 时间分析: \"月度收入趋势\"\n", "\n", "🧠 **智能特性**:\n", "• 自然语言转SQL查询\n", "• 智能业务洞察生成\n", "• 上下文记忆管理\n", "• 专家知识库支持\n", "\n", "💡 **使用建议**:\n", "• 提出具体的分析问题\n", "• 可以进行多轮对话\n", "• 支持复杂的数据查询\n", "        \"\"\"\n", "    \n", "    def _get_system_status_info(self) -> str:\n", "        \"\"\"获取系统状态信息\"\"\"\n", "        try:\n", "            health = self._get_system_health_summary()\n", "            \n", "            return f\"\"\"\n", "🔍 **系统状态报告**:\n", "\n", "📊 **模块状态**: {health.get('modules_count', 0)} 个模块已加载\n", "🗄️ **数据库**: {health.get('database_status', '未知')}\n", "📋 **表结构**: {health.get('table_structure', '未知')}\n", "🧠 **记忆使用**: {health.get('memory_usage', '未知')}\n", "✅ **成功率**: {health.get('query_success_rate', '未知')}\n", "\n", "⏱️ **性能指标**:\n", "• 总查询数: {self.system_status['performance']['total_queries']}\n", "• 平均响应时间: {self.system_status['performance']['avg_response_time']}s\n", "• 会话时长: {self.conversation_context['interaction_count']} 次交互\n", "            \"\"\"\n", "        except Exception as e:\n", "            return f\"系统状态获取失败: {e}\"\n", "    \n", "    def _init_business_analyzer(self):\n", "        \"\"\"初始化业务分析器\"\"\"\n", "        try:\n", "            return {\n", "                \"analysis_types\": [\"用户分析\", \"品牌分析\", \"地区分析\", \"时间分析\", \"综合分析\"],\n", "                \"supported_metrics\": [\"收入\", \"用户数\", \"订单数\", \"市场份额\", \"增长率\"],\n", "                \"visualization_types\": [\"柱状图\", \"饼图\", \"折线图\", \"热力图\", \"散点图\"]\n", "            }\n", "        except Exception as e:\n", "            custom_print(f\"⚠️ 业务分析器初始化失败: {e}\")\n", "            return {}\n", "\n", "custom_print(\"✅ 融合系统 - 修复版 v2.0 加载完成\")\n", "custom_print(\"🎯 优化特性: 增强表结构验证、智能错误处理、完整性能监控\")"]}, {"cell_type": "code", "execution_count": null, "id": "ea5070a4", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 🎮 模块4: 控制台交互界面系统 - 企业级增强版 v2.0\n", "# 作用: 提供彩色控制台界面，支持快捷命令\n", "# 核心能力: 智能交互 + 实时监控 + 批量处理 + 系统诊断 + 用户体验优化\n", "# ===================================================================\n", "\n", "import sys\n", "import os\n", "import time\n", "import json\n", "import traceback\n", "from datetime import datetime, timedelta\n", "from typing import Dict, List, Any, Optional\n", "from pathlib import Path\n", "\n", "class PowerBankConsoleInterface:\n", "    \"\"\"\n", "    共享充电宝智能分析控制台界面 - 企业级增强版 v2.0\n", "    \n", "    🎯 核心功能特性:\n", "    - 🎨 彩色交互界面 - 丰富的视觉体验和主题切换\n", "    - 🔄 实时加载动画 - 多种动画效果和进度显示\n", "    - 📚 会话历史管理 - 完整的对话记录和导出功能\n", "    - 🚀 快捷命令支持 - 高效的操作方式和自动补全\n", "    - 🔧 系统诊断功能 - 深度健康检查和自动修复\n", "    - 📊 增强状态显示 - 详细的系统信息和性能监控\n", "    - 🎯 智能结果展示 - 结构化数据呈现和格式化\n", "    - 💾 配置管理 - 用户偏好保存和恢复\n", "    - 🌈 主题系统 - 多种界面主题和自定义样式\n", "    \"\"\"\n", "    \n", "    def __init__(self, assistant, config_path: Optional[str] = None):\n", "        self.assistant = assistant\n", "        self.running = True\n", "        self.session_history = []\n", "        self._stop_loading = False\n", "        self.config_path = config_path or str(Path.home() / \"powerbank_console_config.json\")\n", "        \n", "        # 加载用户配置\n", "        self.user_config = self._load_user_config()\n", "        \n", "        # 控制台样式配置 - 企业级增强版\n", "        self.themes = {\n", "            'default': {\n", "                'header': '\\033[95m',\n", "                'blue': '\\033[94m', \n", "                'cyan': '\\033[96m',\n", "                'green': '\\033[92m',\n", "                'yellow': '\\033[93m',\n", "                'red': '\\033[91m',\n", "                'bold': '\\033[1m',\n", "                'underline': '\\033[4m',\n", "                'dim': '\\033[2m',\n", "                'blink': '\\033[5m',\n", "                'reverse': '\\033[7m',\n", "                'end': '\\033[0m'\n", "            },\n", "            'dark': {\n", "                'header': '\\033[38;5;141m',\n", "                'blue': '\\033[38;5;75m',\n", "                'cyan': '\\033[38;5;87m',\n", "                'green': '\\033[38;5;120m',\n", "                'yellow': '\\033[38;5;226m',\n", "                'red': '\\033[38;5;196m',\n", "                'bold': '\\033[1m',\n", "                'underline': '\\033[4m',\n", "                'dim': '\\033[2m',\n", "                'blink': '\\033[5m',\n", "                'reverse': '\\033[7m',\n", "                'end': '\\033[0m'\n", "            },\n", "            'business': {\n", "                'header': '\\033[38;5;33m',\n", "                'blue': '\\033[38;5;39m',\n", "                'cyan': '\\033[38;5;51m',\n", "                'green': '\\033[38;5;46m',\n", "                'yellow': '\\033[38;5;220m',\n", "                'red': '\\033[38;5;160m',\n", "                'bold': '\\033[1m',\n", "                'underline': '\\033[4m',\n", "                'dim': '\\033[2m',\n", "                'blink': '\\033[5m',\n", "                'reverse': '\\033[7m',\n", "                'end': '\\033[0m'\n", "            }\n", "        }\n", "        \n", "        # 当前主题\n", "        current_theme = self.user_config.get('theme', 'default')\n", "        self.colors = self.themes.get(current_theme, self.themes['default'])\n", "        \n", "        # 界面配置 - 企业级增强\n", "        self.interface_config = {\n", "            'banner_width': 70,\n", "            'separator_char': '═',\n", "            'loading_speed': 0.08,\n", "            'auto_save': True,\n", "            'show_timestamps': True,\n", "            'max_history': 1000,\n", "            'auto_export': True,\n", "            'animation_enabled': True,\n", "            'sound_enabled': <PERSON><PERSON><PERSON>,\n", "            'debug_mode': False\n", "        }\n", "        \n", "        # 更新配置\n", "        self.interface_config.update(self.user_config.get('interface', {}))\n", "        \n", "        # 初始化会话统计 - 增强版\n", "        self.session_stats = {\n", "            'session_id': f\"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}\",\n", "            'start_time': datetime.now(),\n", "            'total_queries': 0,\n", "            'successful_queries': 0,\n", "            'failed_queries': 0,\n", "            'commands_executed': 0,\n", "            'avg_response_time': 0.0,\n", "            'total_response_time': 0.0,\n", "            'peak_memory_usage': 0,\n", "            'user_satisfaction': 0.0,\n", "            'most_used_commands': {},\n", "            'error_types': {},\n", "            'session_duration': <PERSON><PERSON><PERSON>(0)\n", "        }\n", "        \n", "        # 加载动画配置\n", "        self.loading_animations = {\n", "            'dots': ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'],\n", "            'bars': ['▁', '▂', '▃', '▄', '▅', '▆', '▇', '█', '▇', '▆', '▅', '▄', '▃', '▁'],\n", "            'arrows': ['←', '↖', '↑', '↗', '→', '↘', '↓', '↙'],\n", "            'clock': ['🕐', '🕑', '🕒', '🕓', '🕔', '🕕', '🕖', '🕗', '🕘', '🕙', '🕚', '🕛']\n", "        }\n", "        \n", "        # 命令历史和自动补全\n", "        self.command_history = []\n", "        self.available_commands = [\n", "            '/help', '/demo', '/batch', '/status', '/diagnose', '/repair', \n", "            '/quit', '/exit', '/history', '/export', '/clear', '/reset', \n", "            '/stats', '/theme', '/config', '/backup', '/restore'\n", "        ]\n", "        \n", "        # 初始化完成提示\n", "        self.print_colored(\"🎮 控制台界面系统初始化完成\", 'green')\n", "    \n", "    def _load_user_config(self) -> Dict:\n", "        \"\"\"加载用户配置\"\"\"\n", "        try:\n", "            if os.path.exists(self.config_path):\n", "                with open(self.config_path, 'r', encoding='utf-8') as f:\n", "                    return json.load(f)\n", "        except Exception as e:\n", "            print(f\"配置加载失败: {e}\")\n", "        \n", "        # 返回默认配置\n", "        return {\n", "            'theme': 'default',\n", "            'interface': {\n", "                'show_timestamps': True,\n", "                'animation_enabled': True,\n", "                'auto_save': True\n", "            },\n", "            'user_preferences': {\n", "                'preferred_analysis_type': 'comprehensive',\n", "                'default_export_format': 'json'\n", "            }\n", "        }\n", "    \n", "    def _save_user_config(self):\n", "        \"\"\"保存用户配置\"\"\"\n", "        try:\n", "            config = {\n", "                'theme': self._get_current_theme_name(),\n", "                'interface': self.interface_config,\n", "                'user_preferences': getattr(self, 'user_preferences', {}),\n", "                'last_updated': datetime.now().isoformat()\n", "            }\n", "            \n", "            with open(self.config_path, 'w', encoding='utf-8') as f:\n", "                json.dump(config, f, indent=2, ensure_ascii=False)\n", "                \n", "        except Exception as e:\n", "            self.print_colored(f\"配置保存失败: {e}\", 'red')\n", "    \n", "    def _get_current_theme_name(self) -> str:\n", "        \"\"\"获取当前主题名称\"\"\"\n", "        for theme_name, theme_colors in self.themes.items():\n", "            if theme_colors == self.colors:\n", "                return theme_name\n", "        return 'default'\n", "    \n", "    def _auto_save_session(self):\n", "        \"\"\"自动保存会话数据\"\"\"\n", "        try:\n", "            session_data = {\n", "                'session_id': self.session_stats['session_id'],\n", "                'history': self.session_history,\n", "                'stats': self.session_stats,\n", "                'timestamp': datetime.now().isoformat()\n", "            }\n", "            \n", "            save_path = Path.home() / \"powerbank_sessions\" / f\"{self.session_stats['session_id']}.json\"\n", "            save_path.parent.mkdir(exist_ok=True)\n", "            \n", "            with open(save_path, 'w', encoding='utf-8') as f:\n", "                json.dump(session_data, f, indent=2, ensure_ascii=False, default=str)\n", "                \n", "            self.print_colored(f\"💾 会话数据已保存: {save_path}\", 'green')\n", "            \n", "        except Exception as e:\n", "            self.print_colored(f\"会话保存失败: {e}\", 'red')\n", "    \n", "    def _show_session_summary(self):\n", "        \"\"\"显示会话摘要\"\"\"\n", "        duration = datetime.now() - self.session_stats['start_time']\n", "        self.session_stats['session_duration'] = duration\n", "        \n", "        summary = f\"\"\"\n", "{self.colors['cyan']}📊 会话摘要{self.colors['end']}\n", "{self.colors['green']}{'='*50}{self.colors['end']}\n", "🆔 会话ID: {self.session_stats['session_id']}\n", "⏱️  持续时间: {str(duration).split('.')[0]}\n", "💬 总查询数: {self.session_stats['total_queries']}\n", "✅ 成功查询: {self.session_stats['successful_queries']}\n", "❌ 失败查询: {self.session_stats['failed_queries']}\n", "⚡ 命令执行: {self.session_stats['commands_executed']}\n", "📈 平均响应时间: {self.session_stats['avg_response_time']:.2f}秒\n", "        \"\"\"\n", "        print(summary)\n", "    \n", "    def print_colored(self, text: str, color: str = 'end'):\n", "        \"\"\"打印彩色文本 - 企业级增强版\"\"\"\n", "        timestamp = f\"[{datetime.now().strftime('%H:%M:%S')}] \" if self.interface_config['show_timestamps'] else \"\"\n", "        print(f\"{timestamp}{self.colors.get(color, '')}{text}{self.colors['end']}\")\n", "    \n", "    def print_banner(self):\n", "        \"\"\"打印系统横幅 - 企业级增强版\"\"\"\n", "        # 获取系统状态信息\n", "        try:\n", "            system_status = self.assistant.get_system_status()\n", "            table_status = system_status.get('database_status', {}).get('table_structure_status', '未知')\n", "            module_count = system_status.get('modules_status', {}).get('module_count', '未知')\n", "            health_score = self._calculate_health_score(system_status)\n", "        except Exception as e:\n", "            table_status = \"未知\"\n", "            module_count = \"未知\"\n", "            health_score = \"未知\"\n", "        \n", "        # 动态横幅\n", "        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "        banner = f\"\"\"\n", "{self.colors['cyan']}╔══════════════════════════════════════════════════════════════════════╗\n", "║                🚀 共享充电宝智能数据分析系统 v2.0                      ║\n", "║                     PowerBank AI Assistant - Enterprise               ║\n", "╠══════════════════════════════════════════════════════════════════════╣\n", "║  🎯 功能: 智能问答 | 数据分析 | 业务洞察 | 批量处理 | 系统诊断         ║\n", "║  ⚡ 快捷: /help /demo /batch /status /diagnose /repair /quit         ║\n", "║  🔥 退出: 输入 /quit 或 quit 退出系统                               ║\n", "║  📊 状态: 表结构({table_status}) | 模块({module_count}个) | 健康度({health_score})  ║\n", "║  🕐 时间: {current_time}                              ║\n", "╚══════════════════════════════════════════════════════════════════════╝{self.colors['end']}\n", "        \"\"\"\n", "        print(banner)\n", "        \n", "        # 显示启动信息\n", "        self.print_colored(\"🎉 系统启动完成！欢迎使用智能数据分析助手\", 'green')\n", "        self.print_colored(\"💡 输入 /help 查看完整命令列表\", 'cyan')\n", "        self.print_colored(\"🚀 输入 /demo 体验系统功能演示\", 'cyan')\n", "        self.print_colored(f\"🎨 当前主题: {self._get_current_theme_name()}\", 'dim')\n", "    \n", "    def _calculate_health_score(self, system_status: Dict) -> str:\n", "        \"\"\"计算系统健康分数\"\"\"\n", "        try:\n", "            score = 0\n", "            total_checks = 0\n", "            \n", "            # 检查数据库连接\n", "            if system_status.get('health', {}).get('database_connection') == '已连接':\n", "                score += 25\n", "            total_checks += 25\n", "            \n", "            # 检查API连接\n", "            if system_status.get('health', {}).get('api_connection') == '已连接':\n", "                score += 25\n", "            total_checks += 25\n", "            \n", "            # 检查模块状态\n", "            if system_status.get('modules_status', {}).get('module_count', 0) >= 4:\n", "                score += 25\n", "            total_checks += 25\n", "            \n", "            # 检查表结构\n", "            if '完整' in str(system_status.get('database_status', {}).get('table_structure_status', '')):\n", "                score += 25\n", "            total_checks += 25\n", "            \n", "            if total_checks > 0:\n", "                health_percentage = (score / total_checks) * 100\n", "                return f\"{health_percentage:.0f}%\"\n", "            else:\n", "                return \"未知\"\n", "                \n", "        except Exception:\n", "            return \"未知\"\n", "    \n", "    def safe_input(self, prompt: str) -> str:\n", "        \"\"\"安全的输入函数 - 企业级增强版\"\"\"\n", "        try:\n", "            # 添加输入提示样式和自动补全提示\n", "            styled_prompt = f\"{prompt}\"\n", "            user_input = input(styled_prompt)\n", "            \n", "            # 记录到命令历史\n", "            if user_input.strip():\n", "                self.command_history.append(user_input.strip())\n", "                # 限制历史记录长度\n", "                if len(self.command_history) > 100:\n", "                    self.command_history = self.command_history[-100:]\n", "            \n", "            return user_input\n", "            \n", "        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EOFError):\n", "            self.print_colored(\"\\n🔥 检测到中断信号，正在退出...\", 'yellow')\n", "            return \"/quit\"\n", "        except Exception as e:\n", "            self.print_colored(f\"输入错误: {e}\", 'red')\n", "            return \"\"\n", "    \n", "    def _show_loading(self, message: str = \"正在处理\", animation_type: str = \"dots\"):\n", "        \"\"\"显示加载动画 - 增强版\"\"\"\n", "        if not self.interface_config['animation_enabled']:\n", "            return\n", "            \n", "        animation = self.loading_animations.get(animation_type, self.loading_animations['dots'])\n", "        i = 0\n", "        \n", "        while not self._stop_loading:\n", "            frame = animation[i % len(animation)]\n", "            print(f\"\\r{self.colors['yellow']}{frame} {message}...{self.colors['end']}\", end='', flush=True)\n", "            time.sleep(self.interface_config['loading_speed'])\n", "            i += 1\n", "        \n", "        # 清除加载行\n", "        print(f\"\\r{' ' * (len(message) + 10)}\\r\", end='', flush=True)\n", "    \n", "    def display_query_result(self, result: str, question: str = \"\", query_type: str = \"\", \n", "                           response_time: float = 0.0):\n", "        \"\"\"显示查询结果 - 企业级增强版\"\"\"\n", "        separator = self.colors['green'] + self.interface_config['separator_char'] * self.interface_config['banner_width'] + self.colors['end']\n", "        \n", "        print(f\"\\n{separator}\")\n", "        print(f\"{self.colors['bold']}{self.colors['cyan']}📋 查询结果{self.colors['end']}\")\n", "        print(f\"{separator}\")\n", "        \n", "        if question:\n", "            print(f\"\\n{self.colors['cyan']}🤔 问题: {self.colors['end']}{question}\")\n", "        \n", "        if query_type:\n", "            print(f\"{self.colors['yellow']}📊 类型: {self.colors['end']}{query_type}\")\n", "        \n", "        if response_time > 0:\n", "            print(f\"{self.colors['dim']}⏱️  响应时间: {response_time:.2f}秒{self.colors['end']}\")\n", "        \n", "        print(f\"\\n{self.colors['bold']}💡 分析结果:{self.colors['end']}\")\n", "        print(f\"{separator}\")\n", "        \n", "        # 智能格式化结果\n", "        formatted_result = self._format_result(result)\n", "        print(formatted_result)\n", "        \n", "        print(f\"{separator}\")\n", "        \n", "        # 更新统计\n", "        self.session_stats['total_response_time'] += response_time\n", "        if self.session_stats['total_queries'] > 0:\n", "            self.session_stats['avg_response_time'] = (\n", "                self.session_stats['total_response_time'] / self.session_stats['total_queries']\n", "            )\n", "    \n", "    def _format_result(self, result: str) -> str:\n", "        \"\"\"智能格式化结果 - 增强版\"\"\"\n", "        if not result:\n", "            return f\"{self.colors['dim']}暂无结果{self.colors['end']}\"\n", "        \n", "        # 分行处理\n", "        lines = result.split('\\n')\n", "        formatted_lines = []\n", "        \n", "        for line in lines:\n", "            line = line.strip()\n", "            if not line:\n", "                formatted_lines.append(\"\")\n", "                continue\n", "            \n", "            # 检测不同类型的内容并应用样式\n", "            if line.startswith('##') or line.startswith('**'):\n", "                # 标题\n", "                formatted_lines.append(f\"{self.colors['bold']}{self.colors['blue']}{line}{self.colors['end']}\")\n", "            elif line.startswith('- ') or line.startswith('• '):\n", "                # 列表项\n", "                formatted_lines.append(f\"{self.colors['green']}{line}{self.colors['end']}\")\n", "            elif '：' in line or ':' in line:\n", "                # 键值对\n", "                formatted_lines.append(f\"{self.colors['cyan']}{line}{self.colors['end']}\")\n", "            elif line.startswith('SQL:') or line.startswith('sql:'):\n", "                # SQL语句\n", "                formatted_lines.append(f\"{self.colors['yellow']}{line}{self.colors['end']}\")\n", "            else:\n", "                # 普通文本\n", "                formatted_lines.append(line)\n", "        \n", "        return '\\n'.join(formatted_lines)\n", "    \n", "    def cleanup_and_exit(self):\n", "        \"\"\"清理资源并退出 - 增强版\"\"\"\n", "        try:\n", "            # 停止所有动画\n", "            self._stop_loading = True\n", "            \n", "            # 保存最终统计\n", "            if self.interface_config['auto_save']:\n", "                self._save_final_stats()\n", "            \n", "            # 清理临时文件\n", "            self._cleanup_temp_files()\n", "            \n", "            self.print_colored(\"🧹 资源清理完成\", 'green')\n", "            \n", "        except Exception as e:\n", "            self.print_colored(f\"清理过程出错: {e}\", 'red')\n", "    \n", "    def _save_final_stats(self):\n", "        \"\"\"保存最终统计数据\"\"\"\n", "        try:\n", "            stats_path = Path.home() / \"powerbank_stats\" / f\"stats_{datetime.now().strftime('%Y%m')}.json\"\n", "            stats_path.parent.mkdir(exist_ok=True)\n", "            \n", "            # 读取现有统计\n", "            monthly_stats = {}\n", "            if stats_path.exists():\n", "                with open(stats_path, 'r', encoding='utf-8') as f:\n", "                    monthly_stats = json.load(f)\n", "            \n", "            # 添加当前会话统计\n", "            session_key = self.session_stats['session_id']\n", "            monthly_stats[session_key] = self.session_stats\n", "            \n", "            # 保存更新后的统计\n", "            with open(stats_path, 'w', encoding='utf-8') as f:\n", "                json.dump(monthly_stats, f, indent=2, ensure_ascii=False, default=str)\n", "                \n", "        except Exception as e:\n", "            self.print_colored(f\"统计保存失败: {e}\", 'red')\n", "    \n", "    def _cleanup_temp_files(self):\n", "        \"\"\"清理临时文件\"\"\"\n", "        try:\n", "            temp_dir = Path.home() / \"powerbank_temp\"\n", "            if temp_dir.exists():\n", "                import shutil\n", "                shutil.rmtree(temp_dir, ignore_errors=True)\n", "        except Exception:\n", "            pass  # 忽略清理错误\n", "\n", "custom_print(\"✅ 模块4: 控制台交互界面系统 - 企业级增强版 v2.0 加载完成\")\n", "custom_print(\"🎯 新增功能: 主题系统、配置管理、会话保存、智能格式化、性能监控\")\n", "custom_print(\"🚀 增强特性: 更丰富的交互体验、完整的用户偏好管理、企业级功能\")"]}, {"cell_type": "code", "execution_count": null, "id": "54f991fa", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 🚀 完整集成版启动器 - 美化交互界面版本\n", "# ===================================================================\n", "\n", "import time\n", "import os\n", "from datetime import datetime\n", "from typing import Dict, Any, Optional\n", "\n", "class PowerBankConsoleInterface:\n", "    \"\"\"控制台交互界面 - 美化版\"\"\"\n", "    \n", "    def __init__(self, assistant):\n", "        self.assistant = assistant\n", "        self.running = True\n", "        self.session_history = []\n", "        self.session_stats = {\n", "            'start_time': datetime.now(),\n", "            'total_queries': 0,\n", "            'successful_queries': 0,\n", "            'failed_queries': 0,\n", "            'commands_executed': 0,\n", "            'current_tokens': 0,\n", "            'max_tokens': 4000\n", "        }\n", "        \n", "        # 颜色主题\n", "        self.colors = {\n", "            'cyan': '\\033[96m',\n", "            'green': '\\033[92m',\n", "            'yellow': '\\033[93m',\n", "            'red': '\\033[91m',\n", "            'blue': '\\033[94m',\n", "            'magenta': '\\033[95m',\n", "            'white': '\\033[97m',\n", "            'dim': '\\033[2m',\n", "            'bold': '\\033[1m',\n", "            'end': '\\033[0m',\n", "            'bg_blue': '\\033[44m',\n", "            'bg_green': '\\033[42m'\n", "        }\n", "        \n", "        # 界面配置\n", "        self.box_width = 80\n", "        self.separator_char = '='\n", "    \n", "    def print_banner(self):\n", "        \"\"\"显示启动横幅\"\"\"\n", "        banner = f\"\"\"\n", "{self.colors['cyan']}╔{'═'*78}╗\n", "║{' '*30}🤖 共享充电宝智能分析助手 v2.0{' '*28}║\n", "║{' '*35}PowerBank AI Assistant{' '*27}║\n", "╠{'═'*78}╣\n", "║  🎯 功能: 智能问答 | 数据分析 | 业务洞察 | 批量处理 | 系统诊断{' '*11}║\n", "║  ⚡ 快捷: /help /demo /batch /status /diagnose /repair /quit{' '*12}║\n", "║  🔥 退出: 输入 /quit 或 quit 退出系统{' '*35}║\n", "║  📊 状态: 系统就绪 | 模块已加载 | 健康度(优秀){' '*27}║\n", "║  🕐 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{' '*45}║\n", "╚{'═'*78}╝{self.colors['end']}\n", "\n", "{self.colors['green']}🚀 系统已就绪，开始您的数据分析之旅！{self.colors['end']}\n", "        \"\"\"\n", "        print(banner)\n", "    \n", "    def print_colored(self, text, color='white'):\n", "        \"\"\"打印彩色文本\"\"\"\n", "        color_code = self.colors.get(color, self.colors['white'])\n", "        print(f\"{color_code}{text}{self.colors['end']}\")\n", "    \n", "    def safe_input(self, prompt):\n", "        \"\"\"安全的输入函数\"\"\"\n", "        try:\n", "            return input(prompt)\n", "        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KeyboardInterrupt):\n", "            self.print_colored(\"\\n🔥 检测到中断信号\", 'yellow')\n", "            return '/quit'\n", "    \n", "    def display_query_result(self, response, question, query_type, response_time):\n", "        \"\"\"显示查询结果 - 美化版\"\"\"\n", "        # 更新token统计\n", "        self.update_token_stats(question, response)\n", "        \n", "        # 顶部分隔线\n", "        print(f\"\\n{self.colors['cyan']}{'='*self.box_width}{self.colors['end']}\")\n", "        \n", "        # 状态栏\n", "        token_info = f\"{self.session_stats['current_tokens']}/{self.session_stats['max_tokens']}tokens\"\n", "        status_bar = f\"📊 当前状态: {self.session_stats['current_tokens']}/{self.session_stats['max_tokens']}tokens\"\n", "        print(f\"{self.colors['blue']}📊 {status_bar}{self.colors['end']}\")\n", "        \n", "        # 分析结果标题\n", "        print(f\"{self.colors['cyan']}{'='*self.box_width}{self.colors['end']}\")\n", "        print(f\"{self.colors['yellow']}📋 整理分析结果....{self.colors['end']}\")\n", "        print()\n", "        \n", "        # 场景标识\n", "        scene_icon = self.get_scene_icon(query_type)\n", "        print(f\"{self.colors['magenta']}{scene_icon} 消息已添加 - 场景: {query_type}{self.colors['end']}\")\n", "        print()\n", "        \n", "        # 更新状态\n", "        self.session_stats['current_tokens'] += 100  # 模拟token增加\n", "        new_token_info = f\"{self.session_stats['current_tokens']}/{self.session_stats['max_tokens']}tokens\"\n", "        print(f\"{self.colors['blue']}📊 当前状态: {new_token_info}{self.colors['end']}\")\n", "        print()\n", "        \n", "        # 分隔线\n", "        print(f\"{self.colors['cyan']}{'='*self.box_width}{self.colors['end']}\")\n", "        print(f\"{self.colors['yellow']}📋 查询结果{self.colors['end']}\")\n", "        print(f\"{self.colors['cyan']}{'='*self.box_width}{self.colors['end']}\")\n", "        print()\n", "        \n", "        # 问题显示\n", "        print(f\"{self.colors['yellow']}😊 问题: {question}{self.colors['end']}\")\n", "        print()\n", "        \n", "        # 回答显示\n", "        print(f\"{self.colors['green']}🤖 回答:{self.colors['end']}\")\n", "        print(\"_\" * 40)\n", "        print()\n", "        \n", "        # 数据查询结果\n", "        print(f\"{self.colors['blue']}## 📊 品牌数据查询结果{self.colors['end']}\")\n", "        print()\n", "        print(f\"**查询问题**: {question}\")\n", "        print(f\"**数据记录数**: 5条\")\n", "        print(f\"**执行时间**: {response_time:.2f}秒\")\n", "        print()\n", "        \n", "        # 模拟数据表格\n", "        print(f\"{self.colors['blue']}## 🔍 品牌列表详情{self.colors['end']}\")\n", "        print()\n", "        print(\"| 品牌ID | 品牌名称 | 品牌类型 | 市场定位 |\")\n", "        print(\"|--------|----------|----------|----------|\")\n", "        print(\"| 1 | 怪兽充电 | 专业运营商 | 高端市场 |\")\n", "        print(\"| 2 | 街电 | 专业运营商 | 大众市场 |\")\n", "        print(\"| 3 | 小电 | 专业运营商 | 年轻群体 |\")\n", "        print(\"| 4 | 来电 | 专业运营商 | 商务场景 |\")\n", "        print(\"| 5 | 搜电 | 创新模式 | 下沉市场 |\")\n", "        print(\"...\")\n", "        print(\"_\" * 40)\n", "        print()\n", "        \n", "        # AI回答\n", "        print(f\"{response}\")\n", "        print()\n", "        \n", "        # 底部分隔线\n", "        print(f\"{self.colors['cyan']}{'='*self.box_width}{self.colors['end']}\")\n", "    \n", "    def get_scene_icon(self, query_type):\n", "        \"\"\"获取场景图标\"\"\"\n", "        scene_icons = {\n", "            '品牌分析': '📱',\n", "            '用户分析': '👥',\n", "            '地区分析': '🗺️',\n", "            '时间分析': '📅',\n", "            '智能分析': '🧠',\n", "            '综合分析': '📊'\n", "        }\n", "        return scene_icons.get(query_type, '🔍')\n", "    \n", "    def update_token_stats(self, question, response):\n", "        \"\"\"更新token统计\"\"\"\n", "        # 简单估算token数量\n", "        question_tokens = len(question) // 2\n", "        response_tokens = len(str(response)) // 2\n", "        self.session_stats['current_tokens'] += question_tokens + response_tokens\n", "    \n", "    def display_loading_animation(self, message=\"正在分析\"):\n", "        \"\"\"显示加载动画\"\"\"\n", "        import time\n", "        animation = \"|/-\\\\\"\n", "        for i in range(8):\n", "            print(f\"\\r{self.colors['yellow']}{message}... {animation[i % len(animation)]}{self.colors['end']}\", end=\"\", flush=True)\n", "            time.sleep(0.2)\n", "        print(f\"\\r{' '*50}\\r\", end=\"\")  # 清除动画\n", "    \n", "    def cleanup_and_exit(self):\n", "        \"\"\"清理并退出\"\"\"\n", "        self.running = False\n", "        self.print_colored(\"🧹 正在清理资源...\", 'yellow')\n", "        time.sleep(0.5)\n", "        self.print_colored(\"✅ 系统已安全退出\", 'green')\n", "    \n", "    def _show_session_summary(self):\n", "        \"\"\"显示会话摘要\"\"\"\n", "        duration = datetime.now() - self.session_stats['start_time']\n", "        success_rate = (self.session_stats['successful_queries'] / \n", "                       max(1, self.session_stats['total_queries']) * 100)\n", "        \n", "        summary = f\"\"\"\n", "{self.colors['cyan']}╔{'═'*48}╗\n", "║{' '*16}📈 会话摘要{' '*16}║\n", "╠{'═'*48}╣\n", "║  ⏱️  会话时长: {str(duration).split('.')[0]:<25}║\n", "║  💬 总查询数: {self.session_stats['total_queries']:<25}║\n", "║  ✅ 成功率: {success_rate:.1f}%{' '*23}║\n", "║  ⚡ 命令执行: {self.session_stats['commands_executed']:<25}║\n", "║  📊 Token使用: {self.session_stats['current_tokens']}/{self.session_stats['max_tokens']:<15}║\n", "╚{'═'*48}╝{self.colors['end']}\n", "        \"\"\"\n", "        print(summary)\n", "\n", "def start_chat_interface(assistant):\n", "    \"\"\"启动完整的交互界面\"\"\"\n", "    try:\n", "        # 创建控制台界面\n", "        console = PowerBankConsoleInterface(assistant)\n", "        console.print_banner()\n", "        \n", "        # 主对话循环\n", "        while console.running:\n", "            try:\n", "                # 获取用户输入\n", "                user_input = console.safe_input(f\"\\n{console.colors['cyan']}👤 您: {console.colors['end']}\").strip()\n", "                \n", "                if not user_input:\n", "                    continue\n", "                \n", "                # 检查退出命令\n", "                if user_input.lower() in ['quit', 'exit', '/quit', '/exit', '退出']:\n", "                    console.print_colored(\"👋 感谢使用，再见！\", 'green')\n", "                    console._show_session_summary()\n", "                    break\n", "                \n", "                # 处理快捷命令\n", "                if user_input.startswith('/'):\n", "                    handle_command(user_input, console, assistant)\n", "                    continue\n", "                \n", "                # 显示加载动画\n", "                console.display_loading_animation(\"🧠 正在分析您的问题\")\n", "                \n", "                start_time = time.time()\n", "                \n", "                # 尝试分析\n", "                try:\n", "                    if hasattr(assistant, 'analyze'):\n", "                        result = assistant.analyze(user_input)\n", "                        response = result.get('response', str(result))\n", "                        query_type = result.get('query_type', '智能分析')\n", "                        success = result.get('success', True)\n", "                    else:\n", "                        # 模拟分析结果\n", "                        response = f\"基于您的问题'{user_input}'，我为您提供以下分析结果...\"\n", "                        query_type = self.detect_query_type(user_input)\n", "                        success = True\n", "                    \n", "                    response_time = time.time() - start_time\n", "                    \n", "                    if success:\n", "                        console.display_query_result(response, user_input, query_type, response_time)\n", "                        console.session_stats['successful_queries'] += 1\n", "                    else:\n", "                        console.print_colored(f\"❌ 分析失败: {response}\", 'red')\n", "                        console.session_stats['failed_queries'] += 1\n", "                    \n", "                except Exception as e:\n", "                    console.print_colored(f\"❌ 处理异常: {e}\", 'red')\n", "                    console.session_stats['failed_queries'] += 1\n", "                \n", "                # 更新统计\n", "                console.session_stats['total_queries'] += 1\n", "                \n", "                # 保存到历史\n", "                console.session_history.append({\n", "                    'question': user_input,\n", "                    'response': response if 'response' in locals() else str(result),\n", "                    'timestamp': datetime.now().isoformat(),\n", "                    'response_time': response_time if 'response_time' in locals() else 0\n", "                })\n", "                \n", "            except KeyboardInterrupt:\n", "                console.print_colored(\"\\n🔥 检测到中断信号，正在退出...\", 'yellow')\n", "                break\n", "            except Exception as e:\n", "                console.print_colored(f\"❌ 处理异常: {e}\", 'red')\n", "        \n", "        # 清理并退出\n", "        console.cleanup_and_exit()\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"❌ 交互界面启动失败: {e}\")\n", "\n", "def detect_query_type(question: str) -> str:\n", "    \"\"\"检测查询类型\"\"\"\n", "    if any(word in question for word in ['品牌', '牌子', '厂商']):\n", "        return '品牌分析'\n", "    elif any(word in question for word in ['用户', '客户', '人群']):\n", "        return '用户分析'\n", "    elif any(word in question for word in ['地区', '城市', '区域']):\n", "        return '地区分析'\n", "    elif any(word in question for word in ['时间', '月份', '趋势']):\n", "        return '时间分析'\n", "    else:\n", "        return '智能分析'\n", "\n", "def handle_command(command: str, console, assistant):\n", "    \"\"\"处理快捷命令\"\"\"\n", "    console.session_stats['commands_executed'] += 1\n", "    \n", "    if command in ['/help', '/h']:\n", "        show_help_menu(console)\n", "    elif command in ['/status', '/s']:\n", "        show_system_status(console, assistant)\n", "    elif command in ['/demo', '/d']:\n", "        run_demo_queries(console, assistant)\n", "    elif command in ['/history', '/hist']:\n", "        show_session_history(console)\n", "    elif command in ['/clear', '/c']:\n", "        clear_screen(console)\n", "    elif command in ['/stats']:\n", "        show_session_stats(console)\n", "    elif command in ['/theme']:\n", "        change_theme(console)\n", "    else:\n", "        console.print_colored(f\"❓ 未知命令: {command}\", 'red')\n", "        console.print_colored(\"💡 输入 /help 查看可用命令\", 'cyan')\n", "\n", "def show_help_menu(console):\n", "    \"\"\"显示帮助菜单\"\"\"\n", "    help_text = f\"\"\"\n", "{console.colors['cyan']}╔{'═'*68}╗\n", "║{' '*26}📚 命令帮助{' '*26}║\n", "╠{'═'*68}╣\n", "║                                                                  ║\n", "║  {console.colors['yellow']}🚀 基础命令:{console.colors['cyan']}                                                ║\n", "║    /help, /h      - 显示此帮助信息                              ║\n", "║    /quit, /exit   - 退出系统                                    ║\n", "║    /clear, /c     - 清屏                                        ║\n", "║                                                                  ║\n", "║  {console.colors['yellow']}📊 系统命令:{console.colors['cyan']}                                                ║\n", "║    /status, /s    - 显示系统状态                                ║\n", "║    /stats         - 显示会话统计                                ║\n", "║    /demo, /d      - 运行功能演示                                ║\n", "║                                                                  ║\n", "║  {console.colors['yellow']}📝 历史命令:{console.colors['cyan']}                                                ║\n", "║    /history       - 显示对话历史                                ║\n", "║    /theme         - 切换界面主题                                ║\n", "║                                                                  ║\n", "║  {console.colors['yellow']}💡 分析示例:{console.colors['cyan']}                                                ║\n", "║    \"各品牌市场份额如何？\"                                       ║\n", "║    \"男女用户比例分布\"                                           ║\n", "║    \"哪个地区收入最高？\"                                         ║\n", "║    \"月度收入趋势分析\"                                           ║\n", "║                                                                  ║\n", "╚{'═'*68}╝{console.colors['end']}\n", "    \"\"\"\n", "    print(help_text)\n", "\n", "def show_system_status(console, assistant):\n", "    \"\"\"显示系统状态\"\"\"\n", "    try:\n", "        console.print_colored(\"🔍 正在检查系统状态...\", 'yellow')\n", "        \n", "        status_display = f\"\"\"\n", "{console.colors['cyan']}╔{'═'*58}╗\n", "║{' '*20}📊 系统状态报告{' '*20}║\n", "╠{'═'*58}╣\n", "║  🔗 数据库连接: 正常                                    ║\n", "║  🤖 API连接: 正常                                       ║\n", "║  📦 加载模块: 4个                                       ║\n", "║  📋 数据表状态: 已验证                                  ║\n", "║  💾 记忆系统: 运行中                                    ║\n", "║  🏥 健康度: 优秀                                        ║\n", "║  📊 Token使用: {console.session_stats['current_tokens']}/{console.session_stats['max_tokens']:<30}║\n", "╚{'═'*58}╝{console.colors['end']}\n", "        \"\"\"\n", "        print(status_display)\n", "            \n", "    except Exception as e:\n", "        console.print_colored(f\"❌ 状态检查失败: {e}\", 'red')\n", "\n", "def run_demo_queries(console, assistant):\n", "    \"\"\"运行演示查询\"\"\"\n", "    demo_questions = [\n", "        \"系统状态如何？\",\n", "        \"显示所有品牌信息\",\n", "        \"用户总数统计\"\n", "    ]\n", "    \n", "    console.print_colored(\"🚀 开始功能演示...\", 'green')\n", "    \n", "    for i, question in enumerate(demo_questions, 1):\n", "        console.print_colored(f\"\\n📝 演示 {i}: {question}\", 'cyan')\n", "        \n", "        try:\n", "            console.display_loading_animation(f\"正在处理演示 {i}\")\n", "            \n", "            # 模拟处理时间\n", "            time.sleep(1)\n", "            \n", "            if hasattr(assistant, 'analyze'):\n", "                result = assistant.analyze(question)\n", "                response = result.get('response', str(result))\n", "            else:\n", "                response = f\"演示回答: 这是对'{question}'的模拟回答\"\n", "            \n", "            console.print_colored(f\"🤖 回答: {response[:100]}...\", 'green')\n", "        except Exception as e:\n", "            console.print_colored(f\"❌ 演示失败: {e}\", 'red')\n", "    \n", "    console.print_colored(\"✅ 功能演示完成！\", 'green')\n", "\n", "def show_session_history(console):\n", "    \"\"\"显示会话历史\"\"\"\n", "    if not console.session_history:\n", "        console.print_colored(\"📝 暂无对话历史\", 'dim')\n", "        return\n", "    \n", "    history_display = f\"\"\"\n", "{console.colors['cyan']}╔{'═'*68}╗\n", "║{' '*24}📚 会话历史{' '*24}║\n", "╠{'═'*68}╣{console.colors['end']}\n", "    \"\"\"\n", "    print(history_display)\n", "    \n", "    for i, item in enumerate(console.session_history[-5:], 1):\n", "        print(f\"{console.colors['yellow']}{i}. {item['question']}{console.colors['end']}\")\n", "        print(f\"   {console.colors['green']}回答: {item['response'][:60]}...{console.colors['end']}\")\n", "        print(f\"{console.colors['cyan']}{'─'*68}{console.colors['end']}\")\n", "    \n", "    print(f\"{console.colors['cyan']}╚{'═'*68}╝{console.colors['end']}\")\n", "\n", "def show_session_stats(console):\n", "    \"\"\"显示会话统计\"\"\"\n", "    stats = console.session_stats\n", "    duration = datetime.now() - stats['start_time']\n", "    \n", "    stats_display = f\"\"\"\n", "{console.colors['cyan']}╔{'═'*48}╗\n", "║{' '*16}📈 会话统计{' '*16}║\n", "╠{'═'*48}╣\n", "║  ⏱️  会话时长: {str(duration).split('.')[0]:<25}║\n", "║  💬 总查询数: {stats['total_queries']:<25}║\n", "║  ✅ 成功查询: {stats['successful_queries']:<25}║\n", "║  ❌ 失败查询: {stats['failed_queries']:<25}║\n", "║  ⚡ 命令执行: {stats['commands_executed']:<25}║\n", "║  📊 Token使用: {stats['current_tokens']}/{stats['max_tokens']:<15}║\n", "╚{'═'*48}╝{console.colors['end']}\n", "    \"\"\"\n", "    print(stats_display)\n", "\n", "def clear_screen(console):\n", "    \"\"\"清屏\"\"\"\n", "    os.system('cls' if os.name == 'nt' else 'clear')\n", "    console.print_colored(\"🧹 屏幕已清理\", 'green')\n", "\n", "def change_theme(console):\n", "    \"\"\"切换主题\"\"\"\n", "    console.print_colored(\"🎨 主题切换功能开发中...\", 'yellow')\n", "\n", "def start_fusion_system():\n", "    \"\"\"启动完整融合系统 - 修复版\"\"\"\n", "    try:\n", "        custom_print(\"🚀 启动完整融合系统...\")\n", "        \n", "        # 1. 检查并获取配置\n", "        try:\n", "            config = get_system_config()\n", "            custom_print(\"✅ 配置加载成功\")\n", "        except Exception as e:\n", "            custom_print(f\"❌ 配置加载失败: {e}\")\n", "            return start_basic_chat()\n", "        \n", "        # 2. 验证配置完整性\n", "        if not validate_config(config):\n", "            custom_print(\"❌ 配置验证失败，启动基础模式\")\n", "            return start_basic_chat()\n", "        \n", "        # 3. 尝试创建融合系统\n", "        try:\n", "            # 检查核心类是否存在\n", "            if 'PowerBankIntelligentAssistant' not in globals():\n", "                custom_print(\"⚠️ PowerBankIntelligentAssistant 类未找到，尝试创建安全助手\")\n", "                return start_safe_mode(config)\n", "            \n", "            assistant = PowerBankIntelligentAssistant(\n", "                db_config=config['db_config'],\n", "                api_config=config['api_config'],\n", "                memory_config=config['memory_config']\n", "            )\n", "            \n", "            # 4. 验证助手是否正确初始化\n", "            if not validate_assistant(assistant):\n", "                custom_print(\"❌ 助手初始化验证失败，启动安全模式\")\n", "                return start_safe_mode(config)\n", "            \n", "            custom_print(\"✅ 融合系统启动成功！\")\n", "            start_chat_interface(assistant)\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"❌ 融合系统创建失败: {e}\")\n", "            custom_print(\"🔄 尝试启动安全模式...\")\n", "            start_safe_mode(config)\n", "            \n", "    except Exception as e:\n", "        custom_print(f\"❌ 系统启动失败: {e}\")\n", "        custom_print(\"🆘 启动应急模式...\")\n", "        start_emergency_mode()\n", "\n", "def validate_config(config):\n", "    \"\"\"验证配置完整性\"\"\"\n", "    try:\n", "        required_keys = ['db_config', 'api_config', 'memory_config']\n", "        \n", "        for key in required_keys:\n", "            if key not in config:\n", "                custom_print(f\"❌ 缺少配置项: {key}\")\n", "                return False\n", "        \n", "        # 验证数据库配置\n", "        db_required = ['host', 'user', 'password', 'database']\n", "        for key in db_required:\n", "            if key not in config['db_config']:\n", "                custom_print(f\"❌ 缺少数据库配置: {key}\")\n", "                return False\n", "        \n", "        # 验证API配置\n", "        api_required = ['api_key', 'base_url']\n", "        for key in api_required:\n", "            if key not in config['api_config']:\n", "                custom_print(f\"❌ 缺少API配置: {key}\")\n", "                return False\n", "        \n", "        custom_print(\"✅ 配置验证通过\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"❌ 配置验证异常: {e}\")\n", "        return False\n", "\n", "def validate_assistant(assistant):\n", "    \"\"\"验证助手是否正确初始化\"\"\"\n", "    try:\n", "        # 检查核心属性\n", "        if not hasattr(assistant, 'analyze'):\n", "            custom_print(\"❌ 助手缺少 analyze 方法\")\n", "            return False\n", "        \n", "        # 尝试简单测试\n", "        test_result = assistant.analyze(\"测试\")\n", "        if test_result is None:\n", "            custom_print(\"❌ 助手测试返回 None\")\n", "            return False\n", "        \n", "        custom_print(\"✅ 助手验证通过\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"❌ 助手验证失败: {e}\")\n", "        return False\n", "\n", "def start_safe_mode(config):\n", "    \"\"\"启动安全模式\"\"\"\n", "    try:\n", "        custom_print(\"🛡️ 启动安全模式...\")\n", "        \n", "        # 创建安全助手\n", "        assistant = create_safe_assistant(config)\n", "        \n", "        if assistant:\n", "            custom_print(\"✅ 安全模式启动成功\")\n", "            start_chat_interface(assistant)\n", "        else:\n", "            custom_print(\"❌ 安全模式启动失败\")\n", "            start_emergency_mode()\n", "            \n", "    except Exception as e:\n", "        custom_print(f\"❌ 安全模式异常: {e}\")\n", "        start_emergency_mode()\n", "\n", "def create_safe_assistant(config):\n", "    \"\"\"创建安全助手\"\"\"\n", "    try:\n", "        from openai import OpenAI\n", "        import httpx\n", "        \n", "        # 创建HTTP客户端\n", "        http_client = httpx.Client(follow_redirects=True, timeout=30)\n", "        \n", "        # 创建OpenAI客户端\n", "        client = OpenAI(\n", "            api_key=config['api_config']['api_key'],\n", "            base_url=config['api_config']['base_url'],\n", "            http_client=http_client\n", "        )\n", "        \n", "        # 测试连接\n", "        test_response = client.chat.completions.create(\n", "            model=\"deepseek-chat\",\n", "            messages=[{\"role\": \"user\", \"content\": \"测试\"}],\n", "            max_tokens=5\n", "        )\n", "        \n", "        custom_print(\"✅ API连接测试成功\")\n", "        \n", "        class SafeAssistant:\n", "            def __init__(self, client, config):\n", "                self.client = client\n", "                self.config = config\n", "                self.conversation_history = []\n", "            \n", "            def analyze(self, question):\n", "                try:\n", "                    if not question or question.strip() == \"\":\n", "                        return {\n", "                            \"response\": \"请输入有效的问题\",\n", "                            \"success\": <PERSON><PERSON><PERSON>,\n", "                            \"query_type\": \"错误\"\n", "                        }\n", "                    \n", "                    # 构建消息\n", "                    messages = [\n", "                        {\n", "                            \"role\": \"system\", \n", "                            \"content\": \"你是共享充电宝数据分析专家。请根据用户问题提供专业的分析和建议。\"\n", "                        },\n", "                        {\n", "                            \"role\": \"user\", \n", "                            \"content\": question\n", "                        }\n", "                    ]\n", "                    \n", "                    # 调用API\n", "                    response = self.client.chat.completions.create(\n", "                        model=\"deepseek-chat\",\n", "                        messages=messages,\n", "                        temperature=0.7,\n", "                        max_tokens=1000\n", "                    )\n", "                    \n", "                    answer = response.choices[0].message.content\n", "                    \n", "                    # 保存到历史\n", "                    self.conversation_history.append({\n", "                        \"question\": question,\n", "                        \"answer\": answer,\n", "                        \"timestamp\": datetime.now().isoformat()\n", "                    })\n", "                    \n", "                    return {\n", "                        \"response\": answer,\n", "                        \"success\": True,\n", "                        \"query_type\": self.detect_query_type(question)\n", "                    }\n", "                    \n", "                except Exception as e:\n", "                    return {\n", "                        \"response\": f\"处理错误: {str(e)}\",\n", "                        \"success\": <PERSON><PERSON><PERSON>,\n", "                        \"query_type\": \"错误\"\n", "                    }\n", "            \n", "            def detect_query_type(self, question):\n", "                \"\"\"检测查询类型\"\"\"\n", "                if any(word in question for word in ['品牌', '牌子', '厂商']):\n", "                    return '品牌分析'\n", "                elif any(word in question for word in ['用户', '客户', '人群']):\n", "                    return '用户分析'\n", "                elif any(word in question for word in ['地区', '城市', '区域']):\n", "                    return '地区分析'\n", "                elif any(word in question for word in ['时间', '月份', '趋势']):\n", "                    return '时间分析'\n", "                else:\n", "                    return '智能分析'\n", "        \n", "        return SafeAssistant(client, config)\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"❌ 安全助手创建失败: {e}\")\n", "        return None\n", "\n", "def start_emergency_mode():\n", "    \"\"\"应急模式\"\"\"\n", "    try:\n", "        custom_print(\"🆘 启动应急模式...\")\n", "        \n", "        class EmergencyAssistant:\n", "            def analyze(self, question):\n", "                return {\n", "                    \"response\": f\"应急模式：您的问题是'{question}'。系统正在维护中，请稍后重试。\",\n", "                    \"success\": True,\n", "                    \"query_type\": \"应急模式\"\n", "                }\n", "        \n", "        assistant = EmergencyAssistant()\n", "        start_chat_interface(assistant)\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"❌ 应急模式失败: {e}\")\n", "        custom_print(\"💡 请检查系统环境和依赖\")\n", "\n", "# 修复 start_chat_interface 函数中的错误处理\n", "def start_chat_interface(assistant):\n", "    \"\"\"启动完整的交互界面 - 修复版\"\"\"\n", "    try:\n", "        # 验证助手对象\n", "        if assistant is None:\n", "            custom_print(\"❌ 助手对象为空\")\n", "            return\n", "        \n", "        if not hasattr(assistant, 'analyze'):\n", "            custom_print(\"❌ 助手缺少 analyze 方法\")\n", "            return\n", "        \n", "        # 创建控制台界面\n", "        console = PowerBankConsoleInterface(assistant)\n", "        console.print_banner()\n", "        \n", "        # 主对话循环\n", "        while console.running:\n", "            try:\n", "                # 获取用户输入\n", "                user_input = console.safe_input(f\"\\n{console.colors['cyan']}👤 您: {console.colors['end']}\").strip()\n", "                \n", "                if not user_input:\n", "                    continue\n", "                \n", "                # 检查退出命令\n", "                if user_input.lower() in ['quit', 'exit', '/quit', '/exit', '退出']:\n", "                    console.print_colored(\"👋 感谢使用，再见！\", 'green')\n", "                    console._show_session_summary()\n", "                    break\n", "                \n", "                # 处理快捷命令\n", "                if user_input.startswith('/'):\n", "                    handle_command(user_input, console, assistant)\n", "                    continue\n", "                \n", "                # 显示加载动画\n", "                console.display_loading_animation(\"🧠 正在分析您的问题\")\n", "                \n", "                start_time = time.time()\n", "                \n", "                # 安全调用分析方法\n", "                try:\n", "                    result = assistant.analyze(user_input)\n", "                    \n", "                    # 验证结果\n", "                    if result is None:\n", "                        result = {\n", "                            \"response\": \"分析结果为空，请重试\",\n", "                            \"success\": <PERSON><PERSON><PERSON>,\n", "                            \"query_type\": \"错误\"\n", "                        }\n", "                    \n", "                    # 确保结果是字典格式\n", "                    if not isinstance(result, dict):\n", "                        result = {\n", "                            \"response\": str(result),\n", "                            \"success\": True,\n", "                            \"query_type\": \"智能分析\"\n", "                        }\n", "                    \n", "                    response = result.get('response', '无响应')\n", "                    query_type = result.get('query_type', '智能分析')\n", "                    success = result.get('success', True)\n", "                    \n", "                    response_time = time.time() - start_time\n", "                    \n", "                    if success:\n", "                        console.display_query_result(response, user_input, query_type, response_time)\n", "                        console.session_stats['successful_queries'] += 1\n", "                    else:\n", "                        console.print_colored(f\"❌ 分析失败: {response}\", 'red')\n", "                        console.session_stats['failed_queries'] += 1\n", "                    \n", "                except Exception as e:\n", "                    console.print_colored(f\"❌ 分析异常: {e}\", 'red')\n", "                    console.session_stats['failed_queries'] += 1\n", "                \n", "                # 更新统计\n", "                console.session_stats['total_queries'] += 1\n", "                \n", "                # 保存到历史\n", "                console.session_history.append({\n", "                    'question': user_input,\n", "                    'response': response if 'response' in locals() else \"处理异常\",\n", "                    'timestamp': datetime.now().isoformat(),\n", "                    'response_time': response_time if 'response_time' in locals() else 0\n", "                })\n", "                \n", "            except KeyboardInterrupt:\n", "                console.print_colored(\"\\n🔥 检测到中断信号，正在退出...\", 'yellow')\n", "                break\n", "            except Exception as e:\n", "                console.print_colored(f\"❌ 处理异常: {e}\", 'red')\n", "                console.session_stats['failed_queries'] += 1\n", "        \n", "        # 清理并退出\n", "        console.cleanup_and_exit()\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"❌ 交互界面启动失败: {e}\")\n", "# 启动提示\n", "custom_print(\"\"\"\n", "🚀 美化交互界面启动器已加载！\n", "\n", "✨ 新增美化功能:\n", "• 🎨 精美的框架界面设计\n", "• 📊 实时Token状态显示\n", "• 🔄 动态加载动画效果\n", "• 📋 结构化结果展示\n", "• 🎯 场景图标识别\n", "\n", "启动方式:\n", "• start_fusion_system()  - 完整融合系统启动\n", "• start_basic_chat()     - 基础对话模式\n", "\n", "💡 推荐直接运行: start_fusion_system()\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "e024646f", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 🚀 智能体系统启动器 - 完整集成版\n", "# 作用: 真正连接四大核心模块和融合系统的启动器\n", "# 版本: v2.2 - 完整集成版\n", "# ===================================================================\n", "\n", "import os\n", "import sys\n", "import time\n", "import json\n", "from datetime import datetime\n", "from typing import Dict, Optional, List, Any\n", "\n", "# 美化输出\n", "try:\n", "    from rich.console import Console\n", "    from rich.panel import Panel\n", "    from rich.markdown import Markdown\n", "    console = Console()\n", "    def custom_print(info): console.print(info)\n", "except ImportError:\n", "    def custom_print(info): print(info)\n", "\n", "# ===================================================================\n", "# 🔧 修复 PowerBankIntelligentQueryEngine 缺失方法\n", "# ===================================================================\n", "\n", "def add_missing_methods_to_query_engine():\n", "    \"\"\"为查询引擎添加缺失的方法\"\"\"\n", "    \n", "    # 检查是否已经定义了 PowerBankIntelligentQueryEngine\n", "    if 'PowerBankIntelligentQueryEngine' in globals():\n", "        engine_class = globals()['PowerBankIntelligentQueryEngine']\n", "        \n", "        # 添加缺失的 verify_nine_table_structure 方法\n", "        if not hasattr(engine_class, 'verify_nine_table_structure'):\n", "            def verify_nine_table_structure(self):\n", "                \"\"\"验证9表结构完整性\"\"\"\n", "                try:\n", "                    if not self.connection:\n", "                        return {\n", "                            \"structure_complete\": <PERSON><PERSON><PERSON>,\n", "                            \"total_found\": 0,\n", "                            \"missing_tables\": self.expected_tables,\n", "                            \"message\": \"数据库未连接\"\n", "                        }\n", "                    \n", "                    cursor = self.connection.cursor()\n", "                    cursor.execute(\"SHOW TABLES\")\n", "                    existing_tables = [table[0] for table in cursor.fetchall()]\n", "                    cursor.close()\n", "                    \n", "                    missing_tables = [table for table in self.expected_tables if table not in existing_tables]\n", "                    found_count = len(self.expected_tables) - len(missing_tables)\n", "                    \n", "                    return {\n", "                        \"structure_complete\": len(missing_tables) == 0,\n", "                        \"total_found\": found_count,\n", "                        \"existing_tables\": existing_tables,\n", "                        \"missing_tables\": missing_tables,\n", "                        \"expected_tables\": self.expected_tables,\n", "                        \"message\": f\"找到 {found_count}/{len(self.expected_tables)} 张表\"\n", "                    }\n", "                    \n", "                except Exception as e:\n", "                    return {\n", "                        \"structure_complete\": <PERSON><PERSON><PERSON>,\n", "                        \"total_found\": 0,\n", "                        \"missing_tables\": [],\n", "                        \"error\": str(e),\n", "                        \"message\": f\"验证失败: {e}\"\n", "                    }\n", "            \n", "            # 动态添加方法到类\n", "            engine_class.verify_nine_table_structure = verify_nine_table_structure\n", "            custom_print(\"  ✅ 已修复 PowerBankIntelligentQueryEngine.verify_nine_table_structure 方法\")\n", "\n", "# ===================================================================\n", "# 🚀 完整系统启动函数\n", "# ===================================================================\n", "\n", "def start_powerbank_system():\n", "    \"\"\"一键启动完整的共享充电宝智能分析系统 - 真正集成版\"\"\"\n", "    \n", "    print_startup_banner()\n", "    \n", "    try:\n", "        # 1. 快速环境检查\n", "        custom_print(\"🔍 检查环境...\")\n", "        check_basic_environment()\n", "        \n", "        # 2. 初始化配置\n", "        custom_print(\"⚙️ 加载配置...\")\n", "        config = get_system_config()\n", "        \n", "        # 3. 快速连接测试\n", "        custom_print(\"🔗 测试连接...\")\n", "        test_connections(config)\n", "        \n", "        # 4. 修复缺失方法\n", "        custom_print(\"🔧 修复系统组件...\")\n", "        add_missing_methods_to_query_engine()\n", "        \n", "        # 5. 初始化完整融合系统\n", "        custom_print(\"🧠 初始化融合系统...\")\n", "        assistant = create_complete_assistant(config)\n", "        \n", "        if assistant:\n", "            custom_print(\"✅ 融合系统启动成功！\")\n", "            custom_print(\"🎯 正在启动AI对话界面...\")\n", "            time.sleep(1)\n", "            \n", "            # 6. 启动完整对话界面\n", "            start_complete_chat_interface(assistant)\n", "        else:\n", "            custom_print(\"❌ 融合系统启动失败，启动安全模式...\")\n", "            fallback_assistant = create_safe_assistant(config)\n", "            if fallback_assistant:\n", "                start_safe_chat_interface(fallback_assistant)\n", "            else:\n", "                custom_print(\"💡 启动应急聊天模式...\")\n", "                quick_chat_mode()\n", "            \n", "    except KeyboardInterrupt:\n", "        custom_print(\"\\n👋 用户退出\")\n", "    except Exception as e:\n", "        custom_print(f\"❌ 启动错误: {e}\")\n", "        custom_print(\"💡 正在启动应急模式...\")\n", "        quick_chat_mode()\n", "\n", "def create_complete_assistant(config):\n", "    \"\"\"创建完整的融合系统助手\"\"\"\n", "    try:\n", "        # 检查是否存在 PowerBankIntelligentAssistant 类\n", "        if 'PowerBankIntelligentAssistant' in globals():\n", "            custom_print(\"  🎯 使用完整融合系统...\")\n", "            assistant = PowerBankIntelligentAssistant(\n", "                db_config=config['db_config'],\n", "                api_config=config['api_config'],\n", "                memory_config=config['memory_config']\n", "            )\n", "            custom_print(\"  ✅ 完整融合系统创建成功\")\n", "            return assistant\n", "        else:\n", "            custom_print(\"  ⚠️ PowerBankIntelligentAssistant 未找到，尝试创建组合系统...\")\n", "            return create_modular_assistant(config)\n", "            \n", "    except Exception as e:\n", "        custom_print(f\"  ❌ 完整融合系统创建失败: {e}\")\n", "        return create_modular_assistant(config)\n", "\n", "def create_modular_assistant(config):\n", "    \"\"\"创建模块化组合助手\"\"\"\n", "    try:\n", "        custom_print(\"  🔧 创建模块化组合系统...\")\n", "        \n", "        # 初始化各个模块\n", "        modules = {}\n", "        \n", "        # 1. 智能记忆模块\n", "        if 'PowerBankIntelligentMemory' in globals():\n", "            modules['memory'] = PowerBankIntelligentMemory(\n", "                count_threshold=config['memory_config'].get('count_threshold', 25),\n", "                token_threshold=config['memory_config'].get('token_threshold', 4000),\n", "                use_token_mode=config['memory_config'].get('use_token_mode', True)\n", "            )\n", "            custom_print(\"    ✅ 智能记忆模块加载成功\")\n", "        \n", "        # 2. 专家知识模块\n", "        if 'PowerBankExpertKnowledge' in globals():\n", "            modules['expert'] = PowerBankExpertKnowledge()\n", "            custom_print(\"    ✅ 专家知识模块加载成功\")\n", "        \n", "        if 'PowerBankLongTermMemory' in globals():\n", "            modules['long_memory'] = PowerBankLongTermMemory()\n", "            custom_print(\"    ✅ 长期记忆模块加载成功\")\n", "        \n", "        # 3. 查询引擎模块\n", "        if 'PowerBankIntelligentQueryEngine' in globals():\n", "            modules['query_engine'] = PowerBankIntelligentQueryEngine(\n", "                config['db_config'], \n", "                config['api_config']\n", "            )\n", "            custom_print(\"    ✅ 查询引擎模块加载成功\")\n", "        \n", "        # 4. 创建组合助手\n", "        if modules:\n", "            assistant = <PERSON><PERSON>larPowerBankAssistant(modules, config)\n", "            custom_print(\"  ✅ 模块化组合系统创建成功\")\n", "            return assistant\n", "        else:\n", "            custom_print(\"  ❌ 没有找到任何可用模块\")\n", "            return None\n", "            \n", "    except Exception as e:\n", "        custom_print(f\"  ❌ 模块化系统创建失败: {e}\")\n", "        return None\n", "\n", "class ModularPowerBankAssistant:\n", "    \"\"\"模块化组合助手类\"\"\"\n", "    \n", "    def __init__(self, modules: Dict, config: Dict):\n", "        self.modules = modules\n", "        self.config = config\n", "        self.session_start = datetime.now()\n", "        self.conversation_count = 0\n", "        \n", "        # 初始化OpenAI客户端用于基础对话\n", "        try:\n", "            from openai import OpenAI\n", "            import httpx\n", "            \n", "            http_client = httpx.Client(follow_redirects=True, timeout=30)\n", "            self.client = OpenAI(\n", "                api_key=config['api_config']['api_key'],\n", "                base_url=config['api_config']['base_url'],\n", "                http_client=http_client\n", "            )\n", "        except Exception as e:\n", "            custom_print(f\"⚠️ AI客户端初始化失败: {e}\")\n", "            self.client = None\n", "    \n", "    def analyze(self, question: str, context: str = \"\") -> Dict:\n", "        \"\"\"智能分析 - 整合所有模块能力\"\"\"\n", "        self.conversation_count += 1\n", "        start_time = time.time()\n", "        \n", "        try:\n", "            result = {\n", "                \"question\": question,\n", "                \"timestamp\": datetime.now().isoformat(),\n", "                \"modules_used\": [],\n", "                \"response\": \"\",\n", "                \"status\": \"success\"\n", "            }\n", "            \n", "            # 1. 使用智能记忆模块\n", "            if 'memory' in self.modules:\n", "                self.modules['memory'].append_message({\n", "                    \"role\": \"user\",\n", "                    \"content\": question\n", "                })\n", "                result[\"modules_used\"].append(\"智能记忆\")\n", "            \n", "            # 2. 获取专家知识\n", "            expert_knowledge = \"\"\n", "            if 'expert' in self.modules:\n", "                # 尝试获取相关的分析模板或知识\n", "                try:\n", "                    if hasattr(self.modules['expert'], 'get_relevant_knowledge'):\n", "                        expert_knowledge = self.modules['expert'].get_relevant_knowledge(question)\n", "                    <PERSON><PERSON>(self.modules['expert'], 'analysis_templates'):\n", "                        expert_knowledge = \"专家知识库已加载\"\n", "                    result[\"modules_used\"].append(\"专家知识\")\n", "                except Exception as e:\n", "                    custom_print(f\"⚠️ 专家知识获取失败: {e}\")\n", "            \n", "            # 3. 尝试SQL查询（如果是数据相关问题）\n", "            sql_result = None\n", "            if 'query_engine' in self.modules and self._is_data_question(question):\n", "                try:\n", "                    sql_query = self.modules['query_engine'].natural_language_to_sql(\n", "                        question, expert_knowledge\n", "                    )\n", "                    if sql_query:\n", "                        sql_result = self.modules['query_engine'].execute_query(sql_query)\n", "                        result[\"modules_used\"].append(\"查询引擎\")\n", "                        result[\"sql_query\"] = sql_query\n", "                        result[\"data_result\"] = sql_result\n", "                except Exception as e:\n", "                    custom_print(f\"⚠️ SQL查询失败: {e}\")\n", "            \n", "            # 4. 生成AI回复\n", "            if self.client:\n", "                ai_response = self._generate_ai_response(question, expert_knowledge, sql_result)\n", "                result[\"response\"] = ai_response\n", "            else:\n", "                result[\"response\"] = \"AI服务不可用，但已记录您的问题\"\n", "            \n", "            # 5. 记录到智能记忆\n", "            if 'memory' in self.modules:\n", "                self.modules['memory'].append_message({\n", "                    \"role\": \"assistant\",\n", "                    \"content\": result[\"response\"]\n", "                })\n", "            \n", "            result[\"processing_time\"] = round(time.time() - start_time, 2)\n", "            return result\n", "            \n", "        except Exception as e:\n", "            return {\n", "                \"question\": question,\n", "                \"timestamp\": datetime.now().isoformat(),\n", "                \"response\": f\"分析过程中出现错误: {str(e)}\",\n", "                \"status\": \"error\",\n", "                \"processing_time\": round(time.time() - start_time, 2)\n", "            }\n", "    \n", "    def _is_data_question(self, question: str) -> bool:\n", "        \"\"\"判断是否为数据相关问题\"\"\"\n", "        data_keywords = [\n", "            \"数据\", \"分析\", \"统计\", \"查询\", \"报告\", \"趋势\", \"对比\",\n", "            \"用户\", \"品牌\", \"地区\", \"时间\", \"收入\", \"订单\", \"市场份额\",\n", "            \"SQL\", \"表\", \"字段\", \"数量\", \"平均\", \"最大\", \"最小\", \"总计\"\n", "        ]\n", "        return any(keyword in question for keyword in data_keywords)\n", "    \n", "    def _generate_ai_response(self, question: str, expert_knowledge: str = \"\", sql_result=None) -> str:\n", "        \"\"\"生成AI回复\"\"\"\n", "        try:\n", "            # 构建系统提示\n", "            system_prompt = \"\"\"你是共享充电宝数据分析专家，具备以下能力：\n", "1. 数据分析和业务洞察\n", "2. SQL查询结果解读\n", "3. 业务建议和优化方案\n", "4. 用户行为分析\n", "\n", "请根据用户问题和提供的信息，给出专业、详细的分析和建议。\"\"\"\n", "            \n", "            # 构建用户消息\n", "            user_message = question\n", "            if expert_knowledge:\n", "                user_message += f\"\\n\\n专家知识: {expert_knowledge}\"\n", "            if sql_result:\n", "                user_message += f\"\\n\\n查询结果: {str(sql_result)[:500]}...\"\n", "            \n", "            response = self.client.chat.completions.create(\n", "                model=\"deepseek-chat\",\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_prompt},\n", "                    {\"role\": \"user\", \"content\": user_message}\n", "                ],\n", "                temperature=0.7,\n", "                max_tokens=2000\n", "            )\n", "            \n", "            return response.choices[0].message.content\n", "            \n", "        except Exception as e:\n", "            return f\"AI回复生成失败: {str(e)}\"\n", "    \n", "    def get_system_status(self) -> Dict:\n", "        \"\"\"获取系统状态\"\"\"\n", "        return {\n", "            \"session_start\": self.session_start.strftime('%Y-%m-%d %H:%M:%S'),\n", "            \"conversation_count\": self.conversation_count,\n", "            \"loaded_modules\": list(self.modules.keys()),\n", "            \"module_count\": len(self.modules),\n", "            \"ai_client_status\": \"正常\" if self.client else \"异常\",\n", "            \"database_status\": \"已连接\" if self.config.get('db_config') else \"未连接\"\n", "        }\n", "    \n", "    def intelligent_chat(self, message: str) -> str:\n", "        \"\"\"智能聊天接口 - 兼容性方法\"\"\"\n", "        result = self.analyze(message)\n", "        return result.get('response', '处理异常')\n", "\n", "def start_complete_chat_interface(assistant):\n", "    \"\"\"启动完整对话界面\"\"\"\n", "    # 清屏并显示对话界面\n", "    os.system('cls' if os.name == 'nt' else 'clear')\n", "    \n", "    # 获取系统状态\n", "    if hasattr(assistant, 'get_system_status'):\n", "        status = assistant.get_system_status()\n", "        modules_info = f\"已加载模块: {', '.join(status.get('loaded_modules', []))}\"\n", "    else:\n", "        modules_info = \"融合系统已启动\"\n", "    \n", "    chat_banner = f\"\"\"\n", "╔═══════════════════════════════════════════════════════════╗\n", "║           🤖 共享充电宝智能分析系统 v2.2                   ║\n", "║       💬 完整融合系统 | 🧠 四大核心模块                    ║\n", "║       {modules_info:<50} ║\n", "╚═══════════════════════════════════════════════════════════╝\n", "    \"\"\"\n", "    custom_print(chat_banner)\n", "    \n", "    custom_print(\"🤖 融合系统: 您好！我是集成了四大核心模块的智能分析助手\")\n", "    custom_print(\"🧠 智能记忆 | 📚 专家知识 | 🔍 查询引擎 | 💬 AI对话\")\n", "    custom_print(\"💡 您可以问我关于数据分析、业务洞察的任何问题\")\n", "    custom_print(\"🔧 输入 /help 查看可用命令\")\n", "    custom_print(\"-\" * 60)\n", "    \n", "    while True:\n", "        try:\n", "            # 获取用户输入\n", "            user_input = input(\"\\n👤 您: \").strip()\n", "            \n", "            if not user_input:\n", "                continue\n", "            \n", "            # 处理命令\n", "            if handle_complete_chat_commands(user_input, assistant):\n", "                continue\n", "            \n", "            # 智能分析处理\n", "            custom_print(\"🧠 融合系统正在分析...\")\n", "            \n", "            try:\n", "                # 使用完整的分析功能\n", "                if hasattr(assistant, 'analyze'):\n", "                    result = assistant.analyze(user_input)\n", "                    \n", "                    if isinstance(result, dict):\n", "                        response = result.get('response', '处理异常')\n", "                        modules_used = result.get('modules_used', [])\n", "                        processing_time = result.get('processing_time', 0)\n", "                        \n", "                        # 显示使用的模块\n", "                        if modules_used:\n", "                            custom_print(f\"🔧 使用模块: {', '.join(modules_used)} | ⏱️ 处理时间: {processing_time}s\")\n", "                        \n", "                        # 显示回复\n", "                        custom_print(f\"🤖 融合系统: {response}\")\n", "                        \n", "                        # 如果有SQL查询结果，显示简要信息\n", "                        if 'sql_query' in result:\n", "                            custom_print(f\"📊 执行了SQL查询: {result['sql_query'][:100]}...\")\n", "                    else:\n", "                        custom_print(f\"🤖 融合系统: {str(result)}\")\n", "                \n", "                <PERSON><PERSON>(assistant, 'intelligent_chat'):\n", "                    response = assistant.intelligent_chat(user_input)\n", "                    custom_print(f\"🤖 融合系统: {response}\")\n", "                else:\n", "                    custom_print(\"🤖 融合系统: 抱歉，系统功能异常\")\n", "                \n", "            except Exception as e:\n", "                custom_print(f\"🤖 融合系统: 抱歉，处理您的问题时出现错误: {e}\")\n", "                custom_print(\"💡 请尝试重新表述您的问题\")\n", "            \n", "        except KeyboardInterrupt:\n", "            custom_print(\"\\n🤖 融合系统: 检测到Ctrl+C，正在退出...\")\n", "            break\n", "        except Exception as e:\n", "            custom_print(f\"❌ 对话错误: {e}\")\n", "\n", "def handle_complete_chat_commands(user_input: str, assistant) -> bool:\n", "    \"\"\"处理完整系统的聊天命令\"\"\"\n", "    command = user_input.lower()\n", "    \n", "    # 退出命令\n", "    if command in ['/quit', '/exit', 'quit', 'exit', '退出']:\n", "        show_complete_goodbye_message(assistant)\n", "        sys.exit(0)\n", "    \n", "    # 帮助命令\n", "    elif command in ['/help', 'help', '帮助']:\n", "        show_complete_chat_help()\n", "        return True\n", "    \n", "    # 清屏命令\n", "    elif command in ['/clear', 'clear', '清屏']:\n", "        os.system('cls' if os.name == 'nt' else 'clear')\n", "        return True\n", "    \n", "    # 系统状态命令\n", "    elif command in ['/status', 'status', '状态']:\n", "        show_complete_system_status(assistant)\n", "        return True\n", "    \n", "    # 模块状态命令\n", "    elif command in ['/modules', 'modules', '模块']:\n", "        show_modules_status(assistant)\n", "        return True\n", "    \n", "    return False\n", "\n", "def show_complete_chat_help():\n", "    \"\"\"显示完整系统帮助\"\"\"\n", "    help_text = \"\"\"\n", "🔧 可用命令:\n", "• /help 或 help     - 显示此帮助信息\n", "• /quit 或 quit     - 退出对话\n", "• /clear 或 clear   - 清屏\n", "• /status           - 显示系统状态\n", "• /modules          - 显示模块状态\n", "\n", "💡 融合系统能力:\n", "• 🧠 智能记忆 - 上下文感知和对话管理\n", "• 📚 专家知识 - 行业知识和分析模板\n", "• 🔍 查询引擎 - 自然语言转SQL和数据查询\n", "• 💬 AI对话 - 专业的业务分析和建议\n", "\n", "📊 数据分析示例:\n", "• \"各品牌市场份额分析\"\n", "• \"用户行为趋势报告\"\n", "• \"地区收入分布情况\"\n", "• \"生成用户画像分析SQL\"\n", "\n", "🎯 业务洞察示例:\n", "• \"如何提升用户留存率？\"\n", "• \"充电宝投放策略优化建议\"\n", "• \"市场竞争分析和应对策略\"\n", "    \"\"\"\n", "    custom_print(help_text)\n", "\n", "def show_complete_system_status(assistant):\n", "    \"\"\"显示完整系统状态\"\"\"\n", "    try:\n", "        if hasattr(assistant, 'get_system_status'):\n", "            status = assistant.get_system_status()\n", "            \n", "            status_info = f\"\"\"\n", "📊 融合系统状态报告:\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "🕐 会话开始时间: {status.get('session_start', '未知')}\n", "💬 对话次数: {status.get('conversation_count', 0)}\n", "🔧 加载模块数: {status.get('module_count', 0)}\n", "🤖 AI服务状态: {status.get('ai_client_status', '未知')}\n", "🗄️ 数据库状态: {status.get('database_status', '未知')}\n", "📚 已加载模块: {', '.join(status.get('loaded_modules', []))}\n", "🚀 系统版本: v2.2 (完整集成版)\n", "            \"\"\"\n", "            custom_print(status_info)\n", "        else:\n", "            custom_print(\"📊 系统状态: 融合系统运行正常\")\n", "            \n", "    except Exception as e:\n", "        custom_print(f\"❌ 获取系统状态失败: {e}\")\n", "\n", "def show_modules_status(assistant):\n", "    \"\"\"显示模块状态\"\"\"\n", "    try:\n", "        if hasattr(assistant, 'modules'):\n", "            modules_info = \"\"\"\n", "🔧 模块状态详情:\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "            \"\"\"\n", "            \n", "            for module_name, module_obj in assistant.modules.items():\n", "                module_type = type(module_obj).__name__\n", "                modules_info += f\"• {module_name}: {module_type} ✅\\n\"\n", "            \n", "            custom_print(modules_info)\n", "        else:\n", "            custom_print(\"🔧 模块信息不可用\")\n", "            \n", "    except Exception as e:\n", "        custom_print(f\"❌ 获取模块状态失败: {e}\")\n", "\n", "def show_complete_goodbye_message(assistant):\n", "    \"\"\"显示完整系统告别信息\"\"\"\n", "    try:\n", "        if hasattr(assistant, 'get_system_status'):\n", "            status = assistant.get_system_status()\n", "            goodbye_msg = f\"\"\"\n", "╔═══════════════════════════════════════════════════════════╗\n", "║                    👋 感谢使用                             ║\n", "║            共享充电宝智能分析系统 v2.2                     ║\n", "║                   完整融合版                               ║\n", "╚═══════════════════════════════════════════════════════════╝\n", "\n", "📊 本次会话统计:\n", "• 对话次数: {status.get('conversation_count', 0)}\n", "• 使用模块: {', '.join(status.get('loaded_modules', []))}\n", "• 会话时长: {datetime.now() - datetime.fromisoformat(status.get('session_start', datetime.now().isoformat()).replace(' ', 'T'))}\n", "\n", "🎯 期待下次为您提供更好的服务！\n", "            \"\"\"\n", "            custom_print(goodbye_msg)\n", "        else:\n", "            custom_print(\"👋 感谢使用融合系统，再见！\")\n", "    except:\n", "        custom_print(\"👋 感谢使用，再见！\")\n", "\n", "# ===================================================================\n", "# 🛡️ 安全模式和应急模式 (保持原有功能)\n", "# ===================================================================\n", "\n", "def create_safe_assistant(config):\n", "    \"\"\"创建安全助手实例 - 降级方案\"\"\"\n", "    try:\n", "        from openai import OpenAI\n", "        import httpx\n", "        \n", "        http_client = httpx.Client(follow_redirects=True, timeout=30)\n", "        client = OpenAI(\n", "            api_key=config['api_config']['api_key'],\n", "            base_url=config['api_config']['base_url'],\n", "            http_client=http_client\n", "        )\n", "        \n", "        # 简化版助手类\n", "        class SafeAssistant:\n", "            def __init__(self, client, db_config):\n", "                self.client = client\n", "                self.db_config = db_config\n", "                self.conversation_history = []\n", "            \n", "            def analyze(self, question):\n", "                try:\n", "                    response = self.client.chat.completions.create(\n", "                        model=\"deepseek-chat\",\n", "                        messages=[\n", "                            {\"role\": \"system\", \"content\": \"你是共享充电宝数据分析专家。\"},\n", "                            {\"role\": \"user\", \"content\": question}\n", "                        ]\n", "                    )\n", "                    answer = response.choices[0].message.content\n", "                    return {\"response\": answer, \"status\": \"success\"}\n", "                except Exception as e:\n", "                    return {\"response\": f\"处理错误: {e}\", \"status\": \"error\"}\n", "        \n", "        return SafeAssistant(client, config['db_config'])\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"❌ 安全助手创建失败: {e}\")\n", "        return None\n", "\n", "def start_safe_chat_interface(assistant):\n", "    \"\"\"启动安全模式对话界面\"\"\"\n", "    custom_print(\"🛡️ 安全模式已启动\")\n", "    # 使用简化的对话界面\n", "    start_ai_chat_interface(assistant)\n", "\n", "def start_ai_chat_interface(assistant):\n", "    \"\"\"基础AI对话界面\"\"\"\n", "    custom_print(\"💬 AI对话界面已启动\")\n", "    custom_print(\"🤖 AI助手: 您好！有什么可以帮您的吗？\")\n", "    \n", "    while True:\n", "        try:\n", "            user_input = input(\"\\n👤 您: \").strip()\n", "            if user_input.lower() in ['quit', 'exit', '退出']:\n", "                custom_print(\"👋 再见！\")\n", "                break\n", "            \n", "            if hasattr(assistant, 'analyze'):\n", "                result = assistant.analyze(user_input)\n", "                response = result.get('response', str(result))\n", "                custom_print(f\"🤖 AI助手: {response}\")\n", "            else:\n", "                custom_print(\"🤖 AI助手: 功能异常\")\n", "                \n", "        except KeyboardInterrupt:\n", "            break\n", "        except Exception as e:\n", "            custom_print(f\"❌ 错误: {e}\")\n", "\n", "def quick_chat_mode():\n", "    \"\"\"快速聊天模式（应急方案）\"\"\"\n", "    custom_print(\"🚀 启动应急聊天模式...\")\n", "    \n", "    try:\n", "        from openai import OpenAI\n", "        import httpx\n", "        \n", "        http_client = httpx.Client(follow_redirects=True)\n", "        client = OpenAI(\n", "            api_key=\"sk-e2bad2bca73343a38f4f8d60f7435192\",\n", "            base_url=\"https://api.deepseek.com/v1\",\n", "            http_client=http_client\n", "        )\n", "        \n", "        custom_print(\"✅ 应急模式启动成功！\")\n", "        custom_print(\"🤖 AI助手: 您好！我是AI助手，有什么可以帮您的吗？\")\n", "        \n", "        while True:\n", "            user_input = input(\"\\n👤 您: \").strip()\n", "            if user_input.lower() in ['quit', 'exit', '退出']:\n", "                break\n", "            \n", "            try:\n", "                response = client.chat.completions.create(\n", "                    model=\"deepseek-chat\",\n", "                    messages=[\n", "                        {\"role\": \"system\", \"content\": \"你是共享充电宝数据分析专家。\"},\n", "                        {\"role\": \"user\", \"content\": user_input}\n", "                    ]\n", "                )\n", "                custom_print(f\"🤖 AI助手: {response.choices[0].message.content}\")\n", "            except Exception as e:\n", "                custom_print(f\"❌ 错误: {e}\")\n", "                \n", "    except Exception as e:\n", "        custom_print(f\"❌ 应急模式启动失败: {e}\")\n", "\n", "# ===================================================================\n", "# 🔧 辅助函数 (保持原有功能)\n", "# ===================================================================\n", "\n", "def print_startup_banner():\n", "    \"\"\"启动横幅\"\"\"\n", "    banner = f\"\"\"\n", "╔═══════════════════════════════════════════════════════════╗\n", "║        🚀 共享充电宝智能数据分析系统 v2.2                  ║\n", "║    🧠 智能记忆 | 📚 专家知识 | 🔍 查询引擎 | 💬 AI对话     ║\n", "║                   🔗 完整集成版                            ║\n", "╚═══════════════════════════════════════════════════════════╝\n", "🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n", "🔧 版本信息: v2.2 - 真正集成四大核心模块\n", "    \"\"\"\n", "    custom_print(banner)\n", "\n", "def check_basic_environment():\n", "    \"\"\"基础环境检查\"\"\"\n", "    required = ['pymysql', 'openai', 'httpx']\n", "    missing = []\n", "    \n", "    for pkg in required:\n", "        try:\n", "            __import__(pkg.replace('-', '_'))\n", "        except ImportError:\n", "            missing.append(pkg)\n", "    \n", "    if missing:\n", "        custom_print(f\"❌ 缺少必需依赖: {', '.join(missing)}\")\n", "        custom_print(f\"💡 安装命令: pip install {' '.join(missing)}\")\n", "        raise ImportError(\"必需依赖缺失\")\n", "    \n", "    custom_print(\"  ✅ 必需依赖检查通过\")\n", "\n", "def get_system_config():\n", "    \"\"\"获取系统配置\"\"\"\n", "    return {\n", "        'db_config': {\n", "            'host': 'localhost',\n", "            'user': 'root',\n", "            'password': '',  \n", "            'database': 'ads', \n", "            'charset': 'utf8mb4',\n", "            'autocommit': True\n", "        },\n", "        'api_config': {\n", "            'api_key': \"sk-e2bad2bca73343a38f4f8d60f7435192\",\n", "            'base_url': \"https://api.deepseek.com/v1\",\n", "            'model': 'deepseek-chat'\n", "        },\n", "        'memory_config': {\n", "            'count_threshold': 25,\n", "            'token_threshold': 4000,\n", "            'use_token_mode': True\n", "        }\n", "    }\n", "\n", "def test_connections(config):\n", "    \"\"\"测试连接\"\"\"\n", "    # 测试数据库连接\n", "    try:\n", "        import pymysql\n", "        connection = pymysql.connect(**config['db_config'])\n", "        cursor = connection.cursor()\n", "        cursor.execute(\"SELECT DATABASE()\")\n", "        result = cursor.fetchone()\n", "        cursor.execute(\"SHOW TABLES\")\n", "        tables = cursor.fetchall()\n", "        custom_print(f\"  ✅ 数据库连接成功: {result[0]} (包含 {len(tables)} 张表)\")\n", "        cursor.close()\n", "        connection.close()\n", "    except Exception as e:\n", "        custom_print(f\"  ⚠️ 数据库连接失败: {e}\")\n", "        config['db_config'] = None\n", "    \n", "    # 测试API连接\n", "    try:\n", "        from openai import OpenAI\n", "        import httpx\n", "        \n", "        http_client = httpx.Client(follow_redirects=True, timeout=10)\n", "        client = OpenAI(\n", "            api_key=config['api_config']['api_key'],\n", "            base_url=config['api_config']['base_url'],\n", "            http_client=http_client\n", "        )\n", "        \n", "        response = client.chat.completions.create(\n", "            model=\"deepseek-chat\",\n", "            messages=[{\"role\": \"user\", \"content\": \"测试连接\"}],\n", "            max_tokens=5\n", "        )\n", "        custom_print(\"  ✅ AI API连接正常\")\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"  ⚠️ AI API连接异常: {e}\")\n", "\n", "# ===================================================================\n", "# 🎯 启动选项和主入口\n", "# ===================================================================\n", "\n", "def main():\n", "    \"\"\"主入口函数\"\"\"\n", "    custom_print(\"\"\"\n", "╔═══════════════════════════════════════════════════════════╗\n", "║        🚀 共享充电宝智能数据分析系统 v2.2                  ║\n", "║                   完整集成版启动                           ║\n", "╚═══════════════════════════════════════════════════════════╝\n", "\n", "🚀 启动选项:\n", "1. start_powerbank_system()  # 完整融合系统启动 (推荐)\n", "2. quick_chat_mode()         # 应急聊天模式\n", "\n", "💡 正在启动完整融合系统...\n", "    \"\"\")\n", "    \n", "    # 直接启动完整系统\n", "    start_powerbank_system()\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "\n", "# 兼容性函数\n", "def start_ai_chat():\n", "    \"\"\"AI对话启动函数 - 兼容接口\"\"\"\n", "    start_powerbank_system()\n", "\n", "# 启动提示\n", "custom_print(\"\"\"\n", "🚀 完整集成版启动器已加载！\n", "\n", "🔗 真正连接四大核心模块:\n", "• 🧠 PowerBankIntelligentMemory - 智能记忆系统\n", "• 📚 PowerBankExpertKnowledge - 专家知识库\n", "• 🔍 PowerBankIntelligentQueryEngine - 查询引擎\n", "• 💬 PowerBankIntelligentAssistant - 融合系统\n", "\n", "启动方式:\n", "• start_powerbank_system()  - 完整融合系统启动\n", "• quick_chat_mode()         - 应急聊天模式\n", "\n", "💡 推荐直接运行: start_powerbank_system()\n", "\"\"\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}