#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试饼图修复
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
import sys

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_pie_chart_fix():
    """测试修复后的饼图功能"""
    
    # 准备测试数据 - 品牌市场份额数据
    test_data = [
        {"品牌名称": "来电", "订单数量": 4963, "总收入": 9926.00, "市场份额": 26.60},
        {"品牌名称": "搜电", "订单数量": 5100, "总收入": 10200.00, "市场份额": 20.30},
        {"品牌名称": "盒电宝", "订单数量": 5076, "总收入": 15228.00, "市场份额": 19.60},
        {"品牌名称": "怪兽充电", "订单数量": 4800, "总收入": 14400.00, "市场份额": 13.60},
        {"品牌名称": "思腾智电", "订单数量": 4200, "总收入": 12600.00, "市场份额": 13.20},
        {"品牌名称": "街电", "订单数量": 3800, "总收入": 11400.00, "市场份额": 6.60}
    ]
    
    # 转换为DataFrame
    df = pd.DataFrame(test_data)
    
    print("🔍 测试数据:")
    print(df)
    print(f"\n📊 数据结构:")
    print(f"数值列: {df.select_dtypes(include=[np.number]).columns.tolist()}")
    print(f"分类列: {df.select_dtypes(include=['object']).columns.tolist()}")
    
    # 创建修复后的饼图函数
    def create_pie_chart_fixed(df: pd.DataFrame, ax, title: str):
        """修复后的饼图创建函数"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            # 智能选择最适合的列进行饼图展示
            # 优先选择市场份额、占比等列
            target_col = None
            for col in numeric_cols:
                if any(keyword in col for keyword in ['份额', '占比', '比例', '百分比']):
                    target_col = col
                    break
            
            # 如果没有找到特定列，使用第一个数值列
            if target_col is None:
                target_col = numeric_cols[0]
            
            print(f"📈 选择的数据列: {target_col}")
            
            # 只显示前8个，其余合并为"其他"
            if len(df) > 8:
                top_data = df.nlargest(7, target_col)
                others_sum = df[~df.index.isin(top_data.index)][target_col].sum()
                
                labels = list(top_data[categorical_cols[0]]) + ['其他']
                sizes = list(top_data[target_col]) + [others_sum]
            else:
                # 确保转换为list，避免pandas Series问题
                labels = list(df[categorical_cols[0]])
                sizes = list(df[target_col])
            
            print(f"🏷️ 标签: {labels}")
            print(f"📊 数值: {sizes}")
            
            # 过滤掉0值或负值
            filtered_data = [(l, s) for l, s in zip(labels, sizes) if s > 0]
            if not filtered_data:
                ax.text(0.5, 0.5, '无有效数据', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f"{title} - 占比分析")
                return
            
            labels, sizes = zip(*filtered_data)
            
            # 生成颜色
            colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
            
            # 创建饼图
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, autopct='%1.1f%%',
                                             colors=colors, startangle=90, 
                                             textprops={'fontsize': 10})
            
            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_weight('bold')
            
            ax.set_title(f"{title} - {target_col}占比分析", fontsize=14, fontweight='bold')
            
        else:
            # 处理数据结构不符合要求的情况
            ax.text(0.5, 0.5, f'数据结构不适合饼图\n分类列: {len(categorical_cols)}\n数值列: {len(numeric_cols)}', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            ax.set_title(f"{title} - 数据结构错误")
            
            # 显示数据结构信息
            info_text = f"数据列信息:\n"
            for i, col in enumerate(df.columns):
                dtype = str(df[col].dtype)
                info_text += f"{col}: {dtype}\n"
                if i >= 5:  # 最多显示6列
                    info_text += "..."
                    break
            
            ax.text(0.5, 0.2, info_text, ha='center', va='center', 
                   transform=ax.transAxes, fontsize=8, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.7))
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(12, 8))
    
    try:
        create_pie_chart_fixed(df, ax, "各品牌的份额占比")
        
        # 保存图表
        output_path = "powerbank_memory/test_pie_chart_fixed.png"
        os.makedirs("powerbank_memory", exist_ok=True)
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        
        print(f"\n✅ 饼图生成成功!")
        print(f"📁 保存路径: {output_path}")
        print(f"📏 文件大小: {os.path.getsize(output_path)} bytes")
        
        # 显示图表
        plt.show()
        
        return True
        
    except Exception as e:
        print(f"❌ 饼图生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        plt.close()

if __name__ == "__main__":
    print("🧪 开始测试饼图修复...")
    success = test_pie_chart_fix()
    
    if success:
        print("\n🎉 测试成功! 饼图修复有效!")
    else:
        print("\n❌ 测试失败! 需要进一步调试!")
