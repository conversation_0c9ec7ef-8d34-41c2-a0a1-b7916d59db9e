{"cells": [{"cell_type": "markdown", "id": "3419e8f6", "metadata": {}, "source": ["# 1.前置代码"]}, {"cell_type": "code", "execution_count": 3, "id": "4e2d645f", "metadata": {}, "outputs": [], "source": ["from rich.console import Console\n", "console = Console()\n", "def custom_print(info):\n", "    console.print(info)"]}, {"cell_type": "code", "execution_count": 4, "id": "399f2dc6", "metadata": {}, "outputs": [], "source": ["import os\n", "import io\n", "import json\n", "import httpx\n", "import inspect\n", "import numpy as np\n", "import pandas as pd\n", "import pymysql\n", "import openai\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 5, "id": "9c0e2de9", "metadata": {}, "outputs": [], "source": ["api_key = \"sk-5617b89342844050a48268b477327b44\"\n", "base_url = \"https://api.deepseek.com/v1\"\n", "http_client = httpx.Client(follow_redirects=True)\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "9fdea827", "metadata": {}, "outputs": [], "source": ["def get_user_info(sql,username='root',password='123456',db='user_info'):\n", "    \"\"\"\n", "    当前函数，用于查询指定数据库下的相关表中的数据\n", "    ：param sql：参数为必要参数,字符串类型的SQL语句,\n", "    ：param username：参数为可选参数,代表:用户名,字符串类型,\n", "    ：param password：参数为可选参数,代表:用户密码,字符串类型,\n", "    ：param db：参数为可选参数,代表:需要连接的数据库名字,字符串类型,\n", "    ：return:当前SQL语句执行完的查询结果\n", "    \"\"\"\n", "    conn = pymysql.connect(\n", "        host = 'localhost',\n", "        user = username,\n", "        password = password,\n", "        db = db,\n", "        charset = 'utf8'\n", "    )\n", "    try: \n", "        with conn.cursor() as cursor:\n", "            cursor.execute(sql)\n", "            results = cursor.fetchall()\n", "    finally:\n", "        cursor.close()\n", "        conn.close()\n", "    column_names = [desc[0] for desc in cursor.description]\n", "    df = pd.DataFrame(results, columns = column_names)\n", "    return df.to_json(orient = 'records') "]}, {"cell_type": "code", "execution_count": 7, "id": "378cb691", "metadata": {}, "outputs": [], "source": ["#构建函数的指针(引用)\n", "tools_list = {\"get_user_info\": get_user_info}\n", "\n", "#函数的说明(工具的说明)\n", "tools = [{'type': 'function',\n", "         'function': {\n", "            'name': 'get_user_info',\n", "            'description': '当前函数，用于查询指定数据库下的相关表中的数据。输入参数为字符串类型的SQL语句，输出为当前SQL语句执行完的查询结果',\n", "            'parameters': {'type': 'object',\n", "                           'properties': {'sql': {'description': '必要参数，字符串类型的SQL语句','type': 'string'},\n", "                                          'username': {'description': '用户名,字符串类型','type': 'string'},\n", "                                          'password': {'description': '用户密码,字符串类型','type': 'string'},\n", "                                          'db': {'description': '需要连接的数据库名字,字符串类型','type': 'string'}\n", "                                         },\n", "         'required': ['sql']},\n", "        }}]"]}, {"cell_type": "code", "execution_count": 8, "id": "7c2bc6bb", "metadata": {}, "outputs": [], "source": ["def auto_call(client,messages,tools=None,model='deepseek-chat'):\n", "    if tools == None :\n", "        response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages\n", "        )\n", "        return response.choices[0].message.content\n", "    else :\n", "        #函数第一次调用\n", "        first_response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages,\n", "            tools = tools,\n", "            tool_choice=\"auto\",\n", "        )\n", "        message_call = first_response.choices[0].message\n", "        tools_calls = message_call.tool_calls\n", "        if tools_calls != None:\n", "            print(\"进行二次调用\")\n", "        else:\n", "            print(\"没有进行二次调用\")\n", "        if tools_calls != None:\n", "            tool_call_id = tools_calls[0].id\n", "            func_name = first_response.choices[0].message.tool_calls[0].function.name\n", "            func_args = json.loads(tools_calls[0].function.arguments)['sql']\n", "            custom_print(func_args)\n", "            func_result = tools_list[func_name](func_args)\n", "            messages.append(message_call)\n", "            messages.append(\n", "                {\n", "                    \"role\":\"tool\",\n", "                    \"tool_call_id\":tool_call_id,\n", "                    \"name\":func_name,\n", "                    \"content\":func_result\n", "                }\n", "            )\n", "            #二次调用\n", "            second_response = client.chat.completions.create(\n", "                model=model,\n", "                messages=messages\n", "            )\n", "            return second_response.choices[0].message.content\n", "        else:\n", "            return first_response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 9, "id": "894ae67a", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\n", "\n", "```sql\n", "SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> AS total_records \n", "FROM user_base_info;\n", "```\n", "\n", "这条SQL会返回表中的总行数，列名为 `total_records`。\n", "\n", "注意事项：\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. 如果表很大，<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> 可能会比较耗时\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. 在某些数据库系统中，可以使用近似计数方法提高大表查询速度\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. 如果需要特定条件下的计数，可以添加WHERE子句\n", "\n", "您需要在实际数据库环境中执行此查询才能获取准确结果。<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">如果您能提供具体的数据库类型</span><span style=\"font-weight: bold\">(</span>MySQL, \n", "PostgreSQL等<span style=\"font-weight: bold\">)</span>，我可以给出更针对性的建议。\n", "</pre>\n"], "text/plain": ["要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\n", "\n", "```sql\n", "SELECT \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m AS total_records \n", "FROM user_base_info;\n", "```\n", "\n", "这条SQL会返回表中的总行数，列名为 `total_records`。\n", "\n", "注意事项：\n", "\u001b[1;36m1\u001b[0m. 如果表很大，\u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m 可能会比较耗时\n", "\u001b[1;36m2\u001b[0m. 在某些数据库系统中，可以使用近似计数方法提高大表查询速度\n", "\u001b[1;36m3\u001b[0m. 如果需要特定条件下的计数，可以添加WHERE子句\n", "\n", "您需要在实际数据库环境中执行此查询才能获取准确结果。\u001b[1;35m如果您能提供具体的数据库类型\u001b[0m\u001b[1m(\u001b[0mMySQL, \n", "PostgreSQL等\u001b[1m)\u001b[0m，我可以给出更针对性的建议。\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["m1 = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "]\n", "custom_print(auto_call(client,m1))"]}, {"cell_type": "code", "execution_count": 10, "id": "5be0b6c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">今天下课吃什么可以根据你的口味和心情来决定！以下是一些常见的建议：\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **中餐**：\n", "   - 麻辣烫/冒菜：适合喜欢辣味的人。\n", "   - 盖浇饭/炒饭：简单快捷。\n", "   - 面条/米线：热腾腾的汤面很适合放松。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **西餐**：\n", "   - 汉堡/薯条：快速解决饥饿。\n", "   - 披萨：适合和朋友一起分享。\n", "   - 沙拉：健康轻食的选择。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. **日韩料理**：\n", "   - 寿司/刺身：清淡又美味。\n", "   - 韩式拌饭/烤肉：适合喜欢重口味的人。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>. **小吃**：\n", "   - 煎饼果子/手抓饼：街头小吃，方便快捷。\n", "   - 炸鸡/烧烤：解馋必备。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>. **甜点**：\n", "   - 奶茶/咖啡：搭配小点心。\n", "   - 冰淇淋：适合夏天。\n", "\n", "如果你有具体的偏好或者附近有什么餐厅，可以告诉我，我可以帮你更具体地推荐！\n", "</pre>\n"], "text/plain": ["今天下课吃什么可以根据你的口味和心情来决定！以下是一些常见的建议：\n", "\n", "\u001b[1;36m1\u001b[0m. **中餐**：\n", "   - 麻辣烫/冒菜：适合喜欢辣味的人。\n", "   - 盖浇饭/炒饭：简单快捷。\n", "   - 面条/米线：热腾腾的汤面很适合放松。\n", "\n", "\u001b[1;36m2\u001b[0m. **西餐**：\n", "   - 汉堡/薯条：快速解决饥饿。\n", "   - 披萨：适合和朋友一起分享。\n", "   - 沙拉：健康轻食的选择。\n", "\n", "\u001b[1;36m3\u001b[0m. **日韩料理**：\n", "   - 寿司/刺身：清淡又美味。\n", "   - 韩式拌饭/烤肉：适合喜欢重口味的人。\n", "\n", "\u001b[1;36m4\u001b[0m. **小吃**：\n", "   - 煎饼果子/手抓饼：街头小吃，方便快捷。\n", "   - 炸鸡/烧烤：解馋必备。\n", "\n", "\u001b[1;36m5\u001b[0m. **甜点**：\n", "   - 奶茶/咖啡：搭配小点心。\n", "   - 冰淇淋：适合夏天。\n", "\n", "如果你有具体的偏好或者附近有什么餐厅，可以告诉我，我可以帮你更具体地推荐！\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["m2 = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问今天下课吃啥？\"}\n", "]\n", "custom_print(auto_call(client,m2,tools))"]}, {"cell_type": "code", "execution_count": 11, "id": "4f7cda61", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> AS total_records FROM user_base_info\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m AS total_records FROM user_base_info\n"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "OperationalError", "evalue": "(1045, \"Access denied for user 'root'@'localhost' (using password: YES)\")", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mOperationalError\u001b[39m                          <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[11]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      1\u001b[39m m3 = [    \n\u001b[32m      2\u001b[39m     {\u001b[33m\"\u001b[39m\u001b[33mrole\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33msystem\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mcontent\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33m你是大数据领域的数据专家,精通各种简单和复杂的数据分析\u001b[39m\u001b[33m\"\u001b[39m},\n\u001b[32m      3\u001b[39m     {\u001b[33m\"\u001b[39m\u001b[33mrole\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33muser\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mcontent\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33m请问user_base_info数据表中一共有多少条数据？\u001b[39m\u001b[33m\"\u001b[39m}\n\u001b[32m      4\u001b[39m ]\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m \u001b[43mauto_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43mclient\u001b[49m\u001b[43m,\u001b[49m\u001b[43mm3\u001b[49m\u001b[43m,\u001b[49m\u001b[43mtools\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 27\u001b[39m, in \u001b[36mauto_call\u001b[39m\u001b[34m(client, messages, tools, model)\u001b[39m\n\u001b[32m     25\u001b[39m func_args = json.loads(tools_calls[\u001b[32m0\u001b[39m].function.arguments)[\u001b[33m'\u001b[39m\u001b[33msql\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m     26\u001b[39m custom_print(func_args)\n\u001b[32m---> \u001b[39m\u001b[32m27\u001b[39m func_result = \u001b[43mtools_list\u001b[49m\u001b[43m[\u001b[49m\u001b[43mfunc_name\u001b[49m\u001b[43m]\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfunc_args\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     28\u001b[39m messages.append(message_call)\n\u001b[32m     29\u001b[39m messages.append(\n\u001b[32m     30\u001b[39m     {\n\u001b[32m     31\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mrole\u001b[39m\u001b[33m\"\u001b[39m:\u001b[33m\"\u001b[39m\u001b[33mtool\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     35\u001b[39m     }\n\u001b[32m     36\u001b[39m )\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 10\u001b[39m, in \u001b[36mget_user_info\u001b[39m\u001b[34m(sql, username, password, db)\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mget_user_info\u001b[39m(sql,username=\u001b[33m'\u001b[39m\u001b[33mroot\u001b[39m\u001b[33m'\u001b[39m,password=\u001b[33m'\u001b[39m\u001b[33m123456\u001b[39m\u001b[33m'\u001b[39m,db=\u001b[33m'\u001b[39m\u001b[33muser_info\u001b[39m\u001b[33m'\u001b[39m):\n\u001b[32m      2\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[33;03m    当前函数，用于查询指定数据库下的相关表中的数据\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[33;03m    ：param sql：参数为必要参数,字符串类型的SQL语句,\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m      8\u001b[39m \u001b[33;03m    ：return:当前SQL语句执行完的查询结果\u001b[39;00m\n\u001b[32m      9\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m     conn = \u001b[43mpymysql\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     11\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhost\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mlocalhost\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     12\u001b[39m \u001b[43m        \u001b[49m\u001b[43muser\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43musername\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     13\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     14\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdb\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mdb\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     15\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcharset\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mutf8\u001b[39;49m\u001b[33;43m'\u001b[39;49m\n\u001b[32m     16\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     17\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m: \n\u001b[32m     18\u001b[39m         \u001b[38;5;28;01mwith\u001b[39;00m conn.cursor() \u001b[38;5;28;01mas\u001b[39;00m cursor:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\connections.py:361\u001b[39m, in \u001b[36mConnection.__init__\u001b[39m\u001b[34m(self, user, password, host, database, unix_socket, port, charset, collation, sql_mode, read_default_file, conv, use_unicode, client_flag, cursorclass, init_command, connect_timeout, read_default_group, autocommit, local_infile, max_allowed_packet, defer_connect, auth_plugin_map, read_timeout, write_timeout, bind_address, binary_prefix, program_name, server_public_key, ssl, ssl_ca, ssl_cert, ssl_disabled, ssl_key, ssl_key_password, ssl_verify_cert, ssl_verify_identity, compress, named_pipe, passwd, db)\u001b[39m\n\u001b[32m    359\u001b[39m     \u001b[38;5;28mself\u001b[39m._sock = \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m    360\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m361\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\connections.py:669\u001b[39m, in \u001b[36mConnection.connect\u001b[39m\u001b[34m(self, sock)\u001b[39m\n\u001b[32m    666\u001b[39m \u001b[38;5;28mself\u001b[39m._next_seq_id = \u001b[32m0\u001b[39m\n\u001b[32m    668\u001b[39m \u001b[38;5;28mself\u001b[39m._get_server_information()\n\u001b[32m--> \u001b[39m\u001b[32m669\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_request_authentication\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    671\u001b[39m \u001b[38;5;66;03m# Send \"SET NAMES\" query on init for:\u001b[39;00m\n\u001b[32m    672\u001b[39m \u001b[38;5;66;03m# - Ensure charaset (and collation) is set to the server.\u001b[39;00m\n\u001b[32m    673\u001b[39m \u001b[38;5;66;03m#   - collation_id in handshake packet may be ignored.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    682\u001b[39m \u001b[38;5;66;03m# - https://github.com/wagtail/wagtail/issues/9477\u001b[39;00m\n\u001b[32m    683\u001b[39m \u001b[38;5;66;03m# - https://zenn.dev/methane/articles/2023-mysql-collation (Japanese)\u001b[39;00m\n\u001b[32m    684\u001b[39m \u001b[38;5;28mself\u001b[39m.set_character_set(\u001b[38;5;28mself\u001b[39m.charset, \u001b[38;5;28mself\u001b[39m.collation)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\connections.py:979\u001b[39m, in \u001b[36mConnection._request_authentication\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    977\u001b[39m \u001b[38;5;66;03m# https://dev.mysql.com/doc/internals/en/successful-authentication.html\u001b[39;00m\n\u001b[32m    978\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._auth_plugin_name == \u001b[33m\"\u001b[39m\u001b[33mcaching_sha2_password\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m979\u001b[39m     auth_packet = \u001b[43m_auth\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcaching_sha2_password_auth\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mauth_packet\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    980\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._auth_plugin_name == \u001b[33m\"\u001b[39m\u001b[33msha256_password\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m    981\u001b[39m     auth_packet = _auth.sha256_password_auth(\u001b[38;5;28mself\u001b[39m, auth_packet)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\_auth.py:268\u001b[39m, in \u001b[36mcaching_sha2_password_auth\u001b[39m\u001b[34m(conn, pkt)\u001b[39m\n\u001b[32m    265\u001b[39m         \u001b[38;5;28mprint\u001b[39m(conn.server_public_key.decode(\u001b[33m\"\u001b[39m\u001b[33mascii\u001b[39m\u001b[33m\"\u001b[39m))\n\u001b[32m    267\u001b[39m data = sha2_rsa_encrypt(conn.password, conn.salt, conn.server_public_key)\n\u001b[32m--> \u001b[39m\u001b[32m268\u001b[39m pkt = \u001b[43m_roundtrip\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\_auth.py:121\u001b[39m, in \u001b[36m_roundtrip\u001b[39m\u001b[34m(conn, send_data)\u001b[39m\n\u001b[32m    119\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_roundtrip\u001b[39m(conn, send_data):\n\u001b[32m    120\u001b[39m     conn.write_packet(send_data)\n\u001b[32m--> \u001b[39m\u001b[32m121\u001b[39m     pkt = \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_read_packet\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    122\u001b[39m     pkt.check_error()\n\u001b[32m    123\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m pkt\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\connections.py:775\u001b[39m, in \u001b[36mConnection._read_packet\u001b[39m\u001b[34m(self, packet_type)\u001b[39m\n\u001b[32m    773\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._result \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._result.unbuffered_active \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m    774\u001b[39m         \u001b[38;5;28mself\u001b[39m._result.unbuffered_active = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m775\u001b[39m     \u001b[43mpacket\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraise_for_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    776\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m packet\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\protocol.py:219\u001b[39m, in \u001b[36mMysqlPacket.raise_for_error\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    217\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m DEBUG:\n\u001b[32m    218\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33merrno =\u001b[39m\u001b[33m\"\u001b[39m, errno)\n\u001b[32m--> \u001b[39m\u001b[32m219\u001b[39m \u001b[43merr\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraise_mysql_exception\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_data\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\err.py:150\u001b[39m, in \u001b[36mraise_mysql_exception\u001b[39m\u001b[34m(data)\u001b[39m\n\u001b[32m    148\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m errorclass \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    149\u001b[39m     errorclass = InternalError \u001b[38;5;28;01mif\u001b[39;00m errno < \u001b[32m1000\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m OperationalError\n\u001b[32m--> \u001b[39m\u001b[32m150\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m errorclass(errno, errval)\n", "\u001b[31mOperationalError\u001b[39m: (1045, \"Access denied for user 'root'@'localhost' (using password: YES)\")"]}], "source": ["m3 = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "]\n", "auto_call(client,m3,tools)"]}, {"cell_type": "markdown", "id": "afc26b97", "metadata": {}, "source": ["# 2.多轮对话核心流程"]}, {"cell_type": "markdown", "id": "fefdbee9", "metadata": {}, "source": ["## 2.1 能力边界测试"]}, {"cell_type": "markdown", "id": "93432c48", "metadata": {}, "source": ["测试大语言模型回复涉及私有化知识问题的能力边界"]}, {"cell_type": "code", "execution_count": null, "id": "75d88db4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">FLOOR</span><span style=\"font-weight: bold\">(</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">AVG</span><span style=\"font-weight: bold\">(</span>age<span style=\"font-weight: bold\">))</span> AS average_age FROM user_base_info;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mFLOOR\u001b[0m\u001b[1m(\u001b[0m\u001b[1;35mAVG\u001b[0m\u001b[1m(\u001b[0mage\u001b[1m)\u001b[0m\u001b[1m)\u001b[0m AS average_age FROM user_base_info;\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'根据计算结果，user_base_info数据表中所有人员的平均年龄是29岁（保留整数）。'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": null, "id": "7f8991c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'[{\"average_age\":29.7542}]'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_info(\"SELECT AVG(age) AS average_age FROM user_base_info ;\")"]}, {"cell_type": "markdown", "id": "a27da459", "metadata": {}, "source": ["报错的原因：大模型不知道本地的私有化知识"]}, {"cell_type": "code", "execution_count": null, "id": "a7738c5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> AS total_siblings_spouses FROM user_base_info GROUP BY user_id ORDER BY total_siblings_spouses DESC\n", "LIMIT <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m AS total_siblings_spouses FROM user_base_info GROUP BY user_id ORDER BY total_siblings_spouses DESC\n", "LIMIT \u001b[1;36m1\u001b[0m;\n"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "OperationalError", "evalue": "(1054, \"Unknown column 'user_id' in 'group statement'\")", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mOperationalError\u001b[0m                          <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[9], line 5\u001b[0m\n\u001b[0;32m      1\u001b[0m messages \u001b[38;5;241m=\u001b[39m [    \n\u001b[0;32m      2\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msystem\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m你是大数据领域的数据专家,精通各种简单和复杂的数据分析\u001b[39m\u001b[38;5;124m\"\u001b[39m},\n\u001b[0;32m      3\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m请问user_base_info数据表兄弟姐妹以及配偶总人数，最多的为多少？\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[0;32m      4\u001b[0m ]\n\u001b[1;32m----> 5\u001b[0m auto_call(client,messages,tools)\n", "Cell \u001b[1;32mIn[6], line 27\u001b[0m, in \u001b[0;36mauto_call\u001b[1;34m(client, messages, tools, model)\u001b[0m\n\u001b[0;32m     25\u001b[0m func_args \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(tools_calls[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mfunction\u001b[38;5;241m.\u001b[39marguments)[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msql\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m     26\u001b[0m custom_print(func_args)\n\u001b[1;32m---> 27\u001b[0m func_result \u001b[38;5;241m=\u001b[39m tools_list[func_name](func_args)\n\u001b[0;32m     28\u001b[0m messages\u001b[38;5;241m.\u001b[39mappend(message_call)\n\u001b[0;32m     29\u001b[0m messages\u001b[38;5;241m.\u001b[39mappend(\n\u001b[0;32m     30\u001b[0m     {\n\u001b[0;32m     31\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124<PERSON><PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m:\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtool\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     35\u001b[0m     }\n\u001b[0;32m     36\u001b[0m )\n", "Cell \u001b[1;32mIn[4], line 19\u001b[0m, in \u001b[0;36mget_user_info\u001b[1;34m(sql, username, password, db)\u001b[0m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m: \n\u001b[0;32m     18\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m conn\u001b[38;5;241m.\u001b[39mcursor() \u001b[38;5;28;01mas\u001b[39;00m cursor:\n\u001b[1;32m---> 19\u001b[0m         cursor\u001b[38;5;241m.\u001b[39mexecute(sql)\n\u001b[0;32m     20\u001b[0m         results \u001b[38;5;241m=\u001b[39m cursor\u001b[38;5;241m.\u001b[39mfe<PERSON>all()\n\u001b[0;32m     21\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\pymysql\\cursors.py:153\u001b[0m, in \u001b[0;36mCursor.execute\u001b[1;34m(self, query, args)\u001b[0m\n\u001b[0;32m    149\u001b[0m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[0;32m    151\u001b[0m query \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmogrify(query, args)\n\u001b[1;32m--> 153\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_query(query)\n\u001b[0;32m    154\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_executed \u001b[38;5;241m=\u001b[39m query\n\u001b[0;32m    155\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\pymysql\\cursors.py:322\u001b[0m, in \u001b[0;36mCursor._query\u001b[1;34m(self, q)\u001b[0m\n\u001b[0;32m    320\u001b[0m conn \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_db()\n\u001b[0;32m    321\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_clear_result()\n\u001b[1;32m--> 322\u001b[0m conn\u001b[38;5;241m.\u001b[39mquery(q)\n\u001b[0;32m    323\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_do_get_result()\n\u001b[0;32m    324\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrowcount\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\pymysql\\connections.py:563\u001b[0m, in \u001b[0;36mConnection.query\u001b[1;34m(self, sql, unbuffered)\u001b[0m\n\u001b[0;32m    561\u001b[0m     sql \u001b[38;5;241m=\u001b[39m sql\u001b[38;5;241m.\u001b[39mencode(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mencoding, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msurrogateescape\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    562\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_execute_command(COMMAND\u001b[38;5;241m.\u001b[39mCOM_QUERY, sql)\n\u001b[1;32m--> 563\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_affected_rows \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read_query_result(unbuffered\u001b[38;5;241m=\u001b[39munbuffered)\n\u001b[0;32m    564\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_affected_rows\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\pymysql\\connections.py:825\u001b[0m, in \u001b[0;36mConnection._read_query_result\u001b[1;34m(self, unbuffered)\u001b[0m\n\u001b[0;32m    823\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    824\u001b[0m     result \u001b[38;5;241m=\u001b[39m MySQLResult(\u001b[38;5;28mself\u001b[39m)\n\u001b[1;32m--> 825\u001b[0m     result\u001b[38;5;241m.\u001b[39mread()\n\u001b[0;32m    826\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result \u001b[38;5;241m=\u001b[39m result\n\u001b[0;32m    827\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m result\u001b[38;5;241m.\u001b[39mserver_status \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\pymysql\\connections.py:1199\u001b[0m, in \u001b[0;36mMySQLResult.read\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1197\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mread\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m   1198\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1199\u001b[0m         first_packet \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconnection\u001b[38;5;241m.\u001b[39m_read_packet()\n\u001b[0;32m   1201\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m first_packet\u001b[38;5;241m.\u001b[39mis_ok_packet():\n\u001b[0;32m   1202\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read_ok_packet(first_packet)\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\pymysql\\connections.py:775\u001b[0m, in \u001b[0;36mConnection._read_packet\u001b[1;34m(self, packet_type)\u001b[0m\n\u001b[0;32m    773\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\u001b[38;5;241m.\u001b[39munbuffered_active \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m    774\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\u001b[38;5;241m.\u001b[39munbuffered_active \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[1;32m--> 775\u001b[0m     packet\u001b[38;5;241m.\u001b[39mraise_for_error()\n\u001b[0;32m    776\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m packet\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\pymysql\\protocol.py:219\u001b[0m, in \u001b[0;36mMysqlPacket.raise_for_error\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    217\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m DEBUG:\n\u001b[0;32m    218\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124merrno =\u001b[39m\u001b[38;5;124m\"\u001b[39m, errno)\n\u001b[1;32m--> 219\u001b[0m err\u001b[38;5;241m.\u001b[39mraise_mysql_exception(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data)\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\pymysql\\err.py:150\u001b[0m, in \u001b[0;36mraise_mysql_exception\u001b[1;34m(data)\u001b[0m\n\u001b[0;32m    148\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m errorclass \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m:\n\u001b[0;32m    149\u001b[0m     errorclass \u001b[38;5;241m=\u001b[39m InternalError \u001b[38;5;28;01mif\u001b[39;00m errno \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m1000\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m OperationalError\n\u001b[1;32m--> 150\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m errorclass(errno, errval)\n", "\u001b[1;31mOperationalError\u001b[0m: (1054, \"Unknown column 'user_id' in 'group statement'\")"]}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表兄弟姐妹以及配偶总人数，最多的为多少？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": null, "id": "5867d151", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">为了回答您的问题，我需要查询相关的数据表以获取遇难人数和非遇难人数的统计信息。请提供以下信息以便我进行查询：\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **数据库名称**：您需要查询的数据库名称。\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **表名称**：包含出行数据的表名称。\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. **字段名称**：表中用于标识“遇难”和“非遇难”的字段名称（例如，是否有“status”或“outcome”字段）。\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>. **其他筛选条件**：如果有其他筛选条件（如出行时间、地点等），请一并提供。\n", "\n", "如果您能提供这些信息，我可以编写SQL查询并为您获取结果。\n", "</pre>\n"], "text/plain": ["为了回答您的问题，我需要查询相关的数据表以获取遇难人数和非遇难人数的统计信息。请提供以下信息以便我进行查询：\n", "\n", "\u001b[1;36m1\u001b[0m. **数据库名称**：您需要查询的数据库名称。\n", "\u001b[1;36m2\u001b[0m. **表名称**：包含出行数据的表名称。\n", "\u001b[1;36m3\u001b[0m. **字段名称**：表中用于标识“遇难”和“非遇难”的字段名称（例如，是否有“status”或“outcome”字段）。\n", "\u001b[1;36m4\u001b[0m. **其他筛选条件**：如果有其他筛选条件（如出行时间、地点等），请一并提供。\n", "\n", "如果您能提供这些信息，我可以编写SQL查询并为您获取结果。\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问本次出行中，遇难人数和非遇难人数，分别是多少人？\"}\n", "]\n", "custom_print(auto_call(client,messages,tools))"]}, {"cell_type": "markdown", "id": "1bbadb19", "metadata": {}, "source": ["## 2.2 加载系统知识"]}, {"cell_type": "code", "execution_count": null, "id": "9e9aaab4", "metadata": {}, "outputs": [{"data": {"text/plain": ["'# 表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch 字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare 字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked 字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量，0 表示乘客没有在泰坦尼克号事故中幸存，1 表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” 等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理性别，只有 “male”（男性）和 “female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” 的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n```\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp 类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(r'C:\\Users\\<USER>\\Desktop\\data.md', 'r', encoding='utf-8') as f:\n", "    system_info = f.read()\n", "system_info"]}, {"cell_type": "code", "execution_count": null, "id": "53b94578", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">MAX</span><span style=\"font-weight: bold\">(</span>sib_sp<span style=\"font-weight: bold\">)</span> AS max_siblings_spouse FROM user_base_info;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mMAX\u001b[0m\u001b[1m(\u001b[0msib_sp\u001b[1m)\u001b[0m AS max_siblings_spouse FROM user_base_info;\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'根据查询结果，user_base_info数据表中记录的兄弟姐妹以及配偶总人数(sib_sp字段)的最大值为8人。'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"system\", \"content\": \"数据库中表和字段的附加说明:%s\" % system_info},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表兄弟姐妹以及配偶总人数，最多的为多少？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": null, "id": "b4be0b5e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'[{\"max_sib_sp\":\"8\"}]'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_info(\"SELECT MAX(sib_sp) AS max_sib_sp FROM user_base_info ;\")"]}, {"cell_type": "code", "execution_count": null, "id": "a9e208d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT survived, <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> as count FROM is_survived GROUP BY survived;\n", "</pre>\n"], "text/plain": ["SELECT survived, \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m as count FROM is_survived GROUP BY survived;\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">根据泰坦尼克号乘客数据统计结果：\n", "\n", "- 遇难人数：549人（<span style=\"color: #808000; text-decoration-color: #808000\">survived</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>）\n", "- 幸存人数：342人（<span style=\"color: #808000; text-decoration-color: #808000\">survived</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>）\n", "\n", "总乘客数为891人（<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">549</span>+<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">342</span>）。\n", "</pre>\n"], "text/plain": ["根据泰坦尼克号乘客数据统计结果：\n", "\n", "- 遇难人数：549人（\u001b[33msurvived\u001b[0m=\u001b[1;36m0\u001b[0m）\n", "- 幸存人数：342人（\u001b[33msurvived\u001b[0m=\u001b[1;36m1\u001b[0m）\n", "\n", "总乘客数为891人（\u001b[1;36m549\u001b[0m+\u001b[1;36m342\u001b[0m）。\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"system\", \"content\": \"数据库中表和字段的附加说明:%s\" % system_info},\n", "    {\"role\": \"user\", \"content\": \"请问本次出行中，遇难人数和非遇难人数，分别是多少人？\"}\n", "]\n", "custom_print(auto_call(client,messages,tools))"]}, {"cell_type": "code", "execution_count": null, "id": "3945dd95", "metadata": {}, "outputs": [{"data": {"text/plain": ["'[{\"survived\":\"0\",\"count\":549},{\"survived\":\"1\",\"count\":342}]'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_info(\"SELECT survived, COUNT(*) AS count FROM is_survived GROUP BY survived ;\")"]}, {"cell_type": "markdown", "id": "7591d54a", "metadata": {}, "source": ["## 2.3 多轮对话"]}, {"cell_type": "code", "execution_count": null, "id": "8c3c9885", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '你是大数据领域的数据专家,精通各种简单和复杂的数据分析'},\n", " {'role': 'system',\n", "  'content': '数据库中表和字段的附加说明:# 表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch 字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare 字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked 字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量，0 表示乘客没有在泰坦尼克号事故中幸存，1 表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” 等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理性别，只有 “male”（男性）和 “female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” 的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n```\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp 类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'},\n", " {'role': 'user', 'content': '请问本次出行中，遇难人数和非遇难人数，分别是多少人？'},\n", " ChatCompletionMessage(content='', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_0_e7d39e96-bd3f-4f56-a152-9a90b687e648', function=Function(arguments='{\"sql\":\"SELECT survived, COUNT(*) as count FROM is_survived GROUP BY survived;\"}', name='get_user_info'), type='function', index=0)]),\n", " {'role': 'tool',\n", "  'tool_call_id': 'call_0_e7d39e96-bd3f-4f56-a152-9a90b687e648',\n", "  'name': 'get_user_info',\n", "  'content': '[{\"survived\":\"0\",\"count\":549},{\"survived\":\"1\",\"count\":342}]'}]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["messages"]}, {"cell_type": "markdown", "id": "d73e5a29", "metadata": {}, "source": ["功能：用户输入问题，由大模型决定自己回复还是调用工具回复，大模型可以回复问题，多轮。"]}, {"cell_type": "markdown", "id": "814ce43c", "metadata": {}, "source": ["```\n", "system_base_info 角色设定\n", "system_extend_info 本地私有化知识\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "05fe3240", "metadata": {}, "outputs": [], "source": ["def auto_chat(client,\n", "              user_input,\n", "              system_base_info=\"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\",\n", "              system_extend_info=\"\",\n", "              tools = None,\n", "              model=\"deepseek-chat\"):\n", "    system_message = {\"role\":\"system\",\"content\":system_base_info + \"%s\" %system_extend_info}\n", "    user_message={\"role\":\"user\",\"content\":user_input}\n", "    messages = []\n", "    messages.append(system_message)\n", "    messages.append(user_message)\n", "    while True:           \n", "        result = auto_call(client,messages,tools,model=model)\n", "        print(f\"模型回复: {result}\")\n", "        user_input = input(\"您还有想要提问的问题？(输入0结束对话): \")\n", "        messages.clear()\n", "        messages.append(system_message)\n", "        messages.append({\"role\":\"user\",\"content\":user_input})\n", "        custom_print(messages)\n", "        print(\"================================================================================\")\n", "        if user_input == \"0\":\n", "            break"]}, {"cell_type": "code", "execution_count": null, "id": "1900bcb7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ROUND</span><span style=\"font-weight: bold\">(</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">AVG</span><span style=\"font-weight: bold\">(</span>age<span style=\"font-weight: bold\">))</span> AS average_age FROM user_base_info;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mROUND\u001b[0m\u001b[1m(\u001b[0m\u001b[1;35mAVG\u001b[0m\u001b[1m(\u001b[0mage\u001b[1m)\u001b[0m\u001b[1m)\u001b[0m AS average_age FROM user_base_info;\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["模型回复: user_base_info数据表中所有人员的平均年龄是30岁（保留整数）。\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，0 表示乘客没有在泰坦尼克号事故中幸存，1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">性别，只有 “male”（男性）和 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'在舟山周末去哪里玩呢？第一次来'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# \u001b[0m\n", "\u001b[32m表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，\u001b[0m\n", "\u001b[32m需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mpassenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mage 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch \u001b[0m\n", "\u001b[32m字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked \u001b[0m\n", "\u001b[32m字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 \u001b[0m\n", "\u001b[32msurvived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 \u001b[0m\n", "\u001b[32mID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量\u001b[0m\n", "\u001b[32m，0 表示乘客没有在泰坦尼克号事故中幸存，1 \u001b[0m\n", "\u001b[32m表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 \u001b[0m\n", "\u001b[32m代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 \u001b[0m\n", "\u001b[32m代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存\u001b[0m\n", "\u001b[32m概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测\u001b[0m\n", "\u001b[32m生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” \u001b[0m\n", "\u001b[32m等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理\u001b[0m\n", "\u001b[32m性别，只有 “male”（男性）和 \u001b[0m\n", "\u001b[32m“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” \u001b[0m\n", "\u001b[32m的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，\u001b[0m\n", "\u001b[32m不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在\u001b[0m\n", "\u001b[32m灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直\u001b[0m\n", "\u001b[32m系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如\u001b[0m\n", "\u001b[32m，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`\u001b[0m\n", "\u001b[32m``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp \u001b[0m\n", "\u001b[32m类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩\u001b[0m\n", "\u001b[32m子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预\u001b[0m\n", "\u001b[32m测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare\u001b[0m\n", "\u001b[32m（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经\u001b[0m\n", "\u001b[32m济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置\u001b[0m\n", "\u001b[32m，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口\u001b[0m\n", "\u001b[32m，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并\u001b[0m\n", "\u001b[32m且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'在舟山周末去哪里玩呢？第一次来'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "没有进行二次调用\n", "模型回复: 舟山是一个风景优美、充满海岛风情的城市，周末来这里可以体验到丰富的自然景观和人文活动。以下是一些推荐的地方和活动，适合第一次来舟山的游客：\n", "\n", "---\n", "\n", "### **1. 普陀山**\n", "- **推荐理由**：普陀山是中国四大佛教名山之一，以观音文化闻名，风景秀丽，寺庙众多。\n", "- **玩法建议**：\n", "  - 参观普济寺、法雨寺、慧济寺等著名寺庙。\n", "  - 徒步或乘坐缆车登上佛顶山，俯瞰全岛美景。\n", "  - 在海边散步，感受宁静的海岛氛围。\n", "- **适合人群**：喜欢文化、宗教和自然风光的游客。\n", "\n", "---\n", "\n", "### **2. 朱家尖**\n", "- **推荐理由**：朱家尖是舟山著名的海滨度假胜地，沙滩细腻，海水清澈。\n", "- **玩法建议**：\n", "  - 在南沙海滩游泳、玩沙或参加水上项目（如摩托艇、帆船）。\n", "  - 参观乌石塘景区，欣赏独特的黑色鹅卵石海滩。\n", "  - 晚上可以看沙雕艺术展（夏季限定）。\n", "- **适合人群**：家庭、情侣或喜欢海滩活动的游客。\n", "\n", "---\n", "\n", "### **3. 东极岛**\n", "- **推荐理由**：东极岛是中国最东端的有人居住岛屿，风景原始，海水湛蓝。\n", "- **玩法建议**：\n", "  - 徒步环岛，欣赏日出和日落。\n", "  - 体验渔家乐，品尝新鲜的海鲜。\n", "  - 参观《后会无期》电影取景地。\n", "- **适合人群**：喜欢小众、文艺和摄影的游客。\n", "\n", "---\n", "\n", "### **4. 嵊泗列岛**\n", "- **推荐理由**：嵊泗列岛由多个小岛组成，每个岛都有独特的风景和玩法。\n", "- **玩法建议**：\n", "  - 在枸杞岛或嵊山岛体验渔村生活。\n", "  - 参观无人村（绿野仙踪般的废弃村落）。\n", "  - 在基湖沙滩或南长涂沙滩放松。\n", "- **适合人群**：喜欢慢节奏和探索小众景点的游客。\n", "\n", "---\n", "\n", "### **5. 沈家门渔港**\n", "- **推荐理由**：沈家门是中国最大的渔港之一，以海鲜闻名。\n", "- **玩法建议**：\n", "  - 傍晚逛渔港夜市，品尝新鲜的海鲜大排档。\n", "  - 参观舟山国际水产城，了解当地渔业文化。\n", "- **适合人群**：吃货和喜欢体验当地生活的游客。\n", "\n", "---\n", "\n", "### **6. 岱山岛**\n", "- **推荐理由**：岱山岛有丰富的自然和人文景观，适合短途旅行。\n", "- **玩法建议**：\n", "  - 参观东沙古镇，感受渔镇的历史风貌。\n", "  - 在鹿栏晴沙（华东最长沙滩）散步或骑行。\n", "- **适合人群**：喜欢历史文化和自然风光的游客。\n", "\n", "---\n", "\n", "### **小贴士**\n", "1. **交通**：舟山各岛之间需要乘船，建议提前查询船班时间并预订船票。\n", "2. **天气**：海岛天气多变，出行前关注天气预报，带好防晒和雨具。\n", "3. **美食**：一定要尝试舟山的海鲜，如带鱼、黄鱼、梭子蟹等。\n", "\n", "如果是第一次来，建议选择1-2个主要景点深度游玩，避免行程太赶。希望你在舟山度过一个愉快的周末！\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，0 表示乘客没有在泰坦尼克号事故中幸存，1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">性别，只有 “male”（男性）和 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'0'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# \u001b[0m\n", "\u001b[32m表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，\u001b[0m\n", "\u001b[32m需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mpassenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mage 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch \u001b[0m\n", "\u001b[32m字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked \u001b[0m\n", "\u001b[32m字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 \u001b[0m\n", "\u001b[32msurvived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 \u001b[0m\n", "\u001b[32mID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量\u001b[0m\n", "\u001b[32m，0 表示乘客没有在泰坦尼克号事故中幸存，1 \u001b[0m\n", "\u001b[32m表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 \u001b[0m\n", "\u001b[32m代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 \u001b[0m\n", "\u001b[32m代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存\u001b[0m\n", "\u001b[32m概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测\u001b[0m\n", "\u001b[32m生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” \u001b[0m\n", "\u001b[32m等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理\u001b[0m\n", "\u001b[32m性别，只有 “male”（男性）和 \u001b[0m\n", "\u001b[32m“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” \u001b[0m\n", "\u001b[32m的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，\u001b[0m\n", "\u001b[32m不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在\u001b[0m\n", "\u001b[32m灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直\u001b[0m\n", "\u001b[32m系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如\u001b[0m\n", "\u001b[32m，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`\u001b[0m\n", "\u001b[32m``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp \u001b[0m\n", "\u001b[32m类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩\u001b[0m\n", "\u001b[32m子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预\u001b[0m\n", "\u001b[32m测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare\u001b[0m\n", "\u001b[32m（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经\u001b[0m\n", "\u001b[32m济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置\u001b[0m\n", "\u001b[32m，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口\u001b[0m\n", "\u001b[32m，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并\u001b[0m\n", "\u001b[32m且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'0'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["================================================================================\n"]}], "source": ["auto_chat(client,user_input=\"请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？\",system_extend_info=system_info,tools=tools)"]}, {"cell_type": "markdown", "id": "b6bef960", "metadata": {}, "source": ["# 3.短期记忆(基于数量)"]}, {"cell_type": "markdown", "id": "3d0116d3", "metadata": {}, "source": ["```\n", "总览：创建名字为ShortMemoryBaseCount的类，当前类主要用于用户与大模型对话内容管理，通过对话内容管理，进而让大模型具有记忆\n", "```"]}, {"cell_type": "markdown", "id": "e207be09", "metadata": {}, "source": ["```\n", "count_threshold：阈值，大模型一共能维护最多几条消息\n", "counts：大模型当前已经维护了多少条消息\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "ae219447", "metadata": {}, "outputs": [], "source": ["class ShortMemoryBaseCount:\n", "    #初始化方法\n", "    def __init__(self, \n", "                 messages=None,\n", "                 count_threshold=0,\n", "                 counts=0,\n", "                 model=\"deepseek-chat\"):\n", "        print(\"创建 ShortMemoryBaseCount 类对应实例对象\")\n", "        if messages is None:\n", "            self.messages = []\n", "        else:\n", "            self.messages = messages\n", "        self.count_threshold = count_threshold\n", "        self.counts = counts\n", "        self.model = model\n", "\n", "    # 根据大模型维护的消息总数量 和 设定阈值进行比较\n", "    def _based_on_count(self):\n", "        # 判断：大模型维护的消息数量是否大于阈值\n", "        if self.counts > self.count_threshold:\n", "            # 计算差值\n", "            diff = self.counts - self.count_threshold\n", "            self.counts -= diff\n", "            #练习：自己创建一个列表，messages[1:(1 + 3)]，查看删除哪些内容\n", "            del self.messages[1:(1 + diff)]\n", "            #练习：思考在删除消息的时候，为什么要做这样的操作呢？\n", "            if(\"tool_call_id\" in self.messages[1]):\n", "                del self.messages[1]\n", "                # self.counts-=1\n", "\n", "    #追加消息的方法\n", "    def append_message(self, message:dict):\n", "        self.counts += 1\n", "        self.messages.append(message)\n", "        self._based_on_count()\n", "\n", "    #获取当前短期记忆都维护了哪些消息\n", "    def get_messages(self):\n", "        return self.messages"]}, {"cell_type": "code", "execution_count": null, "id": "4966aef8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["创建 ShortMemoryBaseCount 类对应实例对象\n", "[{'user': '我问你第一个问题，你是谁？'}]\n", "----------------------------------------------\n", "[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第二个问题，重新回答你是谁？'}]\n", "----------------------------------------------\n", "[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第二个问题，重新回答你是谁？'}, {'user': '我问你第三个问题，你是谁？'}]\n"]}], "source": ["short_memory = ShortMemoryBaseCount(count_threshold=3)\n", "short_memory.append_message(message={\"user\":\"我问你第一个问题，你是谁？\"})\n", "print(short_memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "short_memory.append_message(message={\"user\":\"我问你第二个问题，重新回答你是谁？\"})\n", "print(short_memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "short_memory.append_message(message={\"user\":\"我问你第三个问题，你是谁？\"})\n", "print(short_memory.get_messages())"]}, {"cell_type": "code", "execution_count": null, "id": "32631c24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第三个问题，你是谁？'}, {'user': '我问你第四个问题，重新回答你是谁？'}]\n"]}], "source": ["short_memory.append_message(message={\"user\":\"我问你第四个问题，重新回答你是谁？\"})\n", "print(short_memory.get_messages())"]}, {"cell_type": "code", "execution_count": null, "id": "586fbe50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第四个问题，重新回答你是谁？'}, {'user': '我问你第五个问题，重新回答你是谁？'}]\n"]}], "source": ["short_memory.append_message(message={\"user\":\"我问你第五个问题，重新回答你是谁？\"})\n", "print(short_memory.get_messages())"]}, {"cell_type": "code", "execution_count": null, "id": "c3c7f667", "metadata": {}, "outputs": [], "source": ["def auto_chat(client,\n", "              user_input,\n", "              system_base_info=\"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\",\n", "              system_extend_info=\"\",\n", "              tools = None,\n", "              model=\"deepseek-chat\"):\n", "    #系统消息\n", "    system_message = {\"role\":\"system\",\"content\":system_base_info + \"%s\" %system_extend_info}\n", "    #用户消息\n", "    user_message={\"role\":\"user\",\"content\":user_input}\n", "    # global memory\n", "    memory = ShortMemoryBaseCount(count_threshold=6) # counts = 0 \n", "    memory.append_message(system_message)\n", "    memory.append_message(user_message)   # counts = 2\n", "    #多轮对话\n", "    while True:         \n", "        #大模型调用          \n", "        result = auto_call(client,memory.get_messages(),tools=tools,model=model)\n", "        custom_print(f\"模型回复: {result}\")\n", "        memory.append_message({\"role\": \"assistant\", \"content\": result})\n", "        custom_print(\"追加完系统回复后的消息结构: \")\n", "        custom_print(memory.get_messages())\n", "        user_input = input(\"您还有其他问题？(输入0结束对话): \")\n", "        memory.append_message({\"role\":\"user\",\"content\":user_input})\n", "        custom_print(\"重新追加用户问题后的消息结构: \")\n", "        custom_print(memory.get_messages())\n", "        if user_input == \"0\":\n", "            break"]}, {"cell_type": "code", "execution_count": null, "id": "ef95e15d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["创建 ShortMemoryBaseCount 类对应实例对象\n", "没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: 你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！\n", "</pre>\n"], "text/plain": ["模型回复: 你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> AS total_records FROM user_base_info\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m AS total_records FROM user_base_info\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: `user_base_info` 表中目前共有 **<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">891</span> 条**数据记录。  \n", "\n", "如果需要对数据做进一步分析或查询，请随时告诉我！\n", "</pre>\n"], "text/plain": ["模型回复: `user_base_info` 表中目前共有 **\u001b[1;36m891\u001b[0m 条**数据记录。  \n", "\n", "如果需要对数据做进一步分析或查询，请随时告诉我！\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_dd361184-6f96-4905-9045-c43b0bb90062'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_dd361184-6f96-4905-9045-c43b0bb90062'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"total_records\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中目前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要对数据做进一步分析或查询，请随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_dd361184-6f96-4905-9045-c43b0bb90062'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_dd361184-6f96-4905-9045-c43b0bb90062'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"total_records\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中目前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要对数据做进一步分析或查询，请随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_dd361184-6f96-4905-9045-c43b0bb90062'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_dd361184-6f96-4905-9045-c43b0bb90062'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"total_records\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中目前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要对数据做进一步分析或查询，请随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我上一个问题问的是什么'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_dd361184-6f96-4905-9045-c43b0bb90062'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_dd361184-6f96-4905-9045-c43b0bb90062'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"total_records\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中目前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要对数据做进一步分析或查询，请随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'我上一个问题问的是什么'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: 你上一个问题是询问 `user_base_info` 表中存在多少条数据，我的回答是：该表中共有 **<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">891</span> 条**数据记录。  \n", "\n", "还有其他问题需要帮助吗？\n", "</pre>\n"], "text/plain": ["模型回复: 你上一个问题是询问 `user_base_info` 表中存在多少条数据，我的回答是：该表中共有 **\u001b[1;36m891\u001b[0m 条**数据记录。  \n", "\n", "还有其他问题需要帮助吗？\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_dd361184-6f96-4905-9045-c43b0bb90062'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_dd361184-6f96-4905-9045-c43b0bb90062'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"total_records\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中目前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要对数据做进一步分析或查询，请随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我上一个问题问的是什么'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你上一个问题是询问 `user_base_info` 表中存在多少条数据，我的回答是：该表中共有 **891 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">条**数据记录。  \\n\\n还有其他问题需要帮助吗？'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，我都可以协助您！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_dd361184-6f96-4905-9045-c43b0bb90062'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_dd361184-6f96-4905-9045-c43b0bb90062'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"total_records\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中目前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要对数据做进一步分析或查询，请随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'我上一个问题问的是什么'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你上一个问题是询问 `user_base_info` 表中存在多少条数据，我的回答是：该表中共有 **891 \u001b[0m\n", "\u001b[32m条**数据记录。  \\n\\n还有其他问题需要帮助吗？'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_dd361184-6f96-4905-9045-c43b0bb90062'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_dd361184-6f96-4905-9045-c43b0bb90062'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"total_records\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中目前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要对数据做进一步分析或查询，请随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我上一个问题问的是什么'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你上一个问题是询问 `user_base_info` 表中存在多少条数据，我的回答是：该表中共有 **891 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">条**数据记录。  \\n\\n还有其他问题需要帮助吗？'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'0'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_dd361184-6f96-4905-9045-c43b0bb90062'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_dd361184-6f96-4905-9045-c43b0bb90062'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"total_records\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中目前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要对数据做进一步分析或查询，请随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'我上一个问题问的是什么'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你上一个问题是询问 `user_base_info` 表中存在多少条数据，我的回答是：该表中共有 **891 \u001b[0m\n", "\u001b[32m条**数据记录。  \\n\\n还有其他问题需要帮助吗？'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'0'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在当前单元格或上一个单元格中执行代码时 Kernel 崩溃。\n", "\u001b[1;31m请查看单元格中的代码，以确定故障的可能原因。\n", "\u001b[1;31m单击<a href='https://aka.ms/vscodeJupyterKernelCrash'>此处</a>了解详细信息。\n", "\u001b[1;31m有关更多详细信息，请查看 Jupyter <a href='command:jupyter.viewOutput'>log</a>。"]}], "source": ["user_input = \"你好呀\"\n", "auto_chat(client,user_input=user_input,system_extend_info=\"\",tools=tools)"]}, {"cell_type": "code", "execution_count": null, "id": "7437044d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["创建 ShortMemoryBaseCount 类对应实例对象\n", "没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: 你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！\n", "</pre>\n"], "text/plain": ["模型回复: 你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> FROM user_base_info\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m FROM user_base_info\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: `user_base_info` 表中当前共有 **<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">891</span> 条**数据记录。  \n", "\n", "如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！\n", "</pre>\n"], "text/plain": ["模型回复: `user_base_info` 表中当前共有 **\u001b[1;36m891\u001b[0m 条**数据记录。  \n", "\n", "如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"COUNT(*)\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中当前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中当前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"COUNT(*)\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中当前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中当前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> + <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> 等于 **<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>**。  \n", "\n", "如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊\n", "</pre>\n"], "text/plain": ["模型回复: \u001b[1;36m1\u001b[0m + \u001b[1;36m1\u001b[0m 等于 **\u001b[1;36m2\u001b[0m**。  \n", "\n", "如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"COUNT(*)\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中当前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他相关问题，我都可以为您提供帮助！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中当前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user_base_info表中存在多少条数据'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"COUNT(*)\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中当前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+3等于几'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'user_base_info表中存在多少条数据'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中当前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+3等于几'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> + <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> 等于 **<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>**。  \n", "\n", "如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊\n", "</pre>\n"], "text/plain": ["模型回复: \u001b[1;36m1\u001b[0m + \u001b[1;36m3\u001b[0m 等于 **\u001b[1;36m4\u001b[0m**。  \n", "\n", "如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"COUNT(*)\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中当前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+3等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_45aca033-b42f-40cf-b7b8-991d3efeff28'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中当前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+3等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'`user_base_info` 表中当前共有 **891 条**数据记录。  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+3等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2+2等于几'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'`user_base_info` 表中当前共有 **891 条**数据记录。  \u001b[0m\n", "\u001b[32m\\n\\n如果需要进一步分析这些数据的分布、结构或其他统计指标，可以随时告诉我！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+3等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'2+2等于几'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> + <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> 等于 **<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>**。  \n", "\n", "如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊\n", "</pre>\n"], "text/plain": ["模型回复: \u001b[1;36m2\u001b[0m + \u001b[1;36m2\u001b[0m 等于 **\u001b[1;36m4\u001b[0m**。  \n", "\n", "如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+3等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2+2等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2 + 2 等于 **4**。  \\n\\n如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+3等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'2+2等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'2 + 2 等于 **4**。  \\n\\n如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+3等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2+2等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2 + 2 等于 **4**。  \\n\\n如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'3+3等于几'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 1 等于 **2**。  \\n\\n如果有任何数据分析或数学相关的问题，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+3等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'2+2等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'2 + 2 等于 **4**。  \\n\\n如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'3+3等于几'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> + <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> 等于 **<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span>**。  \n", "\n", "如果需要更复杂的计算或数据分析，随时告诉我！ 😊\n", "</pre>\n"], "text/plain": ["模型回复: \u001b[1;36m3\u001b[0m + \u001b[1;36m3\u001b[0m 等于 **\u001b[1;36m6\u001b[0m**。  \n", "\n", "如果需要更复杂的计算或数据分析，随时告诉我！ 😊\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+3等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2+2等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2 + 2 等于 **4**。  \\n\\n如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'3+3等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'3 + 3 等于 **6**。  \\n\\n如果需要更复杂的计算或数据分析，随时告诉我！ 😊'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'1+3等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'2+2等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'2 + 2 等于 **4**。  \\n\\n如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'3+3等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'3 + 3 等于 **6**。  \\n\\n如果需要更复杂的计算或数据分析，随时告诉我！ 😊'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2+2等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'2 + 2 等于 **4**。  \\n\\n如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'3+3等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'3 + 3 等于 **6**。  \\n\\n如果需要更复杂的计算或数据分析，随时告诉我！ 😊'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'0'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'1 + 3 等于 **4**。  \\n\\n如果需要解决更复杂的数学问题或数据分析任务，随时告诉我！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'2+2等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'2 + 2 等于 **4**。  \\n\\n如果还有其他数学问题或数据分析需求，欢迎随时提问！ 😊'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'3+3等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'3 + 3 等于 **6**。  \\n\\n如果需要更复杂的计算或数据分析，随时告诉我！ 😊'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'0'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["user_input = \"你好呀\"\n", "auto_chat(client,user_input=user_input,system_extend_info=\"\",tools=tools)"]}, {"cell_type": "markdown", "id": "ff8108e5", "metadata": {}, "source": ["# 4.测试"]}, {"cell_type": "markdown", "id": "a7e8da33", "metadata": {}, "source": ["## 4.1 类示例演示"]}, {"cell_type": "code", "execution_count": null, "id": "bcb84bdc", "metadata": {}, "outputs": [], "source": ["class UserTest:\n", "    def __init__(self,name):\n", "        print(\"一个对象被创建了\")\n", "        self.name = name\n", "\n", "    def get<PERSON><PERSON>(self):\n", "        return self.name "]}, {"cell_type": "code", "execution_count": null, "id": "6086be6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["一个对象被创建了\n"]}], "source": ["user1 = UserTest(\"zhaoyi\")"]}, {"cell_type": "code", "execution_count": null, "id": "d34cae88", "metadata": {}, "outputs": [{"data": {"text/plain": ["'zhaoyi'"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["user1.getName()"]}, {"cell_type": "code", "execution_count": null, "id": "3737ad21", "metadata": {}, "outputs": [{"data": {"text/plain": ["'zhaoyi'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["user1.name"]}, {"cell_type": "code", "execution_count": null, "id": "fdd828a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["一个对象被创建了\n"]}], "source": ["user2 = UserTest(\"qianer\")"]}, {"cell_type": "code", "execution_count": null, "id": "6cccd280", "metadata": {}, "outputs": [{"data": {"text/plain": ["'qianer'"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["user2.name"]}, {"cell_type": "markdown", "id": "93ec3485", "metadata": {}, "source": ["## 4.2 代码拆解"]}, {"cell_type": "markdown", "id": "7bb51b36", "metadata": {}, "source": ["```\n", "        if self.counts > self.count_threshold:\n", "            diff = self.counts - self.count_threshold\n", "            self.counts -= diff\n", "            del self.messages[1:(1 + diff)]\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "9ef26b3e", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["counts = 4\n", "count_threshold = 2\n", "diff  = counts - count_threshold\n", "diff"]}, {"cell_type": "code", "execution_count": null, "id": "b7b090da", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '系统消息1'},\n", " {'role': 'user', 'content': '用户消息1'},\n", " {'role': 'user', 'content': '用户消息2'},\n", " {'role': 'user', 'content': '用户消息3'}]"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["mess = [{\"role\":\"system\",\"content\":\"系统消息1\"},{\"role\":\"user\",\"content\":\"用户消息1\"},{\"role\":\"user\",\"content\":\"用户消息2\"},{\"role\":\"user\",\"content\":\"用户消息3\"}]\n", "mess"]}, {"cell_type": "code", "execution_count": null, "id": "2cc4539d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'user', 'content': '用户消息1'}, {'role': 'user', 'content': '用户消息2'}]"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["mess[1:(1+diff)]"]}, {"cell_type": "markdown", "id": "9bf5234e", "metadata": {}, "source": ["## 4.3 消息机制验证"]}, {"cell_type": "markdown", "id": "b90c2973", "metadata": {}, "source": ["验证消息删除的机制，以及大模型回复问题的机制"]}, {"cell_type": "code", "execution_count": null, "id": "48e310ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/plain": ["'你好！作为大数据领域的专家，我可以帮助你处理和分析各种数据问题。以下是一些我可以提供的帮助：\\n\\n1. **数据查询与分析**：通过SQL或其他查询语言从数据库中提取和分析数据。\\n2. **数据清洗与预处理**：处理缺失值、异常值、重复数据等。\\n3. **数据可视化**：生成图表或报告，帮助你更直观地理解数据。\\n4. **复杂计算与建模**：包括统计分析、机器学习模型构建等。\\n5. **大数据技术**：如Hadoop、Spark等框架的使用和优化。\\n6. **数据库设计与优化**：表结构设计、索引优化等。\\n\\n如果你有具体的数据需求或问题，可以告诉我，我会尽力为你提供解决方案！'"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"assistant\", \"content\": \"我是能够处理海量数据的专家，有什么可以帮助你的？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": null, "id": "b33f4bb3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">FLOOR</span><span style=\"font-weight: bold\">(</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">AVG</span><span style=\"font-weight: bold\">(</span>age<span style=\"font-weight: bold\">))</span> AS average_age FROM user_base_info;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mFLOOR\u001b[0m\u001b[1m(\u001b[0m\u001b[1;35mAVG\u001b[0m\u001b[1m(\u001b[0mage\u001b[1m)\u001b[0m\u001b[1m)\u001b[0m AS average_age FROM user_base_info;\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'根据计算结果，user_base_info数据表中所有人员的平均年龄是29岁（取整数部分）。'"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": null, "id": "a41e88a5", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT FLOOR(AVG(age)) AS average_age FROM </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">user_base_info;\",\"db\":\"user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"average_age\":29}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT FLOOR\u001b[0m\u001b[32m(\u001b[0m\u001b[32mAVG\u001b[0m\u001b[32m(\u001b[0m\u001b[32mage\u001b[0m\u001b[32m)\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS average_age FROM \u001b[0m\n", "\u001b[32muser_base_info;\",\"db\":\"user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"average_age\":29\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(messages)"]}, {"cell_type": "code", "execution_count": null, "id": "6d63a293", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT FLOOR(AVG(age)) AS average_age FROM </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">user_base_info;\",\"db\":\"user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"average_age\":29}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT FLOOR\u001b[0m\u001b[32m(\u001b[0m\u001b[32mAVG\u001b[0m\u001b[32m(\u001b[0m\u001b[32mage\u001b[0m\u001b[32m)\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS average_age FROM \u001b[0m\n", "\u001b[32muser_base_info;\",\"db\":\"user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"average_age\":29\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["del messages[1:2]\n", "custom_print(messages)"]}, {"cell_type": "code", "execution_count": null, "id": "350b8227", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/plain": ["'根据查询结果，用户基础信息表中用户的平均年龄约为29岁。'"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": null, "id": "744f55eb", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"average_age\":29}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_76ef3b2f-9299-4271-bc18-917e1d8e64eb'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"average_age\":29\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["del messages[1:2]\n", "custom_print(messages)"]}, {"cell_type": "code", "execution_count": 12, "id": "3bd4ee90", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'messages' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[12]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m auto_call(client,\u001b[43mmessages\u001b[49m,tools)\n", "\u001b[31mNameError\u001b[39m: name 'messages' is not defined"]}], "source": ["auto_call(client,messages,tools)"]}], "metadata": {"kernelspec": {"display_name": "zjou-2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}