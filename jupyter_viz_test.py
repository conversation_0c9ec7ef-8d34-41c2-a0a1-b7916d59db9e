#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Jupyter环境中的智能可视化测试
直接运行并与AI对话生成可视化图表
"""

import os
import sys
import time
import json
import platform
import subprocess
import webbrowser
from datetime import datetime

# 添加项目路径
sys.path.append(os.getcwd())

def setup_environment():
    """设置环境"""
    print("🔧 设置测试环境...")
    
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        import pandas as pd
        import numpy as np
        from openai import OpenAI
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 确保输出目录存在
        os.makedirs('powerbank_memory', exist_ok=True)
        
        print("✅ 环境设置完成")
        return True
        
    except ImportError as e:
        print(f"❌ 依赖包缺失: {e}")
        return False

class JupyterVisualizationTester:
    """Jupyter环境可视化测试器"""
    
    def __init__(self):
        """初始化"""
        self.ai_client = None
        self.test_data = self._prepare_test_data()
        self.conversation_history = []
        
        # 初始化AI客户端
        try:
            from openai import OpenAI
            self.ai_client = OpenAI(
                api_key="sk-e2bad2bca73343a38f4f8d60f7435192",
                base_url="https://api.deepseek.com/v1"
            )
            print("✅ AI客户端初始化成功")
        except Exception as e:
            print(f"⚠️ AI客户端初始化失败: {e}")
            self.ai_client = None
        
        print("🎯 Jupyter可视化测试器初始化完成")
    
    def _prepare_test_data(self):
        """准备测试数据"""
        return {
            "品牌数据": [
                {"品牌名称": "思腾智电", "订单数量": 4963, "总收入": 9926.00, "市场份额": 13.20},
                {"品牌名称": "怪兽充电", "订单数量": 5100, "总收入": 10200.00, "市场份额": 13.60},
                {"品牌名称": "搜电", "订单数量": 5076, "总收入": 15228.00, "市场份额": 20.30},
                {"品牌名称": "街电", "订单数量": 4800, "总收入": 14400.00, "市场份额": 19.20},
                {"品牌名称": "小电", "订单数量": 4200, "总收入": 12600.00, "市场份额": 16.80},
                {"品牌名称": "来电", "订单数量": 3800, "总收入": 11400.00, "市场份额": 15.20}
            ],
            "用户数据": [
                {"年龄段": "18-25岁", "用户数量": 8500, "平均消费": 25.6, "活跃度": 85},
                {"年龄段": "26-35岁", "用户数量": 12000, "平均消费": 32.4, "活跃度": 92},
                {"年龄段": "36-45岁", "用户数量": 6800, "平均消费": 28.9, "活跃度": 78},
                {"年龄段": "46-55岁", "用户数量": 3200, "平均消费": 22.1, "活跃度": 65},
                {"年龄段": "55岁以上", "用户数量": 1500, "平均消费": 18.7, "活跃度": 45}
            ],
            "地区数据": [
                {"地区": "北京", "总收入": 45600.00, "订单数": 2280, "用户数": 15600},
                {"地区": "上海", "总收入": 42300.00, "订单数": 2115, "用户数": 14200},
                {"地区": "深圳", "总收入": 38900.00, "订单数": 1945, "用户数": 12800},
                {"地区": "广州", "总收入": 35200.00, "订单数": 1760, "用户数": 11500},
                {"地区": "杭州", "总收入": 28700.00, "订单数": 1435, "用户数": 9200}
            ],
            "时间数据": [
                {"月份": "2024-01", "收入": 28500.00, "订单数": 1425, "增长率": 5.2},
                {"月份": "2024-02", "收入": 31200.00, "订单数": 1560, "增长率": 9.5},
                {"月份": "2024-03", "收入": 35800.00, "订单数": 1790, "增长率": 14.7},
                {"月份": "2024-04", "收入": 38900.00, "订单数": 1945, "增长率": 8.7},
                {"月份": "2024-05", "收入": 42100.00, "订单数": 2105, "增长率": 8.2},
                {"月份": "2024-06", "收入": 45300.00, "订单数": 2265, "增长率": 7.6}
            ]
        }
    
    def intelligent_chart_recommendation(self, user_question: str) -> dict:
        """AI智能图表推荐"""
        try:
            if not self.ai_client:
                return self._rule_based_recommendation(user_question)
            
            prompt = f"""
作为数据可视化专家，请分析用户问题并推荐最佳的可视化方案。

用户问题：{user_question}

可选图表类型：柱状图、饼图、折线图、热力图、散点图、堆叠柱状图
可用数据类型：品牌数据、用户数据、地区数据、时间数据

请返回JSON格式：
{{
    "chart_type": "推荐的图表类型",
    "data_type": "需要的数据类型", 
    "reason": "推荐理由",
    "title": "图表标题"
}}
"""
            
            response = self.ai_client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是数据可视化专家，只返回JSON格式的推荐结果。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=200
            )
            
            result = json.loads(response.choices[0].message.content.strip())
            return result
            
        except Exception as e:
            print(f"⚠️ AI推荐失败，使用规则推荐: {e}")
            return self._rule_based_recommendation(user_question)
    
    def _rule_based_recommendation(self, question: str) -> dict:
        """基于规则的推荐"""
        question_lower = question.lower()
        
        if "份额" in question or "占比" in question or "比例" in question:
            return {
                "chart_type": "饼图",
                "data_type": "品牌数据",
                "reason": "饼图适合展示占比关系",
                "title": "市场份额分布"
            }
        elif "趋势" in question or "变化" in question or "时间" in question:
            return {
                "chart_type": "折线图", 
                "data_type": "时间数据",
                "reason": "折线图适合展示趋势变化",
                "title": "趋势分析"
            }
        elif "对比" in question or "比较" in question or "排行" in question:
            return {
                "chart_type": "柱状图",
                "data_type": "品牌数据", 
                "reason": "柱状图适合数据对比",
                "title": "数据对比分析"
            }
        elif "用户" in question or "年龄" in question:
            return {
                "chart_type": "柱状图",
                "data_type": "用户数据",
                "reason": "柱状图适合用户分析", 
                "title": "用户分析"
            }
        elif "地区" in question or "城市" in question:
            return {
                "chart_type": "柱状图",
                "data_type": "地区数据",
                "reason": "柱状图适合地区对比",
                "title": "地区分析"
            }
        else:
            return {
                "chart_type": "柱状图",
                "data_type": "品牌数据",
                "reason": "默认推荐柱状图",
                "title": "数据分析"
            }
    
    def create_visualization(self, data, chart_type, title):
        """创建可视化图表"""
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            import pandas as pd
            import numpy as np
            
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            
            if chart_type == "柱状图":
                self._create_bar_chart(df, ax, title)
            elif chart_type == "饼图":
                self._create_pie_chart(df, ax, title)
            elif chart_type == "折线图":
                self._create_line_chart(df, ax, title)
            else:
                self._create_bar_chart(df, ax, title)  # 默认柱状图
            
            # 保存图表
            timestamp = int(time.time())
            chart_path = f"powerbank_memory/jupyter_chart_{chart_type}_{timestamp}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            return {
                "success": True,
                "chart_type": chart_type,
                "path": chart_path,
                "message": f"{chart_type}生成成功"
            }
            
        except Exception as e:
            return {
                "success": False,
                "chart_type": chart_type,
                "path": None,
                "message": f"图表生成失败: {str(e)}"
            }
    
    def _create_bar_chart(self, df, ax, title):
        """创建柱状图"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            x_col = categorical_cols[0]
            y_col = numeric_cols[0]
            
            bars = ax.bar(df[x_col], df[y_col], 
                         color=plt.cm.Set3(np.linspace(0, 1, len(df))))
            
            ax.set_xlabel(x_col)
            ax.set_ylabel(y_col)
            ax.set_title(title, fontsize=14, fontweight='bold')
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height:.0f}', ha='center', va='bottom')
            
            plt.xticks(rotation=45)
    
    def _create_pie_chart(self, df, ax, title):
        """创建饼图"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            labels = df[categorical_cols[0]]
            sizes = df[numeric_cols[-1]]  # 使用最后一个数值列
            
            colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, autopct='%1.1f%%',
                                             colors=colors, startangle=90)
            
            ax.set_title(title, fontsize=14, fontweight='bold')
    
    def _create_line_chart(self, df, ax, title):
        """创建折线图"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) >= 1:
            y_col = numeric_cols[0]
            ax.plot(df.index, df[y_col], marker='o', linewidth=2, markersize=6)
            
            ax.set_xlabel('序号')
            ax.set_ylabel(y_col)
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.grid(True, alpha=0.3)
    
    def open_chart_file(self, file_path):
        """一键打开图表文件"""
        try:
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return False
            
            abs_path = os.path.abspath(file_path)
            system = platform.system()
            
            if system == "Windows":
                os.startfile(abs_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", abs_path])
            else:  # Linux
                subprocess.run(["xdg-open", abs_path])
            
            print(f"📂 已打开图表文件: {file_path}")
            return True
            
        except Exception as e:
            print(f"❌ 打开文件失败: {e}")
            return False
    
    def chat_with_ai(self, user_question: str) -> dict:
        """与AI对话生成可视化"""
        print(f"\n🤖 用户问题: {user_question}")
        print("🔍 正在分析问题...")
        
        # 1. AI智能推荐
        recommendation = self.intelligent_chart_recommendation(user_question)
        print(f"💡 AI推荐: {recommendation['chart_type']} - {recommendation['reason']}")
        
        # 2. 获取数据
        data_type = recommendation["data_type"]
        data = self.test_data.get(data_type, [])
        
        if not data:
            return {"success": False, "message": f"未找到{data_type}"}
        
        # 3. 生成可视化
        print("🎨 正在生成可视化...")
        viz_result = self.create_visualization(data, recommendation["chart_type"], recommendation["title"])
        
        if viz_result.get("success"):
            chart_path = viz_result.get("path")
            print(f"✅ 可视化生成成功: {recommendation['chart_type']}")
            print(f"📁 文件保存: {chart_path}")
            
            # 4. 一键打开文件
            if chart_path:
                self.open_chart_file(chart_path)
            
            return {
                "success": True,
                "chart_type": recommendation["chart_type"],
                "chart_path": chart_path,
                "recommendation": recommendation,
                "message": f"成功生成{recommendation['chart_type']}并已自动打开"
            }
        else:
            print(f"❌ 可视化生成失败: {viz_result.get('message')}")
            return {"success": False, "message": viz_result.get('message')}

def main():
    """主函数"""
    print("🚀 Jupyter智能可视化测试系统")
    print("=" * 60)
    
    # 设置环境
    if not setup_environment():
        print("❌ 环境设置失败")
        return
    
    # 创建测试器
    tester = JupyterVisualizationTester()
    
    # 预设测试问题
    test_questions = [
        "各品牌的市场份额如何？",
        "用户年龄分布情况怎么样？", 
        "哪个地区的收入最高？",
        "最近几个月的收入趋势如何？",
        "品牌收入对比分析"
    ]
    
    print("\n📋 开始自动测试...")
    results = []
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{'='*20} 测试 {i}/5 {'='*20}")
        result = tester.chat_with_ai(question)
        results.append(result)
        time.sleep(1)  # 间隔1秒
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r.get("success"))
    print(f"✅ 成功: {success_count}/{len(results)} 个测试")
    
    for i, result in enumerate(results, 1):
        status = "✅" if result.get("success") else "❌"
        chart_type = result.get("chart_type", "未知")
        print(f"  {status} 测试{i}: {chart_type}")
    
    print(f"\n🎉 智能可视化测试完成！")
    print(f"📁 所有图表文件保存在: powerbank_memory/")
    print(f"💡 图表文件已自动打开，你可以查看可视化效果")
    
    return tester

if __name__ == "__main__":
    tester = main()
