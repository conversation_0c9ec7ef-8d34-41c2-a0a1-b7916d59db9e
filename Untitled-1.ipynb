{"cells": [{"cell_type": "code", "execution_count": null, "id": "40c36374", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 📚 模块2: 长期记忆与专家文档系统  \n", "# 作用: 持久化存储知识，提供专业领域知识支持\n", "# ===================================================================\n", "\n", "class PowerBankExpertKnowledge:\n", "    \"\"\"\n", "    共享充电宝专家知识库 - 基于9表结构优化\n", "    \n", "    功能说明:\n", "    1. 存储完整的9表结构知识和最佳实践\n", "    2. 提供精确的数据字典和业务规则\n", "    3. 支持智能查询模板和分析框架\n", "    4. 动态知识更新和业务场景识别\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        self.domain_knowledge = self._load_domain_knowledge()\n", "        self.data_dictionary = self._load_data_dictionary()\n", "        self.analysis_templates = self._load_analysis_templates()\n", "        self.business_rules = self._load_business_rules()\n", "        self.table_relationships = self._load_table_relationships()\n", "        custom_print(\"📚 专家知识库已加载 - 支持9表完整结构\")\n", "    \n", "    def _load_domain_knowledge(self) -> str:\n", "        \"\"\"加载共享充电宝行业专业知识 - 增强版\"\"\"\n", "        return \"\"\"\n", "# 共享充电宝行业专家知识库 - 完整版\n", "\n", "## 核心业务指标体系\n", "1. **订单数量**: 反映用户使用频次和市场活跃度，来源于order_table\n", "2. **总收入**: 直接的商业价值指标，通过单价*使用时长计算\n", "3. **平均单价**: 定价策略和用户接受度的体现，品牌差异化关键\n", "4. **市场份额**: 品牌竞争力的核心指标，基于收入占比计算\n", "5. **用户行为**: 使用习惯和消费模式分析，关联用户维度表\n", "\n", "## 数据仓库架构说明\n", "### 维度表 (Dimension Tables)\n", "- **user_table**: 用户基础信息维度，支持用户画像分析\n", "- **region_table**: 地理位置维度，支持地域分析和热力图\n", "- **time_table**: 时间维度，支持趋势分析和周期性分析\n", "\n", "### 事实表 (Fact Table)\n", "- **order_table**: 核心业务事实表，记录每次充电行为\n", "\n", "### 汇总表 (Summary Tables)\n", "- **brand_revenue_summary**: 品牌维度汇总，支持竞争分析\n", "- **user_behavior_summary**: 用户行为汇总，支持用户价值分析\n", "- **region_usage_summary**: 地区使用汇总，支持区域运营\n", "- **region_heatmap_data**: 热力图专用数据，支持可视化\n", "- **time_summary**: 时间维度汇总，支持趋势分析\n", "\n", "## 商业模式分析\n", "- **按时计费**: 主流的收费模式，灵活性高，单价字段记录\n", "- **会员制度**: 提高用户粘性，可通过用户行为表分析\n", "- **地域差异**: 不同城市定价策略，通过地区表分析\n", "- **品牌竞争**: 市场份额争夺，通过品牌汇总表分析\n", "\n", "## 关键分析维度\n", "1. **用户维度**: 性别、年龄、职业、消费能力\n", "2. **品牌维度**: 市场份额、收入、订单量、定价\n", "3. **地理维度**: 省份、城市、区域热度分布\n", "4. **时间维度**: 年度趋势、季节性、周期性\n", "5. **综合维度**: 多维交叉分析，深度业务洞察\n", "\n", "## 竞争格局\n", "主要品牌: 怪兽充电、街电、小电、来电、搜电等\n", "竞争要素: 点位覆盖、设备质量、价格策略、用户体验、技术创新\n", "\"\"\"\n", "\n", "    def _load_data_dictionary(self) -> Dict:\n", "        \"\"\"加载完整9表数据字典\"\"\"\n", "        return {\n", "            # 基础维度表\n", "            \"user_table\": {\n", "                \"description\": \"用户基础信息表 - 用户维度的核心表\",\n", "                \"table_type\": \"维度表\",\n", "                \"fields\": {\n", "                    \"用户ID\": \"用户唯一标识，INT类型，主键\",\n", "                    \"姓名\": \"用户姓名，VARCHAR(100)，支持中文\",\n", "                    \"年龄\": \"用户年龄，INT类型，用于年龄段分析\",\n", "                    \"性别\": \"用户性别，VARCHAR(10)，值为'男'或'女'\",\n", "                    \"地址\": \"用户地址，VARCHAR(200)，详细地址信息\",\n", "                    \"职业\": \"用户职业，VARCHAR(100)，职业分类分析\"\n", "                },\n", "                \"business_meaning\": \"用户画像分析、用户分层、精准营销的基础数据\",\n", "                \"key_analyses\": [\"性别分布\", \"年龄结构\", \"职业分析\", \"用户画像\"]\n", "            },\n", "            \"region_table\": {\n", "                \"description\": \"地区维度表 - 地理位置信息的标准维度\",\n", "                \"table_type\": \"维度表\",\n", "                \"fields\": {\n", "                    \"地区ID\": \"地区唯一标识，INT类型，主键\",\n", "                    \"省份\": \"省份名称，VARCHAR(50)，标准省份名\",\n", "                    \"省份简称\": \"省份简称，VARCHAR(20)，如'京'、'沪'\",\n", "                    \"城市\": \"城市名称，VARCHAR(50)，地级市名称\",\n", "                    \"区县\": \"区县名称，VARCHAR(50)，县级行政区\",\n", "                    \"经度\": \"地理经度，DECIMAL(10,6)，GPS坐标\",\n", "                    \"纬度\": \"地理纬度，DECIMAL(10,6)，GPS坐标\"\n", "                },\n", "                \"business_meaning\": \"地域分析、热点区域识别、区域运营策略制定\",\n", "                \"key_analyses\": [\"省份分布\", \"城市排名\", \"区域热力图\", \"地理聚类\"]\n", "            },\n", "            \"time_table\": {\n", "                \"description\": \"时间维度表 - 标准时间维度信息\",\n", "                \"table_type\": \"维度表\",\n", "                \"fields\": {\n", "                    \"时间ID\": \"时间唯一标识，INT类型，主键\",\n", "                    \"日期数字\": \"数字格式日期，INT类型，如20240101\",\n", "                    \"标准日期\": \"标准日期格式，DATE类型，YYYY-MM-DD\",\n", "                    \"中文日期\": \"中文日期描述，VARCHAR(50)，如'2024年1月1日'\",\n", "                    \"年月\": \"年月格式，VARCHAR(20)，如'2024-01'\",\n", "                    \"年月中文\": \"中文年月，VARCHAR(30)，如'2024年1月'\",\n", "                    \"年份\": \"年份，INT类型，如2024\",\n", "                    \"年份中文\": \"中文年份，VARCHAR(20)，如'2024年'\",\n", "                    \"星期\": \"中文星期，VARCHAR(20)，如'星期一'\",\n", "                    \"英文星期\": \"英文星期，VARCHAR(20)，如'Monday'\",\n", "                    \"周数\": \"年内周数，INT类型\",\n", "                    \"年周\": \"年周格式，VARCHAR(30)，如'2024年第1周'\",\n", "                    \"星期数\": \"星期数字，INT类型，1-7\",\n", "                    \"年内天数\": \"年内天数，INT类型，1-366\"\n", "                },\n", "                \"business_meaning\": \"时间序列分析、趋势预测、周期性分析\",\n", "                \"key_analyses\": [\"年度趋势\", \"月度分析\", \"周期性\", \"季节性\"]\n", "            },\n", "            \n", "            # 核心事实表\n", "            \"order_table\": {\n", "                \"description\": \"订单事实表 - 核心业务数据记录表\",\n", "                \"table_type\": \"事实表\",\n", "                \"fields\": {\n", "                    \"订单ID\": \"订单唯一标识，VARCHAR(20)，主键\",\n", "                    \"用户ID\": \"关联用户表，INT类型，外键\",\n", "                    \"地区ID\": \"关联地区表，INT类型，外键\",\n", "                    \"时间ID\": \"关联时间表，INT类型，外键\",\n", "                    \"开始时间\": \"充电开始时间，VARCHAR(10)，格式HH:MM\",\n", "                    \"结束时间\": \"充电结束时间，VARCHAR(10)，格式HH:MM\",\n", "                    \"单价\": \"充电单价，INT类型，单位：分\",\n", "                    \"品牌\": \"充电宝品牌，VARCHAR(50)，品牌标识\"\n", "                },\n", "                \"business_meaning\": \"记录每次充电行为，是所有分析的数据源\",\n", "                \"key_analyses\": [\"订单统计\", \"品牌分析\", \"时长分析\", \"收入计算\"]\n", "            },\n", "            \n", "            # 汇总分析表\n", "            \"brand_revenue_summary\": {\n", "                \"description\": \"品牌收入汇总表 - 品牌维度的核心指标汇总\",\n", "                \"table_type\": \"汇总表\",\n", "                \"fields\": {\n", "                    \"品牌名称\": \"充电宝品牌名称，VARCHAR(50)，主键\",\n", "                    \"订单数量\": \"该品牌总订单数，INT类型，反映使用频次\",\n", "                    \"总收入\": \"该品牌总收入，DECIMAL(15,2)，单位：元\",\n", "                    \"平均单价\": \"平均单价，DECIMAL(10,2)，单位：元\",\n", "                    \"市场份额\": \"市场份额百分比，DECIMAL(5,2)，单位：%\"\n", "                },\n", "                \"business_meaning\": \"品牌竞争分析、市场地位评估、定价策略制定\",\n", "                \"key_analyses\": [\"市场份额\", \"收入排名\", \"定价分析\", \"竞争态势\"]\n", "            },\n", "            \"user_behavior_summary\": {\n", "                \"description\": \"用户行为汇总表 - 用户消费行为的统计汇总\",\n", "                \"table_type\": \"汇总表\",\n", "                \"fields\": {\n", "                    \"用户ID\": \"用户唯一标识，INT类型，主键\",\n", "                    \"使用次数\": \"用户累计使用次数，INT类型，活跃度指标\",\n", "                    \"总消费\": \"用户累计消费金额，DECIMAL(15,2)，价值指标\"\n", "                },\n", "                \"business_meaning\": \"用户价值分析、用户分层、精准营销策略\",\n", "                \"key_analyses\": [\"用户分层\", \"消费分析\", \"活跃度\", \"用户价值\"]\n", "            },\n", "            \"time_summary\": {\n", "                \"description\": \"时间维度汇总表 - 按时间维度的业务统计\",\n", "                \"table_type\": \"汇总表\",\n", "                \"fields\": {\n", "                    \"汇总类型\": \"汇总类型标识，VARCHAR(20)，如'年度'、'月度'\",\n", "                    \"年份\": \"统计年份，INT类型，时间标识\",\n", "                    \"记录数\": \"该时间段记录数量，INT类型，业务量指标\"\n", "                },\n", "                \"business_meaning\": \"时间趋势分析、业务增长监控、周期性分析\",\n", "                \"key_analyses\": [\"年度趋势\", \"增长率\", \"周期分析\", \"预测建模\"]\n", "            },\n", "            \"region_usage_summary\": {\n", "                \"description\": \"地区使用汇总表 - 地区维度的业务统计\",\n", "                \"table_type\": \"汇总表\",\n", "                \"fields\": {\n", "                    \"地区ID\": \"地区唯一标识，INT类型，主键\",\n", "                    \"总收入\": \"该地区总收入，DECIMAL(15,2)，地区价值指标\"\n", "                },\n", "                \"business_meaning\": \"地区业务表现分析、区域运营策略、市场拓展\",\n", "                \"key_analyses\": [\"地区排名\", \"区域价值\", \"市场潜力\", \"运营效率\"]\n", "            },\n", "            \"region_heatmap_data\": {\n", "                \"description\": \"地区热力图数据表 - 专用于可视化的地区数据\",\n", "                \"table_type\": \"应用表\",\n", "                \"fields\": {\n", "                    \"省份\": \"省份名称，VARCHAR(50)，地理标识\",\n", "                    \"城市\": \"城市名称，VARCHAR(50)，地理标识\",\n", "                    \"总收入\": \"该城市总收入，DECIMAL(15,2)，热力值\"\n", "                },\n", "                \"business_meaning\": \"热力图可视化、地理分析、区域对比\",\n", "                \"key_analyses\": [\"热力图\", \"地理分布\", \"城市排名\", \"区域对比\"]\n", "            }\n", "        }\n", "    \n", "    def _load_analysis_templates(self) -> Dict:\n", "        \"\"\"加载完整分析模板 - 基于9表结构\"\"\"\n", "        return {\n", "            \"用户分析\": {\n", "                \"性别分布统计\": \"SELECT 性别, COUNT(*) as 用户数量, ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_table), 2) as 占比 FROM user_table WHERE 性别 IS NOT NULL GROUP BY 性别 ORDER BY 用户数量 DESC\",\n", "                \"年龄段分析\": \"SELECT CASE WHEN 年龄 < 25 THEN '青年(18-24)' WHEN 年龄 < 35 THEN '青年(25-34)' WHEN 年龄 < 45 THEN '中年(35-44)' WHEN 年龄 < 55 THEN '中年(45-54)' ELSE '中老年(55+)' END as 年龄段, COUNT(*) as 人数, AV<PERSON>(年龄) as 平均年龄 FROM user_table WHERE 年龄 IS NOT NULL GROUP BY 年龄段 ORDER BY 平均年龄\",\n", "                \"职业分布排名\": \"SELECT 职业, COUNT(*) as 人数, ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_table WHERE 职业 IS NOT NULL), 2) as 占比 FROM user_table WHERE 职业 IS NOT NULL GROUP BY 职业 ORDER BY 人数 DESC LIMIT 15\",\n", "                \"高价值用户识别\": \"SELECT u.用户ID, u.姓名, u.性别, u.年龄, u.职业, ub.使用次数, ub.总消费, ROUND(ub.总消费/ub.使用次数, 2) as 平均消费 FROM user_table u JOIN user_behavior_summary ub ON u.用户ID = ub.用户ID ORDER BY ub.总消费 DESC LIMIT 20\",\n", "                \"用户消费行为分析\": \"SELECT u.性别, u.职业, COUNT(*) as 用户数, AVG(ub.使用次数) as 平均使用次数, AVG(ub.总消费) as 平均消费, MAX(ub.总消费) as 最高消费 FROM user_table u JOIN user_behavior_summary ub ON u.用户ID = ub.用户ID GROUP BY u.性别, u.职业 ORDER BY 平均消费 DESC\"\n", "            },\n", "            \"品牌分析\": {\n", "                \"品牌市场份额排名\": \"SELECT 品牌名称, 订单数量, 总收入, 平均单价, 市场份额, RANK() OVER (ORDER BY 市场份额 DESC) as 市场排名 FROM brand_revenue_summary ORDER BY 市场份额 DESC\",\n", "                \"品牌收入对比\": \"SELECT 品牌名称, 总收入, 订单数量, ROUND(总收入/订单数量, 2) as 单均收入, 市场份额 FROM brand_revenue_summary ORDER BY 总收入 DESC\",\n", "                \"品牌订单量分析\": \"SELECT 品牌名称, 订单数量, 总收入, 平均单价, ROUND(订单数量 * 100.0 / (SELECT SUM(订单数量) FROM brand_revenue_summary), 2) as 订单占比 FROM brand_revenue_summary ORDER BY 订单数量 DESC\",\n", "                \"品牌定价策略分析\": \"SELECT 品牌名称, 平均单价, 订单数量, 总收入, 市场份额, CASE WHEN 平均单价 > (SELECT AVG(平均单价) FROM brand_revenue_summary) THEN '高价策略' ELSE '低价策略' END as 定价策略 FROM brand_revenue_summary ORDER BY 平均单价 DESC\",\n", "                \"品牌综合竞争力\": \"SELECT 品牌名称, 市场份额, 总收入, 订单数量, 平均单价, ROUND((市场份额 * 0.4 + (订单数量/(SELECT MAX(订单数量) FROM brand_revenue_summary)) * 100 * 0.3 + (总收入/(SELECT MAX(总收入) FROM brand_revenue_summary)) * 100 * 0.3), 2) as 综合得分 FROM brand_revenue_summary ORDER BY 综合得分 DESC\"\n", "            },\n", "            \"地区分析\": {\n", "                \"省份收入排名\": \"SELECT 省份, SUM(总收入) as 省份总收入, COUNT(DISTINCT 城市) as 城市数量, AVG(总收入) as 平均城市收入, MAX(总收入) as 最高城市收入 FROM region_heatmap_data GROUP BY 省份 ORDER BY 省份总收入 DESC LIMIT 20\",\n", "                \"城市收入TOP排名\": \"SELECT 省份, 城市, 总收入, RANK() OVER (ORDER BY 总收入 DESC) as 全国排名, RANK() OVER (PARTITION BY 省份 ORDER BY 总收入 DESC) as 省内排名 FROM region_heatmap_data ORDER BY 总收入 DESC LIMIT 30\",\n", "                \"地区用户分布\": \"SELECT r.省份, r.城市, COUNT(DISTINCT o.用户ID) as 用户数, COUNT(o.订单ID) as 订单数, SUM(o.单价) as 总收入 FROM order_table o JOIN region_table r ON o.地区ID = r.地区ID GROUP BY r.省份, r.城市 ORDER BY 用户数 DESC LIMIT 25\",\n", "                \"区域业务密度\": \"SELECT r.省份, COUNT(DISTINCT r.城市) as 覆盖城市数, COUNT(DISTINCT o.用户ID) as 用户数, COUNT(o.订单ID) as 订单数, ROUND(COUNT(o.订单ID) * 1.0 / COUNT(DISTINCT r.城市), 2) as 城市平均订单数 FROM region_table r LEFT JOIN order_table o ON r.地区ID = o.地区ID GROUP BY r.省份 ORDER BY 城市平均订单数 DESC\",\n", "                \"热点区域识别\": \"SELECT 省份, 城市, 总收入, CASE WHEN 总收入 >= (SELECT AVG(总收入) + 2*STDDEV(总收入) FROM region_heatmap_data) THEN '超热点' WHEN 总收入 >= (SELECT AVG(总收入) + STDDEV(总收入) FROM region_heatmap_data) THEN '热点' WHEN 总收入 >= (SELECT AVG(总收入) FROM region_heatmap_data) THEN '一般' ELSE '冷点' END as 热度等级 FROM region_heatmap_data ORDER BY 总收入 DESC\"\n", "            },\n", "            \"时间分析\": {\n", "                \"年度业务趋势\": \"SELECT 年份, 记录数, LAG(记录数) OVER (ORDER BY 年份) as 上年记录数, ROUND((记录数 - LAG(记录数) OVER (ORDER BY 年份)) * 100.0 / LAG(记录数) OVER (ORDER BY 年份), 2) as 同比增长率 FROM time_summary WHERE 汇总类型 = '年度' ORDER BY 年份\",\n", "                \"月度订单分布\": \"SELECT t.年月, t.年份, COUNT(o.订单ID) as 订单数, SUM(o.单价) as 月收入, COUNT(DISTINCT o.用户ID) as 活跃用户数, ROUND(SUM(o.单价) * 1.0 / COUNT(o.订单ID), 2) as 月均单价 FROM order_table o JOIN time_table t ON o.时间ID = t.时间ID GROUP BY t.年月, t.年份 ORDER BY t.年月\",\n", "                \"星期使用模式\": \"SELECT t.星期, t.星期数, COUNT(o.订单ID) as 订单数, SUM(o.单价) as 收入, AVG(o.单价) as 平均单价 FROM order_table o JOIN time_table t ON o.时间ID = t.时间ID GROUP BY t.星期, t.星期数 ORDER BY t.星期数\",\n", "                \"时间段热度分析\": \"SELECT CASE WHEN CAST(SUBSTRING(o.开始时间, 1, 2) AS UNSIGNED) BETWEEN 6 AND 11 THEN '上午(06-11)' WHEN CAST(SUBSTRING(o.开始时间, 1, 2) AS UNSIGNED) BETWEEN 12 AND 17 THEN '下午(12-17)' WHEN CAST(SUBSTRING(o.开始时间, 1, 2) AS UNSIGNED) BETWEEN 18 AND 23 THEN '晚上(18-23)' ELSE '深夜(00-05)' END as 时间段, COUNT(*) as 订单数, SUM(单价) as 收入, AVG(单价) as 平均单价 FROM order_table o GROUP BY 时间段 ORDER BY 订单数 DESC\"\n", "            },\n", "            \"综合分析\": {\n", "                \"品牌地区分布\": \"SELECT o.品牌, r.省份, COUNT(*) as 订单数, SUM(o.单价) as 收入, COUNT(DISTINCT o.用户ID) as 用户数, ROUND(AVG(o.单价), 2) as 平均单价 FROM order_table o JOIN region_table r ON o.地区ID = r.地区ID GROUP BY o.品牌, r.省份 ORDER BY 收入 DESC LIMIT 50\",\n", "                \"用户价值细分\": \"SELECT u.性别, u.职业, CASE WHEN ub.总消费 >= 200 THEN '高价值' WHEN ub.总消费 >= 100 THEN '中价值' WHEN ub.总消费 >= 50 THEN '普通价值' ELSE '低价值' END as 价值等级, COUNT(*) as 用户数, AVG(ub.总消费) as 平均消费, AVG(ub.使用次数) as 平均使用次数 FROM user_table u JOIN user_behavior_summary ub ON u.用户ID = ub.用户ID GROUP BY u.性别, u.职业, 价值等级 ORDER BY 平均消费 DESC\",\n", "                \"品牌用户画像\": \"SELECT o.品牌, u.性别, u.职业, COUNT(DISTINCT u.用户ID) as 用户数, COUNT(o.订单ID) as 订单数, SUM(o.单价) as 收入, ROUND(AVG(u.年龄), 1) as 平均年龄 FROM order_table o JOIN user_table u ON o.用户ID = u.用户ID GROUP BY o.品牌, u.性别, u.职业 ORDER BY 用户数 DESC LIMIT 100\",\n", "                \"地区品牌竞争\": \"SELECT r.省份, r.城市, o.品牌, COUNT(*) as 订单数, SUM(o.单价) as 收入, RANK() OVER (PARTITION BY r.省份, r.城市 ORDER BY COUNT(*) DESC) as 城市内排名 FROM order_table o JOIN region_table r ON o.地区ID = r.地区ID GROUP BY r.省份, r.城市, o.品牌ORDER BY r.省份, r.城市, 城市内排名\",\n", "                \"全维度业务概览\": \"SELECT '总体概况' as 维度, COUNT(DISTINCT u.用户ID) as 用户数, COUNT(DISTINCT o.品牌) as 品牌数, COUNT(DISTINCT r.省份) as 省份数, COUNT(DISTINCT r.城市) as 城市数, COUNT(o.订单ID) as 总订单数, SUM(o.单价) as 总收入, ROUND(AVG(o.单价), 2) as 平均单价 FROM order_table o JOIN user_table u ON o.用户ID = u.用户ID JOIN region_table r ON o.地区ID = r.地区ID\"\n", "            }\n", "        }\n", "    \n", "    def _load_business_rules(self) -> Dict:\n", "        \"\"\"加载业务规则和判断逻辑 - 增强版\"\"\"\n", "        return {\n", "            \"销量判断\": {\n", "                \"最好\": \"订单数量最高的品牌\",\n", "                \"最差\": \"订单数量最低的品牌\",\n", "                \"优秀\": \"订单数量 > 平均值 + 标准差\",\n", "                \"一般\": \"平均值 - 标准差 <= 订单数量 <= 平均值 + 标准差\",\n", "                \"较差\": \"订单数量 < 平均值 - 标准差\"\n", "            },\n", "            \"收入判断\": {\n", "                \"最赚钱\": \"总收入最高的品牌/地区\",\n", "                \"最不赚钱\": \"总收入最低的品牌/地区\",\n", "                \"高收入\": \"总收入 > 平均收入 * 1.5\",\n", "                \"中等收入\": \"平均收入 * 0.8 <= 总收入 <= 平均收入 * 1.5\",\n", "                \"低收入\": \"总收入 < 平均收入 * 0.8\"\n", "            },\n", "            \"用户价值分层\": {\n", "                \"超高价值\": \"总消费 >= 500元\",\n", "                \"高价值\": \"200元 <= 总消费 < 500元\",\n", "                \"中价值\": \"100元 <= 总消费 < 200元\",\n", "                \"普通价值\": \"50元 <= 总消费 < 100元\",\n", "                \"低价值\": \"总消费 < 50元\"\n", "            },\n", "            \"地区热度分级\": {\n", "                \"超热点\": \"收入 >= 平均值 + 2*标准差\",\n", "                \"热点\": \"平均值 + 标准差 <= 收入 < 平均值 + 2*标准差\",\n", "                \"一般\": \"平均值 <= 收入 < 平均值 + 标准差\",\n", "                \"冷点\": \"收入 < 平均值\"\n", "            },\n", "            \"品牌定价策略\": {\n", "                \"高价策略\": \"平均单价 > 全市场平均单价 * 1.2\",\n", "                \"中价策略\": \"全市场平均单价 * 0.8 <= 平均单价 <= 全市场平均单价 * 1.2\",\n", "                \"低价策略\": \"平均单价 < 全市场平均单价 * 0.8\"\n", "            },\n", "            \"时间趋势判断\": {\n", "                \"快速增长\": \"同比增长率 >= 50%\",\n", "                \"稳定增长\": \"10% <= 同比增长率 < 50%\",\n", "                \"缓慢增长\": \"0% <= 同比增长率 < 10%\",\n", "                \"下降\": \"同比增长率 < 0%\"\n", "            }\n", "        }\n", "    \n", "    def _load_table_relationships(self) -> Dict:\n", "        \"\"\"加载表关系和连接规则\"\"\"\n", "        return {\n", "            \"主要关联\": {\n", "                \"order_table\": {\n", "                    \"user_table\": \"o.用户ID = u.用户ID\",\n", "                    \"region_table\": \"o.地区ID = r.地区ID\", \n", "                    \"time_table\": \"o.时间ID = t.时间ID\"\n", "                },\n", "                \"user_behavior_summary\": {\n", "                    \"user_table\": \"ub.用户ID = u.用户ID\"\n", "                },\n", "                \"region_usage_summary\": {\n", "                    \"region_table\": \"rus.地区ID = r.地区ID\"\n", "                }\n", "            },\n", "            \"常用JOIN模式\": {\n", "                \"用户订单分析\": \"FROM order_table o JOIN user_table u ON o.用户ID = u.用户ID\",\n", "                \"地区订单分析\": \"FROM order_table o JOIN region_table r ON o.地区ID = r.地区ID\",\n", "                \"时间订单分析\": \"FROM order_table o JOIN time_table t ON o.时间ID = t.时间ID\",\n", "                \"用户行为分析\": \"FROM user_table u JOIN user_behavior_summary ub ON u.用户ID = ub.用户ID\",\n", "                \"全维度分析\": \"FROM order_table o JOIN user_table u ON o.用户ID = u.用户ID JOIN region_table r ON o.地区ID = r.地区ID JOIN time_table t ON o.时间ID = t.时间ID\"\n", "            }\n", "        }\n", "\n", "class PowerBankLongTermMemory:\n", "    \"\"\"\n", "    共享充电宝长期记忆管理器 - 增强版\n", "    \n", "    功能说明:\n", "    1. 持久化存储问答历史和分析结果\n", "    2. 支持智能知识检索和相似问题匹配\n", "    3. 提供学习能力和知识积累\n", "    4. 错误记录和优化建议存储\n", "    5. 业务场景分类存储和检索\n", "    \"\"\"\n", "    \n", "    def __init__(self, base_path: str = r'C:\\Users\\<USER>\\Desktop\\powerbank_memory'):\n", "        self.base_path = base_path\n", "        self.expert_knowledge = PowerBankExpertKnowledge()\n", "        self._create_folder(base_path)\n", "        \n", "        # 创建不同类型的记忆文件\n", "        self.qa_file = os.path.join(base_path, \"qa_memory.jsonl\")\n", "        self.analysis_file = os.path.join(base_path, \"analysis_memory.jsonl\") \n", "        self.error_file = os.path.join(base_path, \"error_memory.jsonl\")\n", "        self.expert_file = os.path.join(base_path, \"expert_knowledge.md\")\n", "        self.scenario_file = os.path.join(base_path, \"scenario_memory.jsonl\")  # 新增场景记忆\n", "        \n", "        self._create_expert_document()\n", "        custom_print(f\"📁 长期记忆系统初始化完成: {base_path}\")\n", "        custom_print(f\"📊 支持业务场景: {len(self.expert_knowledge.analysis_templates)}种\")\n", "    \n", "    def _create_folder(self, path: str):\n", "        \"\"\"创建文件夹\"\"\"\n", "        if not os.path.exists(path):\n", "            os.makedirs(path)\n", "    \n", "    def _create_expert_document(self):\n", "        \"\"\"创建专家知识文档 - 增强版\"\"\"\n", "        with open(self.expert_file, 'w', encoding='utf-8') as f:\n", "            f.write(self.expert_knowledge.domain_knowledge)\n", "            \n", "            f.write(\"\\n\\n## 完整数据字典\\n\")\n", "            for table, info in self.expert_knowledge.data_dictionary.items():\n", "                f.write(f\"\\n### {table} ({info['table_type']})\\n\")\n", "                f.write(f\"{info['description']}\\n\")\n", "                f.write(f\"**业务意义**: {info['business_meaning']}\\n\")\n", "                f.write(\"**字段说明**:\\n\")\n", "                for field, desc in info['fields'].items():\n", "                    f.write(f\"- {field}: {desc}\\n\")\n", "                if 'key_analyses' in info:\n", "                    f.write(f\"**关键分析**: {', '.join(info['key_analyses'])}\\n\")\n", "            \n", "            f.write(\"\\n\\n## 表关系说明\\n\")\n", "            for rel_type, relationships in self.expert_knowledge.table_relationships.items():\n", "                f.write(f\"\\n### {rel_type}\\n\")\n", "                for table, joins in relationships.items():\n", "                    f.write(f\"**{table}**:\\n\")\n", "                    if isinstance(joins, dict):\n", "                        for target, condition in joins.items():\n", "                            f.write(f\"- 关联{target}: {condition}\\n\")\n", "                    else:\n", "                        f.write(f\"- {joins}\\n\")\n", "            \n", "            f.write(\"\\n\\n## 业务规则\\n\")\n", "            for rule_type, rules in self.expert_knowledge.business_rules.items():\n", "                f.write(f\"\\n### {rule_type}\\n\")\n", "                for rule, desc in rules.items():\n", "                    f.write(f\"- {rule}: {desc}\\n\")\n", "    \n", "    def save_qa_pair(self, question: str, answer: str, sql_query: str = None, \n", "                     metadata: Dict = None, scenario: str = None):\n", "        \"\"\"\n", "        保存问答对到长期记忆 - 增强版\n", "        \n", "        Args:\n", "            question: 用户问题\n", "            answer: 系统回答\n", "            sql_query: 生成的SQL查询\n", "            metadata: 额外的元数据\n", "            scenario: 业务场景类型\n", "        \"\"\"\n", "        qa_data = {\n", "            \"timestamp\": datetime.now().isoformat(),\n", "            \"question\": question,\n", "            \"answer\": answer,\n", "            \"sql_query\": sql_query,\n", "            \"scenario\": scenario or \"未分类\",\n", "            \"metadata\": metadata or {},\n", "            \"domain\": \"共享充电宝\",\n", "            \"tables_used\": self._extract_tables_from_sql(sql_query) if sql_query else [],\n", "            \"question_type\": self._classify_question_type(question)\n", "        }\n", "        \n", "        with open(self.qa_file, 'a', encoding='utf-8') as f:\n", "            f.write(json.dumps(qa_data, ensure_ascii=False) + \"\\n\")\n", "        \n", "        # 同时保存到场景记忆\n", "        if scenario:\n", "            self._save_scenario_memory(scenario, qa_data)\n", "        \n", "        custom_print(f\"💾 问答对已保存到长期记忆 - 场景: {scenario}\")\n", "    \n", "    def _extract_tables_from_sql(self, sql: str) -> List[str]:\n", "        \"\"\"从SQL中提取使用的表名\"\"\"\n", "        if not sql:\n", "            return []\n", "        \n", "        tables = []\n", "        sql_lower = sql.lower()\n", "        table_names = [\n", "            \"user_table\", \"region_table\", \"time_table\", \"order_table\",\n", "            \"brand_revenue_summary\", \"user_behavior_summary\", \n", "            \"time_summary\", \"region_usage_summary\", \"region_heatmap_data\"\n", "        ]\n", "        \n", "        for table in table_names:\n", "            if table in sql_lower:\n", "                tables.append(table)\n", "        \n", "        return tables\n", "    \n", "    def _classify_question_type(self, question: str) -> str:\n", "        \"\"\"分类问题类型\"\"\"\n", "        question_lower = question.lower()\n", "        \n", "        if any(word in question_lower for word in [\"统计\", \"数量\", \"多少\", \"count\"]):\n", "            return \"统计查询\"\n", "        elif any(word in question_lower for word in [\"排名\", \"排序\", \"最\", \"top\", \"排行\"]):\n", "            return \"排名查询\"\n", "        elif any(word in question_lower for word in [\"对比\", \"比较\", \"差异\", \"vs\"]):\n", "            return \"对比分析\"\n", "        elif any(word in question_lower for word in [\"趋势\", \"变化\", \"增长\", \"下降\"]):\n", "            return \"趋势分析\"\n", "        elif any(word in question_lower for word in [\"分布\", \"占比\", \"比例\", \"结构\"]):\n", "            return \"分布分析\"\n", "        else:\n", "            return \"综合查询\"\n", "    \n", "    def _save_scenario_memory(self, scenario: str, qa_data: Dict):\n", "        \"\"\"保存场景记忆\"\"\"\n", "        scenario_data = {\n", "            \"scenario\": scenario,\n", "            \"timestamp\": qa_data[\"timestamp\"],\n", "            \"question\": qa_data[\"question\"],\n", "            \"question_type\": qa_data[\"question_type\"],\n", "            \"tables_used\": qa_data[\"tables_used\"],\n", "            \"success\": bool(qa_data.get(\"sql_query\"))\n", "        }\n", "        \n", "        with open(self.scenario_file, 'a', encoding='utf-8') as f:\n", "            f.write(json.dumps(scenario_data, ensure_ascii=False) + \"\\n\")\n", "    \n", "    def save_analysis_result(self, analysis_type: str, result_data: Any, \n", "                           insights: str = None, tables_used: List[str] = None):\n", "        \"\"\"保存分析结果 - 增强版\"\"\"\n", "        analysis_data = {\n", "            \"timestamp\": datetime.now().isoformat(),\n", "            \"analysis_type\": analysis_type,\n", "            \"result_data\": result_data,\n", "            \"insights\": insights,\n", "            \"tables_used\": tables_used or [],\n", "            \"domain\": \"共享充电宝\",\n", "            \"data_size\": len(result_data) if isinstance(result_data, list) else 1\n", "        }\n", "        \n", "        with open(self.analysis_file, 'a', encoding='utf-8') as f:\n", "            f.write(json.dumps(analysis_data, ensure_ascii=False) + \"\\n\")\n", "    \n", "    def save_error_case(self, error_type: str, error_msg: str, context: Dict = None,\n", "                       sql_query: str = None):\n", "        \"\"\"保存错误案例 - 增强版\"\"\"\n", "        error_data = {\n", "            \"timestamp\": datetime.now().isoformat(),\n", "            \"error_type\": error_type,\n", "            \"error_message\": error_msg,\n", "            \"sql_query\": sql_query,\n", "            \"context\": context or {},\n", "            \"domain\": \"共享充电宝\",\n", "            \"tables_involved\": self._extract_tables_from_sql(sql_query) if sql_query else []\n", "        }\n", "        \n", "        with open(self.error_file, 'a', encoding='utf-8') as f:\n", "            f.write(json.dumps(error_data, ensure_ascii=False) + \"\\n\")\n", "    \n", "    def search_similar_questions(self, question: str, limit: int = 5, \n", "                               scenario: str = None) -> List[Dict]:\n", "        \"\"\"搜索相似的历史问题 - 增强版\"\"\"\n", "        if not os.path.exists(self.qa_file):\n", "            return []\n", "        \n", "        keywords = question.lower().split()\n", "        similar_qa = []\n", "        \n", "        try:\n", "            with open(self.qa_file, 'r', encoding='utf-8') as f:\n", "                for line in f:\n", "                    if line.strip():\n", "                        qa_data = json.loads(line)\n", "                        qa_question = qa_data['question'].lower()\n", "                        \n", "                        # 计算关键词匹配度\n", "                        match_count = sum(1 for keyword in keywords if keyword in qa_question)\n", "                        if match_count > 0:\n", "                            similarity = match_count / len(keywords)\n", "                            \n", "                            # 场景匹配加权\n", "                            if scenario and qa_data.get('scenario') == scenario:\n", "                                similarity += 0.2\n", "                            \n", "                            # 问题类型匹配加权\n", "                            current_type = self._classify_question_type(question)\n", "                            if qa_data.get('question_type') == current_type:\n", "                                similarity += 0.1\n", "                            \n", "                            qa_data['similarity'] = min(similarity, 1.0)\n", "                            similar_qa.append(qa_data)\n", "            \n", "            # 按相似度排序\n", "            similar_qa.sort(key=lambda x: x['similarity'], reverse=True)\n", "            return similar_qa[:limit]\n", "        except Exception as e:\n", "            custom_print(f\"⚠️ 搜索相似问题时出错: {e}\")\n", "            return []\n", "    \n", "    def get_scenario_statistics(self) -> Dict:\n", "        \"\"\"获取场景统计信息\"\"\"\n", "        if not os.path.exists(self.scenario_file):\n", "            return {}\n", "        \n", "        scenario_stats = {}\n", "        try:\n", "            with open(self.scenario_file, 'r', encoding='utf-8') as f:\n", "                for line in f:\n", "                    if line.strip():\n", "                        data = json.loads(line)\n", "                        scenario = data['scenario']\n", "                        if scenario not in scenario_stats:\n", "                            scenario_stats[scenario] = {\n", "                                \"total_questions\": 0,\n", "                                \"success_rate\": 0,\n", "                                \"question_types\": {},\n", "                                \"common_tables\": {}\n", "                            }\n", "                        \n", "                        scenario_stats[scenario][\"total_questions\"] += 1\n", "                        if data['success']:\n", "                            scenario_stats[scenario][\"success_rate\"] += 1\n", "                        \n", "                        # 统计问题类型\n", "                        q_type = data['question_type']\n", "                        scenario_stats[scenario][\"question_types\"][q_type] = \\\n", "                            scenario_stats[scenario][\"question_types\"].get(q_type, 0) + 1\n", "                        \n", "                        # 统计常用表\n", "                        for table in data['tables_used']:\n", "                            scenario_stats[scenario][\"common_tables\"][table] = \\\n", "                                scenario_stats[scenario][\"common_tables\"].get(table, 0) + 1\n", "            \n", "            # 计算成功率\n", "            for scenario in scenario_stats:\n", "                total = scenario_stats[scenario][\"total_questions\"]\n", "                if total > 0:\n", "                    scenario_stats[scenario][\"success_rate\"] = \\\n", "                        round(scenario_stats[scenario][\"success_rate\"] / total * 100, 2)\n", "        \n", "        except Exception as e:\n", "            custom_print(f\"⚠️ 获取场景统计时出错: {e}\")\n", "        \n", "        return scenario_stats\n", "    \n", "    def get_expert_knowledge(self, topic: str = None) -> str:\n", "        \"\"\"获取专家知识 - 增强版\"\"\"\n", "        if topic and topic in self.expert_knowledge.analysis_templates:\n", "            return json.dumps(self.expert_knowledge.analysis_templates[topic], \n", "                            ensure_ascii=False, indent=2)\n", "        elif topic and topic in self.expert_knowledge.data_dictionary:\n", "            return json.dumps(self.expert_knowledge.data_dictionary[topic], \n", "                            ensure_ascii=False, indent=2)\n", "        return self.expert_knowledge.domain_knowledge\n", "    \n", "    def get_memory_summary(self) -> Dict:\n", "        \"\"\"获取记忆摘要统计 - 增强版\"\"\"\n", "        stats = {\n", "            \"qa_count\": 0,\n", "            \"analysis_count\": 0,\n", "            \"error_count\": 0,\n", "            \"scenario_count\": 0,\n", "            \"scenario_stats\": {}\n", "        }\n", "        \n", "        # 统计问答对数量\n", "        if os.path.exists(self.qa_file):\n", "            with open(self.qa_file, 'r', encoding='utf-8') as f:\n", "                stats[\"qa_count\"] = sum(1 for line in f if line.strip())\n", "        \n", "        # 统计分析结果数量\n", "        if os.path.exists(self.analysis_file):\n", "            with open(self.analysis_file, 'r', encoding='utf-8') as f:\n", "                stats[\"analysis_count\"] = sum(1 for line in f if line.strip())\n", "        \n", "        # 统计错误案例数量\n", "        if os.path.exists(self.error_file):\n", "            with open(self.error_file, 'r', encoding='utf-8') as f:\n", "                stats[\"error_count\"] = sum(1 for line in f if line.strip())\n", "        \n", "        # 统计场景记忆数量\n", "        if os.path.exists(self.scenario_file):\n", "            with open(self.scenario_file, 'r', encoding='utf-8') as f:\n", "                stats[\"scenario_count\"] = sum(1 for line in f if line.strip())\n", "        \n", "        # 获取场景统计\n", "        stats[\"scenario_stats\"] = self.get_scenario_statistics()\n", "        \n", "        return stats\n", "\n", "custom_print(\"✅ 模块2: 长期记忆与专家文档系统 - 加载完成\")\n", "custom_print(\"📊 完整支持9表结构，包含维度表、事实表、汇总表\")\n", "custom_print(\"🎯 智能场景分类，支持5大业务分析场景\")\n", "custom_print(\"🧠 增强记忆功能，支持场景统计和智能检索\")"]}, {"cell_type": "code", "execution_count": null, "id": "a02a4298", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 🔍 模块3: 智能信息查询系统\n", "# 作用: 自然语言转SQL，执行查询，生成业务洞察\n", "# ===================================================================\n", "\n", "class PowerBankIntelligentQueryEngine:\n", "    \"\"\"\n", "    共享充电宝智能查询引擎 - 基于9表结构优化\n", "    \n", "    功能说明:\n", "    1. 智能自然语言理解和精确SQL生成\n", "    2. 多表关联查询和复杂分析支持\n", "    3. 数据库连接池和查询优化\n", "    4. 结果格式化和智能可视化建议\n", "    5. 深度业务洞察自动生成\n", "    6. 查询性能监控和错误恢复\n", "    \"\"\"\n", "    \n", "    def __init__(self, db_config: Dict, api_config: Dict):\n", "        self.db_config = db_config\n", "        self.api_config = api_config\n", "        self.client = self._init_openai_client()\n", "        self.connection = None\n", "        self.table_schemas = {}\n", "        self.query_cache = {}  # 查询缓存\n", "        self.performance_stats = {}  # 性能统计\n", "        \n", "        # 业务表分类\n", "        self.dimension_tables = [\"user_table\", \"region_table\", \"time_table\"]\n", "        self.fact_tables = [\"order_table\"]\n", "        self.summary_tables = [\"brand_revenue_summary\", \"user_behavior_summary\", \n", "                              \"time_summary\", \"region_usage_summary\", \"region_heatmap_data\"]\n", "        \n", "        # 初始化数据库连接\n", "        self._init_database()\n", "        self._load_table_schemas()\n", "        self._init_business_templates()\n", "        \n", "        custom_print(\"🔍 智能查询引擎已启动 - 支持9表完整架构\")\n", "    \n", "    def _init_openai_client(self) -> OpenAI:\n", "        \"\"\"初始化OpenAI客户端 - 增强版\"\"\"\n", "        http_client = httpx.Client(follow_redirects=True, timeout=30.0)\n", "        return OpenAI(\n", "            api_key=self.api_config['api_key'],\n", "            base_url=self.api_config['base_url'],\n", "            http_client=http_client\n", "        )\n", "    \n", "    def _init_database(self):\n", "        \"\"\"初始化数据库连接 - 增强版\"\"\"\n", "        try:\n", "            # 添加连接池配置\n", "            db_config = self.db_config.copy()\n", "            db_config.update({\n", "                'charset': 'utf8mb4',\n", "                'autocommit': True,\n", "                'connect_timeout': 10,\n", "                'read_timeout': 30,\n", "                'write_timeout': 30\n", "            })\n", "            \n", "            self.connection = pymysql.connect(**db_config)\n", "            custom_print(\"✅ 数据库连接成功 - 已优化连接参数\")\n", "            \n", "            # 测试连接\n", "            cursor = self.connection.cursor()\n", "            cursor.execute(\"SELECT 1\")\n", "            cursor.close()\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"❌ 数据库连接失败: {e}\")\n", "            raise\n", "    \n", "    def _load_table_schemas(self):\n", "        \"\"\"加载所有表的结构信息 - 增强版\"\"\"\n", "        cursor = self.connection.cursor()\n", "        \n", "        try:\n", "            # 获取所有表名\n", "            cursor.execute(\"SHOW TABLES\")\n", "            tables = cursor.fetchall()\n", "            \n", "            for table in tables:\n", "                table_name = table[0]\n", "                \n", "                # 获取表结构\n", "                cursor.execute(f\"DESCRIBE {table_name}\")\n", "                columns = cursor.fetchall()\n", "                \n", "                # 获取表统计信息\n", "                cursor.execute(f\"SELECT COUNT(*) FROM {table_name}\")\n", "                row_count = cursor.fetchone()[0]\n", "                \n", "                # 获取示例数据（更智能的采样）\n", "                if row_count > 1000:\n", "                    # 大表使用随机采样\n", "                    cursor.execute(f\"SELECT * FROM {table_name} ORDER BY RAND() LIMIT 3\")\n", "                else:\n", "                    cursor.execute(f\"SELECT * FROM {table_name} LIMIT 3\")\n", "                \n", "                sample_data = cursor.fetchall()\n", "                column_names = [desc[0] for desc in cursor.description]\n", "                \n", "                # 分析字段类型和特征\n", "                field_analysis = self._analyze_table_fields(table_name, columns, sample_data, column_names)\n", "                \n", "                self.table_schemas[table_name] = {\n", "                    'columns': columns,\n", "                    'sample_data': sample_data,\n", "                    'column_names': column_names,\n", "                    'row_count': row_count,\n", "                    'field_analysis': field_analysis,\n", "                    'table_type': self._classify_table_type(table_name),\n", "                    'business_purpose': self._get_table_business_purpose(table_name)\n", "                }\n", "            \n", "            cursor.close()\n", "            custom_print(f\"📊 已加载 {len(self.table_schemas)} 个表的完整结构信息\")\n", "            custom_print(f\"📈 维度表: {len(self.dimension_tables)}个, 事实表: {len(self.fact_tables)}个, 汇总表: {len(self.summary_tables)}个\")\n", "            \n", "        except Exception as e:\n", "            cursor.close()\n", "            custom_print(f\"❌ 加载表结构失败: {e}\")\n", "            raise\n", "    \n", "    def _analyze_table_fields(self, table_name: str, columns: tuple, \n", "                             sample_data: tuple, column_names: list) -> Dict:\n", "        \"\"\"分析表字段特征\"\"\"\n", "        analysis = {\n", "            'key_fields': [],\n", "            'numeric_fields': [],\n", "            'text_fields': [],\n", "            'date_fields': [],\n", "            'foreign_keys': []\n", "        }\n", "        \n", "        for i, col in enumerate(columns):\n", "            field_name, field_type = col[0], col[1]\n", "            \n", "            # 识别关键字段\n", "            if 'id' in field_name.lower() or field_name.endswith('ID'):\n", "                analysis['key_fields'].append(field_name)\n", "                if field_name != f\"{table_name.replace('_table', '').replace('_summary', '')}ID\":\n", "                    analysis['foreign_keys'].append(field_name)\n", "            \n", "            # 识别数值字段\n", "            if any(t in field_type.lower() for t in ['int', 'decimal', 'float', 'double']):\n", "                analysis['numeric_fields'].append(field_name)\n", "            \n", "            # 识别文本字段\n", "            if any(t in field_type.lower() for t in ['varchar', 'text', 'char']):\n", "                analysis['text_fields'].append(field_name)\n", "            \n", "            # 识别日期字段\n", "            if any(t in field_type.lower() for t in ['date', 'time', 'timestamp']):\n", "                analysis['date_fields'].append(field_name)\n", "        \n", "        return analysis\n", "    \n", "    def _classify_table_type(self, table_name: str) -> str:\n", "        \"\"\"分类表类型\"\"\"\n", "        if table_name in self.dimension_tables:\n", "            return \"维度表\"\n", "        elif table_name in self.fact_tables:\n", "            return \"事实表\"\n", "        elif table_name in self.summary_tables:\n", "            return \"汇总表\"\n", "        else:\n", "            return \"其他表\"\n", "    \n", "    def _get_table_business_purpose(self, table_name: str) -> str:\n", "        \"\"\"获取表的业务用途\"\"\"\n", "        purposes = {\n", "            \"user_table\": \"用户基础信息，支持用户画像和行为分析\",\n", "            \"region_table\": \"地理位置信息，支持区域分析和热力图\",\n", "            \"time_table\": \"时间维度信息，支持趋势和周期性分析\",\n", "            \"order_table\": \"核心业务事实，记录每次充电行为\",\n", "            \"brand_revenue_summary\": \"品牌维度汇总，支持竞争分析\",\n", "            \"user_behavior_summary\": \"用户行为汇总，支持用户价值分析\",\n", "            \"time_summary\": \"时间维度汇总，支持趋势分析\",\n", "            \"region_usage_summary\": \"地区使用汇总，支持区域运营\",\n", "            \"region_heatmap_data\": \"热力图数据，支持地理可视化\"\n", "        }\n", "        return purposes.get(table_name, \"业务支持表\")\n", "    \n", "    def _init_business_templates(self):\n", "        \"\"\"初始化业务查询模板\"\"\"\n", "        self.query_templates = {\n", "            \"统计查询\": {\n", "                \"pattern\": [\"统计\", \"数量\", \"多少\", \"count\", \"总数\"],\n", "                \"template\": \"SELECT {group_field}, COUNT(*) as 数量 FROM {table} GROUP BY {group_field} ORDER BY 数量 DESC\"\n", "            },\n", "            \"排名查询\": {\n", "                \"pattern\": [\"排名\", \"排序\", \"最\", \"top\", \"排行\", \"第一\", \"最高\", \"最低\"],\n", "                \"template\": \"SELECT {fields} FROM {table} ORDER BY {order_field} DESC LIMIT {limit}\"\n", "            },\n", "            \"对比分析\": {\n", "                \"pattern\": [\"对比\", \"比较\", \"差异\", \"vs\", \"和\", \"与\"],\n", "                \"template\": \"SELECT {group_field}, {metric_field} FROM {table} GROUP BY {group_field} ORDER BY {metric_field} DESC\"\n", "            },\n", "            \"趋势分析\": {\n", "                \"pattern\": [\"趋势\", \"变化\", \"增长\", \"下降\", \"时间\", \"年度\", \"月度\"],\n", "                \"template\": \"SELECT {time_field}, {metric_field} FROM {table} ORDER BY {time_field}\"\n", "            },\n", "            \"分布分析\": {\n", "                \"pattern\": [\"分布\", \"占比\", \"比例\", \"结构\", \"构成\"],\n", "                \"template\": \"SELECT {category_field}, COUNT(*) as 数量, ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM {table}), 2) as 占比 FROM {table} GROUP BY {category_field}\"\n", "            }\n", "        }\n", "    \n", "    def get_database_info(self) -> str:\n", "        \"\"\"获取数据库结构信息 - 增强版\"\"\"\n", "        info = \"# 共享充电宝数据仓库 - 完整ADS层架构\\n\\n\"\n", "        \n", "        # 按表类型分组显示\n", "        table_groups = {\n", "            \"维度表 (Dimension Tables)\": self.dimension_tables,\n", "            \"事实表 (Fact Tables)\": self.fact_tables,\n", "            \"汇总表 (Summary Tables)\": self.summary_tables\n", "        }\n", "        \n", "        for group_name, table_list in table_groups.items():\n", "            info += f\"## {group_name}\\n\\n\"\n", "            \n", "            for table_name in table_list:\n", "                if table_name in self.table_schemas:\n", "                    schema = self.table_schemas[table_name]\n", "                    info += f\"### {table_name} ({schema['row_count']:,}条记录)\\n\"\n", "                    info += f\"**业务用途**: {schema['business_purpose']}\\n\\n\"\n", "                    \n", "                    info += \"**字段信息**:\\n\"\n", "                    for col in schema['columns']:\n", "                        field_name, field_type = col[0], col[1]\n", "                        # 添加字段说明\n", "                        field_desc = self._get_field_description(table_name, field_name)\n", "                        info += f\"- {field_name} ({field_type}) - {field_desc}\\n\"\n", "                    \n", "                    # 显示关键字段分析\n", "                    analysis = schema['field_analysis']\n", "                    if analysis['key_fields']:\n", "                        info += f\"\\n**关键字段**: {', '.join(analysis['key_fields'])}\\n\"\n", "                    if analysis['foreign_keys']:\n", "                        info += f\"**外键字段**: {', '.join(analysis['foreign_keys'])}\\n\"\n", "                    \n", "                    # 显示示例数据\n", "                    info += \"\\n**示例数据**:\\n\"\n", "                    if schema['sample_data']:\n", "                        try:\n", "                            df = pd.DataFrame(schema['sample_data'], columns=schema['column_names'])\n", "                            info += df.to_string(index=False, max_cols=6)[:300] + \"...\\n\"\n", "                        except:\n", "                            info += \"数据格式复杂，请直接查询\\n\"\n", "                    \n", "                    info += \"\\n---\\n\\n\"\n", "        \n", "        # 添加表关联关系说明\n", "        info += \"## 表关联关系\\n\\n\"\n", "        info += \"**核心关联**:\\n\"\n", "        info += \"- order_table ← user_table (用户ID)\\n\"\n", "        info += \"- order_table ← region_table (地区ID)\\n\"\n", "        info += \"- order_table ← time_table (时间ID)\\n\"\n", "        info += \"- user_behavior_summary ← user_table (用户ID)\\n\"\n", "        info += \"- region_usage_summary ← region_table (地区ID)\\n\\n\"\n", "        \n", "        return info\n", "    \n", "    def _get_field_description(self, table_name: str, field_name: str) -> str:\n", "        \"\"\"获取字段描述\"\"\"\n", "        descriptions = {\n", "            \"user_table\": {\n", "                \"用户ID\": \"用户唯一标识，主键\",\n", "                \"姓名\": \"用户真实姓名\",\n", "                \"年龄\": \"用户年龄，用于年龄段分析\",\n", "                \"性别\": \"用户性别，男/女\",\n", "                \"地址\": \"用户详细地址\",\n", "                \"职业\": \"用户职业信息\"\n", "            },\n", "            \"region_table\": {\n", "                \"地区ID\": \"地区唯一标识，主键\",\n", "                \"省份\": \"省级行政区划\",\n", "                \"省份简称\": \"省份简称代码\",\n", "                \"城市\": \"地级市名称\",\n", "                \"区县\": \"县级行政区\",\n", "                \"经度\": \"GPS经度坐标\",\n", "                \"纬度\": \"GPS纬度坐标\"\n", "            },\n", "            \"time_table\": {\n", "                \"时间ID\": \"时间唯一标识，主键\",\n", "                \"日期数字\": \"数字格式日期\",\n", "                \"标准日期\": \"标准日期格式\",\n", "                \"中文日期\": \"中文日期描述\",\n", "                \"年月\": \"年月格式\",\n", "                \"年份\": \"年份数字\",\n", "                \"星期\": \"中文星期\",\n", "                \"周数\": \"年内周数\"\n", "            },\n", "            \"order_table\": {\n", "                \"订单ID\": \"订单唯一标识，主键\",\n", "                \"用户ID\": \"关联用户表外键\",\n", "                \"地区ID\": \"关联地区表外键\",\n", "                \"时间ID\": \"关联时间表外键\",\n", "                \"开始时间\": \"充电开始时间\",\n", "                \"结束时间\": \"充电结束时间\",\n", "                \"单价\": \"充电单价(分)\",\n", "                \"品牌\": \"充电宝品牌\"\n", "            },\n", "            \"brand_revenue_summary\": {\n", "                \"品牌名称\": \"充电宝品牌名称\",\n", "                \"订单数量\": \"品牌总订单数\",\n", "                \"总收入\": \"品牌总收入金额\",\n", "                \"平均单价\": \"品牌平均单价\",\n", "                \"市场份额\": \"品牌市场份额百分比\"\n", "            },\n", "            \"user_behavior_summary\": {\n", "                \"用户ID\": \"用户唯一标识\",\n", "                \"使用次数\": \"用户累计使用次数\",\n", "                \"总消费\": \"用户累计消费金额\"\n", "            }\n", "        }\n", "        \n", "        table_desc = descriptions.get(table_name, {})\n", "        return table_desc.get(field_name, \"业务字段\")\n", "    \n", "    def execute_sql_query(self, sql: str) -> Union[List[Dict], str]:\n", "        \"\"\"执行SQL查询并返回结果 - 增强版\"\"\"\n", "        import time\n", "        start_time = time.time()\n", "        \n", "        # 检查查询缓存\n", "        cache_key = hash(sql)\n", "        if cache_key in self.query_cache:\n", "            custom_print(\"🚀 使用查询缓存\")\n", "            return self.query_cache[cache_key]\n", "        \n", "        try:\n", "            # 检查连接状态\n", "            if not self.connection or not self.connection.open:\n", "                custom_print(\"🔄 重新建立数据库连接\")\n", "                self._init_database()\n", "            \n", "            cursor = self.connection.cursor()\n", "            custom_print(f\"🔍 执行SQL查询...\")\n", "            custom_print(f\"📝 SQL: {sql[:100]}{'...' if len(sql) > 100 else ''}\")\n", "            \n", "            cursor.execute(sql)\n", "            results = cursor.fetchall()\n", "            \n", "            if not results:\n", "                custom_print(\"⚠️ 查询无结果\")\n", "                return []\n", "            \n", "            column_names = [desc[0] for desc in cursor.description]\n", "            cursor.close()\n", "            \n", "            # 转换为字典列表\n", "            data = [dict(zip(column_names, row)) for row in results]\n", "            \n", "            # 执行时间统计\n", "            execution_time = time.time() - start_time\n", "            custom_print(f\"✅ 查询成功 - 返回 {len(data)} 条记录，耗时 {execution_time:.2f}秒\")\n", "            \n", "            # 缓存结果（小结果集）\n", "            if len(data) <= 1000:\n", "                self.query_cache[cache_key] = data\n", "            \n", "            # 性能统计\n", "            self.performance_stats[sql[:50]] = {\n", "                'execution_time': execution_time,\n", "                'result_count': len(data),\n", "                'timestamp': time.time()\n", "            }\n", "            \n", "            return data\n", "                \n", "        except Exception as e:\n", "            error_msg = f\"SQL执行错误: {str(e)}\"\n", "            custom_print(f\"❌ {error_msg}\")\n", "            \n", "            # 尝试连接恢复\n", "            if \"connection\" in str(e).lower():\n", "                try:\n", "                    custom_print(\"🔄 尝试重新连接数据库...\")\n", "                    self._init_database()\n", "                    return self.execute_sql_query(sql)  # 递归重试一次\n", "                except:\n", "                    pass\n", "            \n", "            return error_msg\n", "    \n", "    def natural_language_to_sql(self, question: str, expert_knowledge: str = \"\") -> str:\n", "        \"\"\"自然语言转SQL查询 - 智能增强版\"\"\"\n", "        \n", "        # 特殊问题处理\n", "        if \"男女\" in question and (\"比例\" in question or \"分布\" in question):\n", "            return \"\"\"\n", "            SELECT \n", "                CASE \n", "                    WHEN 性别 = '男' OR 性别 = 'M' OR 性别 = 'male' THEN '男性'\n", "                    WHEN 性别 = '女' OR 性别 = 'F' OR 性别 = 'female' THEN '女性'\n", "                    ELSE '其他'\n", "                END as 性别分类,\n", "                COUNT(*) as 用户数量,\n", "                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_table WHERE 性别 IS NOT NULL), 2) as 比例\n", "            FROM user_table \n", "            WHERE 性别 IS NOT NULL\n", "            GROUP BY 性别分类\n", "            ORDER BY 用户数量 DESC\n", "            \"\"\"\n", "        \n", "        # 智能查询类型识别\n", "        query_type = self._identify_query_type(question)\n", "        suggested_tables = self._suggest_tables(question)\n", "        \n", "        system_prompt = f\"\"\"你是专业的SQL查询生成专家，专门处理共享充电宝数据仓库的查询需求。\n", "\n", "{self.get_database_info()}\n", "\n", "## 专家知识参考\n", "{expert_knowledge}\n", "\n", "## 智能分析结果\n", "- 查询类型: {query_type}\n", "- 建议使用表: {', '.join(suggested_tables)}\n", "\n", "## 查询生成规则\n", "1. 仔细分析用户的自然语言问题\n", "2. 根据查询类型选择合适的SQL模式\n", "3. 优先使用汇总表进行快速查询\n", "4. 需要详细信息时才使用多表关联\n", "5. 注意中文字段名的准确性\n", "6. 对于排序查询，默认使用DESC降序\n", "7. 添加适当的LIMIT限制结果数量\n", "8. 使用聚合函数进行统计分析\n", "\n", "## 特殊处理规则\n", "- 性别分析：使用user_table，注意性别字段可能的不同值\n", "- 品牌分析：优先使用brand_revenue_summary\n", "- 地区分析：根据需求选择region_heatmap_data或region_table\n", "- 用户分析：结合user_table和user_behavior_summary\n", "- 时间分析：使用time_table进行时间维度关联\n", "\n", "用户问题: {question}\n", "\n", "请只返回SQL语句，不要包含其他解释文字。\"\"\"\n", "\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": question}\n", "        ]\n", "        \n", "        try:\n", "            response = self.client.chat.completions.create(\n", "                model=\"deepseek-chat\",\n", "                messages=messages,\n", "                temperature=0.1,\n", "                max_tokens=1000\n", "            )\n", "            \n", "            sql_query = response.choices[0].message.content.strip()\n", "            \n", "            # 清理SQL语句\n", "            if sql_query.startswith('```sql'):\n", "                sql_query = sql_query[6:]\n", "            if sql_query.endswith('```'):\n", "                sql_query = sql_query[:-3]\n", "            \n", "            # SQL优化和验证\n", "            sql_query = self._optimize_sql(sql_query.strip())\n", "            \n", "            custom_print(f\"🔧 生成SQL查询 - 类型: {query_type}\")\n", "            custom_print(f\"📊 涉及表: {', '.join(suggested_tables)}\")\n", "            \n", "            return sql_query\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"❌ SQL生成失败: {str(e)}\")\n", "            return \"\"\n", "    \n", "    def _identify_query_type(self, question: str) -> str:\n", "        \"\"\"智能识别查询类型\"\"\"\n", "        question_lower = question.lower()\n", "        \n", "        for query_type, template in self.query_templates.items():\n", "            if any(pattern in question_lower for pattern in template[\"pattern\"]):\n", "                return query_type\n", "        \n", "        return \"综合查询\"\n", "    \n", "    def _suggest_tables(self, question: str) -> List[str]:\n", "        \"\"\"建议使用的表\"\"\"\n", "        question_lower = question.lower()\n", "        suggested = []\n", "        \n", "        # 关键词映射表\n", "        table_keywords = {\n", "            \"user_table\": [\"用户\", \"姓名\", \"年龄\", \"性别\", \"职业\", \"地址\"],\n", "            \"region_table\": [\"地区\", \"省份\", \"城市\", \"区县\", \"经度\", \"纬度\"],\n", "            \"time_table\": [\"时间\", \"日期\", \"年份\", \"月份\", \"星期\", \"周\"],\n", "            \"order_table\": [\"订单\", \"开始时间\", \"结束时间\", \"单价\"],\n", "            \"brand_revenue_summary\": [\"品牌\", \"收入\", \"市场份额\", \"订单数量\"],\n", "            \"user_behavior_summary\": [\"使用次数\", \"总消费\", \"行为\"],\n", "            \"region_heatmap_data\": [\"热力图\", \"城市收入\"],\n", "            \"time_summary\": [\"年度\", \"趋势\", \"记录数\"]\n", "        }\n", "        \n", "        for table, keywords in table_keywords.items():\n", "            if any(keyword in question_lower for keyword in keywords):\n", "                suggested.append(table)\n", "        \n", "        # 如果没有明确匹配，根据问题类型推荐\n", "        if not suggested:\n", "            if any(word in question_lower for word in [\"用户\", \"人\", \"性别\", \"年龄\"]):\n", "                suggested.extend([\"user_table\", \"user_behavior_summary\"])\n", "            elif any(word in question_lower for word in [\"品牌\", \"收入\", \"市场\"]):\n", "                suggested.append(\"brand_revenue_summary\")\n", "            elif any(word in question_lower for word in [\"地区\", \"省份\", \"城市\"]):\n", "                suggested.extend([\"region_table\", \"region_heatmap_data\"])\n", "            else:\n", "                suggested.append(\"order_table\")\n", "        \n", "        return suggested[:3]  # 最多返回3个建议表\n", "    \n", "    def _optimize_sql(self, sql: str) -> str:\n", "        \"\"\"SQL优化\"\"\"\n", "        # 基本优化规则\n", "        optimized_sql = sql\n", "        \n", "        # 添加LIMIT如果没有\n", "        if \"limit\" not in sql.lower() and \"count(\" not in sql.lower():\n", "            if \"order by\" in sql.lower():\n", "                optimized_sql += \" LIMIT 50\"\n", "            else:\n", "                optimized_sql += \" LIMIT 100\"\n", "        \n", "        # 确保GROUP BY查询有ORDER BY\n", "        if \"group by\" in sql.lower() and \"order by\" not in sql.lower():\n", "            if \"count(*)\" in sql.lower():\n", "                optimized_sql += \" ORDER BY COUNT(*) DESC\"\n", "        \n", "        return optimized_sql\n", "    \n", "    def generate_business_insight(self, question: str, data: List[Dict], \n", "                                expert_knowledge: str = \"\") -> str:\n", "        \"\"\"生成业务洞察 - 增强版\"\"\"\n", "        if not data or isinstance(data, str):\n", "            return \"无法生成洞察：查询无结果或出现错误\"\n", "        \n", "        try:\n", "            # 智能数据摘要\n", "            data_summary = self._create_intelligent_summary(data)\n", "            query_type = self._identify_query_type(question)\n", "            \n", "            insight_prompt = f\"\"\"基于以下查询结果，为共享充电宝业务提供专业的数据洞察：\n", "\n", "原始问题: {question}\n", "查询类型: {query_type}\n", "数据记录数: {len(data)}条\n", "\n", "## 数据摘要\n", "{data_summary}\n", "\n", "## 专家知识参考\n", "{expert_knowledge}\n", "\n", "## 分析要求\n", "请提供深度的业务洞察，包括：\n", "\n", "1. **数据解读** (关键数字和趋势)\n", "   - 突出最重要的数据发现\n", "   - 识别异常值和特殊模式\n", "   - 量化关键指标\n", "\n", "2. **业务洞察** (对业务的意义和影响)\n", "   - 分析数据背后的业务含义\n", "   - 识别机会和风险点\n", "   - 对比行业标准或历史数据\n", "\n", "3. **行动建议** (基于数据的具体建议)\n", "   - 提供可执行的具体建议\n", "   - 优先级排序\n", "   - 预期效果评估\n", "\n", "请用专业、简洁的语言回答，重点突出业务价值。\"\"\"\n", "\n", "            messages = [\n", "                {\"role\": \"system\", \"content\": \"你是共享充电宝行业的资深数据分析师，具有丰富的商业洞察能力\"},\n", "                {\"role\": \"user\", \"content\": insight_prompt}\n", "            ]\n", "            \n", "            response = self.client.chat.completions.create(\n", "                model=\"deepseek-chat\",\n", "                messages=messages,\n", "                temperature=0.3,\n", "                max_tokens=1500\n", "            )\n", "            \n", "            insight = response.choices[0].message.content\n", "            \n", "            # 添加数据可视化建议\n", "            viz_suggestion = self._suggest_visualization(question, data)\n", "            if viz_suggestion:\n", "                insight += f\"\\n\\n## 📊 可视化建议\\n{viz_suggestion}\"\n", "            \n", "            return insight\n", "            \n", "        except Exception as e:\n", "            return f\"洞察生成失败: {str(e)}\"\n", "    \n", "    def _create_intelligent_summary(self, data: List[Dict]) -> str:\n", "        \"\"\"创建智能数据摘要\"\"\"\n", "        if not data:\n", "            return \"无数据\"\n", "        \n", "        summary = f\"数据样本 (前3条):\\n\"\n", "        \n", "        # 显示前3条数据\n", "        for i, record in enumerate(data[:3]):\n", "            summary += f\"记录{i+1}: \"\n", "            # 只显示关键字段\n", "            key_fields = []\n", "            for key, value in record.items():\n", "                if len(key_fields) < 5:  # 最多显示5个字段\n", "                    key_fields.append(f\"{key}={value}\")\n", "            summary += \", \".join(key_fields) + \"\\n\"\n", "        \n", "        # 数据统计\n", "        if len(data) > 3:\n", "            summary += f\"\\n总计 {len(data)} 条记录\"\n", "        \n", "        # 数值字段统计\n", "        numeric_stats = self._analyze_numeric_fields(data)\n", "        if numeric_stats:\n", "            summary += f\"\\n\\n数值字段统计:\\n{numeric_stats}\"\n", "        \n", "        return summary\n", "    \n", "    def _analyze_numeric_fields(self, data: List[Dict]) -> str:\n", "        \"\"\"分析数值字段\"\"\"\n", "        if not data:\n", "            return \"\"\n", "        \n", "        numeric_fields = {}\n", "        for record in data:\n", "            for key, value in record.items():\n", "                if isinstance(value, (int, float)) and key not in numeric_fields:\n", "                    numeric_fields[key] = []\n", "        \n", "        # 收集数值\n", "        for record in data:\n", "            for key in numeric_fields:\n", "                if key in record and isinstance(record[key], (int, float)):\n", "                    numeric_fields[key].append(record[key])\n", "        \n", "        # 生成统计\n", "        stats = []\n", "        for field, values in numeric_fields.items():\n", "            if values:\n", "                stats.append(f\"- {field}: 最大值={max(values)}, 最小值={min(values)}, 平均值={sum(values)/len(values):.2f}\")\n", "        \n", "        return \"\\n\".join(stats[:3])  # 最多显示3个字段统计\n", "    \n", "    def _suggest_visualization(self, question: str, data: List[Dict]) -> str:\n", "        \"\"\"建议可视化方式\"\"\"\n", "        if not data:\n", "            return \"\"\n", "        \n", "        question_lower = question.lower()\n", "        suggestions = []\n", "        \n", "        # 根据问题类型建议图表\n", "        if any(word in question_lower for word in [\"分布\", \"占比\", \"比例\"]):\n", "            suggestions.append(\"饼图 - 展示各类别的占比分布\")\n", "            suggestions.append(\"柱状图 - 对比各类别的数量\")\n", "        \n", "        elif any(word in question_lower for word in [\"排名\", \"排序\", \"top\"]):\n", "            suggestions.append(\"水平柱状图 - 展示排名对比\")\n", "            suggestions.append(\"表格 - 详细排名列表\")\n", "        \n", "        elif any(word in question_lower for word in [\"趋势\", \"时间\", \"年度\", \"月度\"]):\n", "            suggestions.append(\"折线图 - 展示时间趋势变化\")\n", "            suggestions.append(\"面积图 - 展示累积趋势\")\n", "        \n", "        elif any(word in question_lower for word in [\"地区\", \"省份\", \"城市\"]):\n", "            suggestions.append(\"地图热力图 - 展示地理分布\")\n", "            suggestions.append(\"柱状图 - 地区对比分析\")\n", "        \n", "        else:\n", "            suggestions.append(\"柱状图 - 基础对比分析\")\n", "            suggestions.append(\"表格 - 详细数据展示\")\n", "        \n", "        return \"\\n\".join(f\"• {suggestion}\" for suggestion in suggestions[:2])\n", "    \n", "    def get_performance_stats(self) -> Dict:\n", "        \"\"\"获取性能统计\"\"\"\n", "        return {\n", "            \"total_queries\": len(self.performance_stats),\n", "            \"cache_size\": len(self.query_cache),\n", "            \"avg_execution_time\": sum(stat['execution_time'] for stat in self.performance_stats.values()) / len(self.performance_stats) if self.performance_stats else 0,\n", "            \"recent_queries\": list(self.performance_stats.keys())[-5:]\n", "        }\n", "    \n", "    def clear_cache(self):\n", "        \"\"\"清空查询缓存\"\"\"\n", "        self.query_cache.clear()\n", "        custom_print(\"🗑️ 查询缓存已清空\")\n", "\n", "custom_print(\"✅ 模块3: 智能信息查询系统 - 加载完成\")\n", "custom_print(\"🎯 支持智能SQL生成、查询优化、性能监控\")\n", "custom_print(\"📊 完整支持9表架构，智能表关联和业务洞察\")\n", "custom_print(\"🚀 包含查询缓存、连接恢复、可视化建议等增强功能\")"]}, {"cell_type": "code", "execution_count": null, "id": "cbf925d5", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 🚀 融合系统: 智能数据分析助手 - 9表结构完整版\n", "# 作用: 深度整合三个模块，提供企业级智能分析能力\n", "# ===================================================================\n", "\n", "class PowerBankIntelligentQueryEngine:\n", "    \"\"\"\n", "    共享充电宝智能查询引擎 - 修复9表结构版本\n", "    \n", "    核心功能:\n", "    1. 🔍 智能SQL生成 - 基于自然语言生成优化查询\n", "    2. 📊 9表架构支持 - 完整支持维度表、事实表、汇总表\n", "    3. 🧠 业务洞察生成 - 结合行业知识的深度分析\n", "    4. ⚡ 查询性能优化 - 缓存、连接池、查询优化\n", "    5. 🔄 自动表结构修复 - 检测并创建缺失的汇总表\n", "    \"\"\"\n", "    \n", "    def __init__(self, db_config: Dict, api_config: Dict):\n", "        self.db_config = db_config\n", "        self.api_config = api_config\n", "        self.client = self._init_openai_client()\n", "        self.connection = None\n", "        self.table_schemas = {}\n", "        self.query_cache = {}\n", "        self.performance_stats = {\n", "            \"total_queries\": 0,\n", "            \"cache_hits\": 0,\n", "            \"avg_response_time\": 0,\n", "            \"error_count\": 0\n", "        }\n", "        \n", "        # 9表结构定义\n", "        self.dimension_tables = [\"user_table\", \"region_table\", \"time_table\"]\n", "        self.fact_tables = [\"order_table\"]\n", "        self.summary_tables = [\"brand_revenue_summary\", \"user_behavior_summary\", \n", "                              \"time_summary\", \"region_usage_summary\", \"region_heatmap_data\"]\n", "        self.expected_tables = self.dimension_tables + self.fact_tables + self.summary_tables\n", "\n", "          # 🔥 新增：初始化中英文字段映射\n", "        self._init_field_mapping()\n", "    \n", "        # 初始化数据库连接\n", "        self._init_database()\n", "        self._load_table_schemas()\n", "        self._init_business_templates()\n", "\n", "    def _init_field_mapping(self):\n", "        \"\"\"初始化中英文字段映射\"\"\"\n", "        self.field_mapping = {\n", "            # user_table 映射\n", "            \"user_table\": {\n", "                \"用户ID\": \"user_id\",\n", "                \"姓名\": \"name\", \n", "                \"年龄\": \"age\",\n", "                \"性别\": \"gender\",\n", "                \"地址\": \"address\",\n", "                \"职业\": \"occupation\"\n", "            },\n", "            # order_table 映射\n", "            \"order_table\": {\n", "                \"订单ID\": \"order_id\",\n", "                \"用户ID\": \"user_id\",\n", "                \"地区ID\": \"region_id\",\n", "                \"时间ID\": \"time_id\",\n", "                \"开始时间\": \"start_time\",\n", "                \"结束时间\": \"end_time\",\n", "                \"单价\": \"price\",\n", "                \"品牌\": \"brand\"\n", "            },\n", "            # region_table 映射\n", "            \"region_table\": {\n", "                \"地区ID\": \"region_id\",\n", "                \"省份\": \"province\",\n", "                \"城市\": \"city\",\n", "                \"经度\": \"longitude\",\n", "                \"纬度\": \"latitude\"\n", "            },\n", "            # time_table 映射\n", "            \"time_table\": {\n", "                \"时间ID\": \"time_id\",\n", "                \"年\": \"year\",\n", "                \"月\": \"month\",\n", "                \"日\": \"day\",\n", "                \"年月\": \"year_month\"\n", "            },\n", "            # brand_revenue_summary 映射\n", "            \"brand_revenue_summary\": {\n", "                \"品牌\": \"brand\",\n", "                \"订单数量\": \"order_count\",\n", "                \"总收入\": \"total_revenue\",\n", "                \"平均单价\": \"avg_price\",\n", "                \"市场份额\": \"market_share\"\n", "            },\n", "            # user_behavior_summary 映射\n", "            \"user_behavior_summary\": {\n", "                \"用户ID\": \"user_id\",\n", "                \"姓名\": \"name\",\n", "                \"总消费\": \"total_consumption\",\n", "                \"使用次数\": \"usage_count\",\n", "                \"平均消费\": \"avg_consumption\"\n", "            },\n", "            # region_usage_summary 映射\n", "            \"region_usage_summary\": {\n", "                \"省份\": \"province\",\n", "                \"总收入\": \"total_revenue\",\n", "                \"使用次数\": \"usage_count\",\n", "                \"用户数量\": \"user_count\"\n", "            },\n", "            # region_heatmap_data 映射\n", "            \"region_heatmap_data\": {\n", "                \"省份\": \"province\",\n", "                \"城市\": \"city\",\n", "                \"经度\": \"longitude\",\n", "                \"纬度\": \"latitude\",\n", "                \"总收入\": \"total_revenue\"\n", "            },\n", "            # time_summary 映射\n", "            \"time_summary\": {\n", "                \"年月\": \"year_month\",\n", "                \"总收入\": \"total_revenue\",\n", "                \"记录数\": \"record_count\",\n", "                \"活跃用户数\": \"active_users\"\n", "            }\n", "        }\n", "    \n", "    # 反向映射（英文->中文）\n", "    self.reverse_field_mapping = {}\n", "    for table, fields in self.field_mapping.items():\n", "        self.reverse_field_mapping[table] = {v: k for k, v in fields.items()}\n", "    \n", "    custom_print(\"🔄 中英文字段映射表初始化完成\")\n", "    \n", "    def _init_openai_client(self):\n", "        \"\"\"初始化OpenAI客户端\"\"\"\n", "        import httpx\n", "        from openai import OpenAI\n", "        \n", "        http_client = httpx.Client(\n", "            timeout=30.0,\n", "            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)\n", "        )\n", "        \n", "        return OpenAI(\n", "            api_key=self.api_config['api_key'],\n", "            base_url=self.api_config['base_url'],\n", "            http_client=http_client\n", "        )\n", "    \n", "    def _init_database(self):\n", "        \"\"\"初始化数据库连接 - 增强版本\"\"\"\n", "        try:\n", "            import pymysql\n", "            self.connection = pymysql.connect(**self.db_config)\n", "            custom_print(\"✅ 数据库连接成功\")\n", "                \n", "        except Exception as e:\n", "            custom_print(f\"❌ 数据库连接失败: {e}\")\n", "            raise\n", "    \n", "    def _load_table_schemas(self):\n", "        \"\"\"加载所有表的结构信息 - 修复9表检测问题\"\"\"\n", "        cursor = self.connection.cursor()\n", "        \n", "        try:\n", "            # 获取所有表名\n", "            cursor.execute(\"SHOW TABLES\")\n", "            tables = cursor.fetchall()\n", "            actual_tables = [table[0] for table in tables]\n", "            \n", "            custom_print(f\"🔍 数据库中实际存在的表: {actual_tables}\")\n", "            custom_print(f\"📋 期望的9个表: {self.expected_tables}\")\n", "            \n", "            # 检查缺失的表\n", "            missing_tables = [table for table in self.expected_tables if table not in actual_tables]\n", "            if missing_tables:\n", "                custom_print(f\"⚠️ 缺失的表: {missing_tables}\")\n", "                custom_print(\"💡 建议检查以下可能的原因:\")\n", "                custom_print(\"   1. 表名是否正确（检查大小写、下划线等）\")\n", "                custom_print(\"   2. 汇总表是否已创建（ADS层数据）\")\n", "                custom_print(\"   3. 数据库连接是否指向正确的schema\")\n", "                \n", "                # 尝试自动修复\n", "                custom_print(\"🔄 尝试自动创建缺失的汇总表...\")\n", "                if self.check_and_create_missing_tables():\n", "                    # 重新获取表列表\n", "                    cursor.execute(\"SHOW TABLES\")\n", "                    tables = cursor.fetchall()\n", "                    actual_tables = [table[0] for table in tables]\n", "                    custom_print(\"✅ 汇总表创建完成，重新加载表结构\")\n", "            \n", "            # 检查多余的表\n", "            extra_tables = [table for table in actual_tables if table not in self.expected_tables]\n", "            if extra_tables:\n", "                custom_print(f\"ℹ️ 发现额外的表: {extra_tables}\")\n", "            \n", "            for table_name in actual_tables:\n", "                # 获取表结构\n", "                cursor.execute(f\"DESCRIBE {table_name}\")\n", "                columns = cursor.fetchall()\n", "                \n", "                # 获取表统计信息\n", "                cursor.execute(f\"SELECT COUNT(*) FROM {table_name}\")\n", "                row_count = cursor.fetchone()[0]\n", "                \n", "                # 获取示例数据（更智能的采样）\n", "                if row_count > 1000:\n", "                    cursor.execute(f\"SELECT * FROM {table_name} ORDER BY RAND() LIMIT 3\")\n", "                else:\n", "                    cursor.execute(f\"SELECT * FROM {table_name} LIMIT 3\")\n", "                \n", "                sample_data = cursor.fetchall()\n", "                column_names = [desc[0] for desc in cursor.description]\n", "                \n", "                # 分析字段类型和特征\n", "                field_analysis = self._analyze_table_fields(table_name, columns, sample_data, column_names)\n", "                \n", "                self.table_schemas[table_name] = {\n", "                    'columns': columns,\n", "                    'sample_data': sample_data,\n", "                    'column_names': column_names,\n", "                    'row_count': row_count,\n", "                    'field_analysis': field_analysis,\n", "                    'table_type': self._classify_table_type(table_name),\n", "                    'business_purpose': self._get_table_business_purpose(table_name)\n", "                }\n", "            \n", "            cursor.close()\n", "            \n", "            # 更新统计信息\n", "            actual_dimension_tables = [t for t in self.dimension_tables if t in actual_tables]\n", "            actual_fact_tables = [t for t in self.fact_tables if t in actual_tables]\n", "            actual_summary_tables = [t for t in self.summary_tables if t in actual_tables]\n", "            \n", "            custom_print(f\"📊 已加载 {len(self.table_schemas)} 个表的完整结构信息\")\n", "            custom_print(f\"📈 维度表: {len(actual_dimension_tables)}个 {actual_dimension_tables}\")\n", "            custom_print(f\"📋 事实表: {len(actual_fact_tables)}个 {actual_fact_tables}\")\n", "            custom_print(f\"📊 汇总表: {len(actual_summary_tables)}个 {actual_summary_tables}\")\n", "            \n", "            # 验证9表结构\n", "            verification = self.verify_nine_table_structure()\n", "            if verification[\"structure_complete\"]:\n", "                custom_print(\"🎉 9表结构验证完整！\")\n", "            else:\n", "                custom_print(f\"⚠️ 表结构不完整: {verification['total_found']}/9\")\n", "            \n", "        except Exception as e:\n", "            cursor.close()\n", "            custom_print(f\"❌ 加载表结构失败: {e}\")\n", "            raise\n", "\n", "    def check_and_create_missing_tables(self):\n", "        \"\"\"检查并尝试创建缺失的汇总表\"\"\"\n", "        cursor = self.connection.cursor()\n", "        \n", "        try:\n", "            # 检查基础表是否存在\n", "            base_tables = [\"user_table\", \"region_table\", \"time_table\", \"order_table\"]\n", "            cursor.execute(\"SHOW TABLES\")\n", "            existing_tables = [table[0] for table in cursor.fetchall()]\n", "            \n", "            missing_base = [t for t in base_tables if t not in existing_tables]\n", "            if missing_base:\n", "                custom_print(f\"❌ 缺少基础表: {missing_base}，无法创建汇总表\")\n", "                return False\n", "            \n", "            # 尝试创建缺失的汇总表\n", "            summary_tables_sql = {\n", "                \"brand_revenue_summary\": \"\"\"\n", "                    CREATE TABLE IF NOT EXISTS brand_revenue_summary AS\n", "                    SELECT \n", "                        品牌,\n", "                        COUNT(*) as 订单数量,\n", "                        SUM(单价) as 总收入,\n", "                        AVG(单价) as 平均单价,\n", "                        ROUND(SUM(单价) * 100.0 / (SELECT SUM(单价) FROM order_table), 2) as 市场份额\n", "                    FROM order_table \n", "                    GROUP BY 品牌\n", "                    ORDER BY 总收入 DESC\n", "                \"\"\",\n", "                \n", "                \"user_behavior_summary\": \"\"\"\n", "                    CREATE TABLE IF NOT EXISTS user_behavior_summary AS\n", "                    SELECT \n", "                        o.用户ID,\n", "                        u.姓名,\n", "                        u.性别,\n", "                        u.年龄,\n", "                        COUNT(*) as 使用次数,\n", "                        SUM(o.单价) as 总消费,\n", "                        AVG(o.单价) as 平均消费,\n", "                        MAX(o.单价) as 最高消费\n", "                    FROM order_table o\n", "                    JOIN user_table u ON o.用户ID = u.用户ID\n", "                    GROUP BY o.用户ID, u.姓名, u.性别, u.年龄\n", "                    ORDER BY 总消费 DESC\n", "                \"\"\",\n", "                \n", "                \"region_usage_summary\": \"\"\"\n", "                    CREATE TABLE IF NOT EXISTS region_usage_summary AS\n", "                    SELECT \n", "                        o.地区ID,\n", "                        r.省份,\n", "                        r.城市,\n", "                        COUNT(*) as 使用次数,\n", "                        SUM(o.单价) as 总收入,\n", "                        AVG(o.单价) as 平均单价,\n", "                        COUNT(DISTINCT o.用户ID) as 用户数量\n", "                    FROM order_table o\n", "                    JOIN region_table r ON o.地区ID = r.地区ID\n", "                    GROUP BY o.地区ID, r.省份, r.城市\n", "                    ORDER BY 总收入 DESC\n", "                \"\"\",\n", "                \n", "                \"time_summary\": \"\"\"\n", "                    CREATE TABLE IF NOT EXISTS time_summary AS\n", "                    SELECT \n", "                        o.时间ID,\n", "                        t.年份,\n", "                        t.年月,\n", "                        COUNT(*) as 记录数,\n", "                        SUM(o.单价) as 总收入,\n", "                        AVG(o.单价) as 平均单价,\n", "                        COUNT(DISTINCT o.用户ID) as 活跃用户数\n", "                    FROM order_table o\n", "                    JOIN time_table t ON o.时间ID = t.时间ID\n", "                    GROUP BY o.时间ID, t.年份, t.年月\n", "                    ORDER BY t.年份, t.年月\n", "                \"\"\",\n", "                \n", "                \"region_heatmap_data\": \"\"\"\n", "                    CREATE TABLE IF NOT EXISTS region_heatmap_data AS\n", "                    SELECT \n", "                        r.省份,\n", "                        r.城市,\n", "                        r.经度,\n", "                        r.纬度,\n", "                        COUNT(*) as 使用频次,\n", "                        SUM(o.单价) as 总收入,\n", "                        COUNT(DISTINCT o.用户ID) as 用户数量\n", "                    FROM order_table o\n", "                    JOIN region_table r ON o.地区ID = r.地区ID\n", "                    GROUP BY r.省份, r.城市, r.经度, r.纬度\n", "                    HAVING 使用频次 > 0\n", "                    ORDER BY 总收入 DESC\n", "                \"\"\"\n", "            }\n", "            \n", "            created_tables = []\n", "            for table_name, sql in summary_tables_sql.items():\n", "                if table_name not in existing_tables:\n", "                    try:\n", "                        custom_print(f\"🔄 正在创建汇总表: {table_name}\")\n", "                        cursor.execute(sql)\n", "                        self.connection.commit()\n", "                        created_tables.append(table_name)\n", "                        custom_print(f\"✅ 成功创建: {table_name}\")\n", "                    except Exception as e:\n", "                        custom_print(f\"❌ 创建 {table_name} 失败: {e}\")\n", "            \n", "            cursor.close()\n", "            return len(created_tables) > 0\n", "            \n", "        except Exception as e:\n", "            cursor.close()\n", "            custom_print(f\"❌ 创建汇总表过程失败: {e}\")\n", "            return False\n", "\n", "    def verify_nine_table_structure(self):\n", "        \"\"\"验证9表结构完整性\"\"\"\n", "        expected_structure = {\n", "            \"维度表\": [\"user_table\", \"region_table\", \"time_table\"],\n", "            \"事实表\": [\"order_table\"], \n", "            \"汇总表\": [\"brand_revenue_summary\", \"user_behavior_summary\", \n", "                     \"time_summary\", \"region_usage_summary\", \"region_heatmap_data\"]\n", "        }\n", "        \n", "        verification_result = {\n", "            \"total_expected\": 9,\n", "            \"total_found\": len(self.table_schemas),\n", "            \"missing_tables\": [],\n", "            \"structure_complete\": <PERSON><PERSON><PERSON>,\n", "            \"recommendations\": []\n", "        }\n", "        \n", "        for category, tables in expected_structure.items():\n", "            missing_in_category = [t for t in tables if t not in self.table_schemas]\n", "            if missing_in_category:\n", "                verification_result[\"missing_tables\"].extend(missing_in_category)\n", "        \n", "        verification_result[\"structure_complete\"] = len(verification_result[\"missing_tables\"]) == 0\n", "        \n", "        return verification_result\n", "    \n", "    def _analyze_table_fields(self, table_name: str, columns: List, sample_data: List, column_names: List) -> Dict:\n", "        \"\"\"分析表字段特征\"\"\"\n", "        analysis = {\n", "            'primary_keys': [],\n", "            'foreign_keys': [],\n", "            'business_keys': [],\n", "            'measure_fields': [],\n", "            'dimension_fields': [],\n", "            'date_fields': [],\n", "            'text_fields': []\n", "        }\n", "        \n", "        for col in columns:\n", "            field_name, field_type = col[0], col[1]\n", "            field_lower = field_name.lower()\n", "            \n", "            # 主键识别\n", "            if 'id' in field_lower and (field_lower.endswith('id') or field_lower == 'id'):\n", "                if table_name.lower() in field_lower or field_lower == 'id':\n", "                    analysis['primary_keys'].append(field_name)\n", "                else:\n", "                    analysis['foreign_keys'].append(field_name)\n", "            \n", "            # 业务字段识别\n", "            business_indicators = ['姓名', '品牌', '省份', '城市', '年份', '年月']\n", "            if any(indicator in field_name for indicator in business_indicators):\n", "                analysis['business_keys'].append(field_name)\n", "            \n", "            # 度量字段识别\n", "            measure_indicators = ['单价', '收入', '数量', '次数', '消费', '份额']\n", "            if any(indicator in field_name for indicator in measure_indicators):\n", "                analysis['measure_fields'].append(field_name)\n", "            \n", "            # 维度字段识别\n", "            dimension_indicators = ['性别', '年龄', '地区', '时间', '用户']\n", "            if any(indicator in field_name for indicator in dimension_indicators):\n", "                analysis['dimension_fields'].append(field_name)\n", "            \n", "            # 日期字段识别\n", "            if 'date' in field_type.lower() or 'time' in field_type.lower():\n", "                analysis['date_fields'].append(field_name)\n", "            \n", "            # 文本字段识别\n", "            if 'varchar' in field_type.lower() or 'text' in field_type.lower():\n", "                analysis['text_fields'].append(field_name)\n", "        \n", "        return analysis\n", "    \n", "    def _classify_table_type(self, table_name: str) -> str:\n", "        \"\"\"分类表类型\"\"\"\n", "        if table_name in self.dimension_tables:\n", "            return \"维度表\"\n", "        elif table_name in self.fact_tables:\n", "            return \"事实表\"\n", "        elif table_name in self.summary_tables:\n", "            return \"汇总表\"\n", "        else:\n", "            return \"其他表\"\n", "    \n", "    def _get_table_business_purpose(self, table_name: str) -> str:\n", "        \"\"\"获取表的业务用途\"\"\"\n", "        purposes = {\n", "            \"user_table\": \"存储用户基本信息，支持用户画像分析\",\n", "            \"region_table\": \"存储地理位置信息，支持地域分析\",\n", "            \"time_table\": \"存储时间维度信息，支持时间趋势分析\",\n", "            \"order_table\": \"存储订单事实数据，核心业务数据表\",\n", "            \"brand_revenue_summary\": \"品牌收入汇总，支持品牌竞争分析\",\n", "            \"user_behavior_summary\": \"用户行为汇总，支持用户价值分析\",\n", "            \"time_summary\": \"时间维度汇总，支持趋势分析\",\n", "            \"region_usage_summary\": \"地区使用汇总，支持区域运营分析\",\n", "            \"region_heatmap_data\": \"地区热力图数据，支持可视化展示\"\n", "        }\n", "        return purposes.get(table_name, \"业务用途待定义\")\n", "    \n", "    def _init_business_templates(self):\n", "        \"\"\"初始化业务查询模板\"\"\"\n", "        self.query_templates = {\n", "            \"品牌分析\": {\n", "                \"市场份额\": \"SELECT 品牌, 总收入, 市场份额 FROM brand_revenue_summary ORDER BY 总收入 DESC\",\n", "                \"品牌对比\": \"SELECT 品牌, 订单数量, 总收入, 平均单价 FROM brand_revenue_summary\",\n", "            },\n", "            \"用户分析\": {\n", "                \"用户价值\": \"SELECT 姓名, 总消费, 使用次数, 平均消费 FROM user_behavior_summary ORDER BY 总消费 DESC LIMIT 10\",\n", "                \"用户分层\": \"SELECT 性别, COUNT(*) as 用户数, AVG(总消费) as 平均消费 FROM user_behavior_summary GROUP BY 性别\",\n", "            },\n", "            \"地区分析\": {\n", "                \"地区收入\": \"SELECT 省份, 总收入, 使用次数, 用户数量 FROM region_usage_summary ORDER BY 总收入 DESC\",\n", "                \"热力图数据\": \"SELECT 省份, 城市, 经度, 纬度, 总收入 FROM region_heatmap_data ORDER BY 总收入 DESC\",\n", "            },\n", "            \"时间分析\": {\n", "                \"时间趋势\": \"SELECT 年月, 总收入, 记录数, 活跃用户数 FROM time_summary ORDER BY 年月\",\n", "                \"月度对比\": \"SELECT 年月, 总收入 FROM time_summary ORDER BY 年月\",\n", "            }\n", "        }\n", "        custom_print(\"📋 业务查询模板已加载\")\n", "    \n", "    def generate_sql_query(self, question: str, context: str = \"\") -> Dict:\n", "        \"\"\"生成SQL查询\"\"\"\n", "        start_time = datetime.now()\n", "        \n", "        try:\n", "            # 构建表结构信息\n", "            schema_info = self._build_schema_context()\n", "            \n", "            # 构建提示词\n", "            prompt = f\"\"\"基于以下数据库表结构，为用户问题生成准确的SQL查询：\n", "\n", "## 数据库表结构（共{len(self.table_schemas)}个表）\n", "{schema_info}\n", "\n", "## 用户问题\n", "{question}\n", "\n", "## 上下文信息\n", "{context}\n", "\n", "## 要求\n", "1. 生成标准的MySQL查询语句\n", "2. 使用适当的JOIN连接相关表\n", "3. 确保字段名和表名准确\n", "4. 添加必要的排序和限制\n", "5. 优化查询性能\n", "\n", "请直接返回SQL语句，不需要解释。\"\"\"\n", "\n", "            response = self.client.chat.completions.create(\n", "                model=\"deepseek-chat\",\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": \"你是SQL查询专家，专门为共享充电宝数据分析生成优化的SQL查询。\"},\n", "                    {\"role\": \"user\", \"content\": prompt}\n", "                ],\n", "                temperature=0.1\n", "            )\n", "            \n", "            sql_query = response.choices[0].message.content.strip()\n", "            \n", "            # 清理SQL语句\n", "            sql_query = self._clean_sql_query(sql_query)\n", "            \n", "            # 更新性能统计\n", "            response_time = (datetime.now() - start_time).total_seconds()\n", "            self.performance_stats[\"total_queries\"] += 1\n", "            self.performance_stats[\"avg_response_time\"] = (\n", "                (self.performance_stats[\"avg_response_time\"] * (self.performance_stats[\"total_queries\"] - 1) + response_time) \n", "                / self.performance_stats[\"total_queries\"]\n", "            )\n", "            \n", "            return {\n", "                \"sql_query\": sql_query,\n", "                \"response_time\": response_time,\n", "                \"success\": True,\n", "                \"error\": None\n", "            }\n", "            \n", "        except Exception as e:\n", "            self.performance_stats[\"error_count\"] += 1\n", "            return {\n", "                \"sql_query\": None,\n", "                \"response_time\": (datetime.now() - start_time).total_seconds(),\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"error\": str(e)\n", "            }\n", "    \n", "    def _build_schema_context(self) -> str:\n", "        \"\"\"构建表结构上下文\"\"\"\n", "        schema_parts = []\n", "        \n", "        for table_name, schema in self.table_schemas.items():\n", "            table_type = schema.get('table_type', '未知')\n", "            row_count = schema.get('row_count', 0)\n", "            business_purpose = schema.get('business_purpose', '')\n", "            \n", "            columns_info = []\n", "            for col in schema.get('columns', []):\n", "                field_name, field_type = col[0], col[1]\n", "                columns_info.append(f\"  {field_name} ({field_type})\")\n", "            \n", "            schema_part = f\"\"\"\n", "### {table_name} ({table_type}) - {row_count}行\n", "用途: {business_purpose}\n", "字段:\n", "{chr(10).join(columns_info)}\"\"\"\n", "            \n", "            schema_parts.append(schema_part)\n", "        \n", "        return \"\\n\".join(schema_parts)\n", "    \n", "    def _clean_sql_query(self, sql_query: str) -> str:\n", "        \"\"\"清理SQL查询语句\"\"\"\n", "        # 移除markdown代码块标记\n", "        sql_query = sql_query.replace(\"```sql\", \"\").replace(\"```\", \"\")\n", "        \n", "        # 移除多余的空白字符\n", "        sql_query = sql_query.strip()\n", "        \n", "        # 确保以分号结尾\n", "        if not sql_query.endswith(';'):\n", "            sql_query += ';'\n", "        \n", "        return sql_query\n", "    \n", "    def execute_query(self, sql_query: str) -> List[Dict]:\n", "        \"\"\"执行查询\"\"\"\n", "        cursor = self.connection.cursor()\n", "        \n", "        try:\n", "            cursor.execute(sql_query)\n", "            results = cursor.fetchall()\n", "            column_names = [desc[0] for desc in cursor.description]\n", "            \n", "            # 转换为字典列表\n", "            data = []\n", "            for row in results:\n", "                row_dict = {}\n", "                for i, value in enumerate(row):\n", "                    row_dict[column_names[i]] = value\n", "                data.append(row_dict)\n", "            \n", "            cursor.close()\n", "            return data\n", "            \n", "        except Exception as e:\n", "            cursor.close()\n", "            custom_print(f\"❌ 查询执行失败: {e}\")\n", "            return []\n", "    \n", "    def generate_business_insight(self, question: str, data: List[Dict], expert_knowledge: str = \"\") -> str:\n", "        \"\"\"生成业务洞察\"\"\"\n", "        if not data or isinstance(data, str):\n", "            return \"无法生成洞察：查询无结果或出现错误\"\n", "        \n", "        try:\n", "            # 智能数据摘要\n", "            data_summary = self._create_intelligent_summary(data)\n", "            query_type = self._identify_query_type(question)\n", "            \n", "            insight_prompt = f\"\"\"基于以下查询结果，为共享充电宝业务提供专业的数据洞察：\n", "\n", "原始问题: {question}\n", "查询类型: {query_type}\n", "数据记录数: {len(data)}条\n", "\n", "## 数据摘要\n", "{data_summary}\n", "\n", "## 专家知识参考\n", "{expert_knowledge}\n", "\n", "## 分析要求\n", "请提供深度的业务洞察，包括：\n", "\n", "1. **数据解读** (关键数字和趋势)\n", "   - 突出最重要的数据发现\n", "   - 识别异常值和特殊模式\n", "   - 量化关键指标\n", "\n", "2. **业务洞察** (对业务的意义和影响)\n", "   - 分析数据背后的业务含义\n", "   - 识别机会和风险点\n", "   - 对比行业标准或历史数据\n", "\n", "3. **行动建议** (基于数据的具体建议)\n", "   - 提供可执行的具体建议\n", "   - 优先级排序\n", "   - 预期效果评估\n", "\n", "请用专业、简洁的语言回答，重点突出可执行的商业价值。\"\"\"\n", "\n", "            response = self.client.chat.completions.create(\n", "                model=\"deepseek-chat\",\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": \"你是共享充电宝行业的资深数据分析师，擅长从数据中挖掘商业价值和提供决策建议。\"},\n", "                    {\"role\": \"user\", \"content\": insight_prompt}\n", "                ],\n", "                temperature=0.3\n", "            )\n", "            \n", "            return response.choices[0].message.content\n", "            \n", "        except Exception as e:\n", "            return f\"生成业务洞察时出错: {e}\"\n", "    \n", "    def _create_intelligent_summary(self, data: List[Dict]) -> str:\n", "        \"\"\"创建智能数据摘要\"\"\"\n", "        if not data:\n", "            return \"无数据\"\n", "        \n", "        summary_parts = []\n", "        \n", "        # 基本统计\n", "        summary_parts.append(f\"数据量: {len(data)}条记录\")\n", "        \n", "        # 字段分析\n", "        if data:\n", "            fields = list(data[0].keys())\n", "            summary_parts.append(f\"字段数: {len(fields)}个\")\n", "            \n", "            # 数值字段统计\n", "            numeric_fields = []\n", "            for field in fields:\n", "                try:\n", "                    values = [float(row[field]) for row in data if row[field] is not None]\n", "                    if values:\n", "                        numeric_fields.append({\n", "                            'field': field,\n", "                            'min': min(values),\n", "                            'max': max(values),\n", "                            'avg': sum(values) / len(values)\n", "                        })\n", "                except:\n", "                    continue\n", "            \n", "            if numeric_fields:\n", "                summary_parts.append(\"数值字段统计:\")\n", "                for nf in numeric_fields[:3]:  # 只显示前3个\n", "                    summary_parts.append(f\"  {nf['field']}: 最小值={nf['min']:.2f}, 最大值={nf['max']:.2f}, 平均值={nf['avg']:.2f}\")\n", "        \n", "        return \"\\n\".join(summary_parts)\n", "    \n", "    def _identify_query_type(self, question: str) -> str:\n", "        \"\"\"识别查询类型\"\"\"\n", "        question_lower = question.lower()\n", "        \n", "        if any(word in question_lower for word in ['品牌', 'brand']):\n", "            return \"品牌分析\"\n", "        elif any(word in question_lower for word in ['用户', 'user', '客户']):\n", "            return \"用户分析\"\n", "        elif any(word in question_lower for word in ['地区', '省份', '城市', 'region']):\n", "            return \"地区分析\"\n", "        elif any(word in question_lower for word in ['时间', '月份', '年份', 'time']):\n", "            return \"时间分析\"\n", "        else:\n", "            return \"综合分析\"\n", "    \n", "    def get_performance_stats(self) -> Dict:\n", "        \"\"\"获取性能统计\"\"\"\n", "        return self.performance_stats.copy()\n", "\n", "\n", "class PowerBankIntelligentAssistant:\n", "    \"\"\"\n", "    共享充电宝智能数据分析助手 - 9表结构完整版\n", "    \n", "    深度整合三个核心模块:\n", "    1. 🧠 智能记忆管理 - 上下文感知和学习能力\n", "    2. 📚 专家知识库 - 行业知识和最佳实践\n", "    3. 🔍 智能查询引擎 - 9表架构完整支持和自动修复\n", "    4. 🎯 智能路由系统 - 问题分类和最优处理\n", "    5. 🚀 并行处理能力 - 批量分析和性能优化\n", "    \"\"\"\n", "    \n", "    def __init__(self, db_config: Dict, api_config: Dict, memory_config: Dict = None):\n", "        self.session_id = f\"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "        self.start_time = datetime.now()\n", "        \n", "        # 系统状态监控\n", "        self.system_status = {\n", "            \"session_id\": self.session_id,\n", "            \"start_time\": self.start_time.isoformat(),\n", "            \"modules_loaded\": [],\n", "            \"table_structure_status\": \"未检查\",\n", "            \"health\": {\n", "                \"memory_usage\": \"正常\",\n", "                \"database_connection\": \"未连接\",\n", "                \"api_connection\": \"未连接\"\n", "            }\n", "        }\n", "        \n", "        # 对话上下文管理\n", "        self.conversation_context = {\n", "            \"current_topic\": None,\n", "            \"interaction_count\": 0,\n", "            \"user_preferences\": {},\n", "            \"recent_analyses\": []\n", "        }\n", "        \n", "        try:\n", "            # 初始化三个核心模块 - 增强版\n", "            custom_print(\"🔄 正在初始化核心模块...\")\n", "            \n", "            # 1. 智能记忆模块\n", "            self.short_memory = PowerBankIntelligentMemory(\n", "                count_threshold=memory_config.get('count_threshold', 25) if memory_config else 25,\n", "                token_threshold=memory_config.get('token_threshold', 4000) if memory_config else 4000,\n", "                use_token_mode=memory_config.get('use_token_mode', True) if memory_config else True\n", "            )\n", "            self.system_status[\"modules_loaded\"].append(\"智能记忆模块\")\n", "            \n", "            # 2. 长期记忆和知识管理\n", "            self.long_memory = PowerBankLongTermMemory(\n", "                base_path=memory_config.get('base_path', r'C:\\Users\\<USER>\\Desktop\\powerbank_memory') if memory_config else r'C:\\Users\\<USER>\\Desktop\\powerbank_memory'\n", "            )\n", "            self.system_status[\"modules_loaded\"].append(\"长期记忆模块\")\n", "            \n", "            # 3. 智能查询引擎 - 带9表结构验证\n", "            custom_print(\"🔄 正在初始化智能查询引擎（9表架构）...\")\n", "            self.query_engine = PowerBankIntelligentQueryEngine(db_config, api_config)\n", "            self.system_status[\"modules_loaded\"].append(\"智能查询引擎\")\n", "            \n", "            # 检查9表结构状态\n", "            verification = self.query_engine.verify_nine_table_structure()\n", "            if verification[\"structure_complete\"]:\n", "                self.system_status[\"table_structure_status\"] = \"完整(9/9)\"\n", "                custom_print(\"✅ 9表结构验证完整！\")\n", "            else:\n", "                self.system_status[\"table_structure_status\"] = f\"不完整({verification['total_found']}/9)\"\n", "                custom_print(f\"⚠️ 表结构不完整: {verification['total_found']}/9\")\n", "            \n", "            # 4. 业务智能分析器\n", "            self.business_analyzer = self._init_business_analyzer()\n", "            self.system_status[\"modules_loaded\"].append(\"业务智能分析器\")\n", "            \n", "            # 5. 问题路由器\n", "            self.question_router = self._init_question_router()\n", "            self.system_status[\"modules_loaded\"].append(\"问题路由器\")\n", "            \n", "            # 更新健康状态\n", "            self.system_status[\"health\"][\"database_connection\"] = \"已连接\"\n", "            self.system_status[\"health\"][\"api_connection\"] = \"已连接\"\n", "            \n", "            # 初始化系统消息\n", "            self._initialize_system_context()\n", "            \n", "            custom_print(\"🎉 三模块深度融合系统初始化完成！\")\n", "            custom_print(f\"📊 表结构状态: {self.system_status['table_structure_status']}\")\n", "            custom_print(f\"🆔 会话ID: {self.session_id}\")\n", "            \n", "        except Exception as e:\n", "            custom_print(f\"❌ 系统初始化失败: {e}\")\n", "            raise\n", "    \n", "    def _init_business_analyzer(self):\n", "        \"\"\"初始化业务智能分析器\"\"\"\n", "        return {\n", "            \"trend_analyzer\": self._create_trend_analyzer(),\n", "            \"comparison_engine\": self._create_comparison_engine(),\n", "            \"anomaly_detector\": self._create_anomaly_detector(),\n", "            \"recommendation_system\": self._create_recommendation_system()\n", "        }\n", "    \n", "    def _create_trend_analyzer(self):\n", "        \"\"\"创建趋势分析器\"\"\"\n", "        return {\n", "            \"patterns\": [\"增长\", \"下降\", \"波动\", \"季节性\", \"周期性\"],\n", "            \"metrics\": [\"同比\", \"环比\", \"复合增长率\", \"趋势强度\"],\n", "            \"algorithms\": [\"移动平均\", \"线性回归\", \"时间序列分解\"]\n", "        }\n", "    \n", "    def _create_comparison_engine(self):\n", "        \"\"\"创建对比分析引擎\"\"\"\n", "        return {\n", "            \"dimensions\": [\"品牌\", \"地区\", \"时间\", \"用户群体\"],\n", "            \"metrics\": [\"市场份额\", \"增长率\", \"用户价值\", \"活跃度\"],\n", "            \"methods\": [\"排名分析\", \"差异分析\", \"相关性分析\"]\n", "        }\n", "    \n", "    def _create_anomaly_detector(self):\n", "        \"\"\"创建异常检测器\"\"\"\n", "        return {\n", "            \"detection_methods\": [\"统计异常\", \"业务异常\", \"趋势异常\"],\n", "            \"thresholds\": {\"revenue_drop\": 0.2, \"usage_spike\": 2.0},\n", "            \"alert_levels\": [\"轻微\", \"中等\", \"严重\"]\n", "        }\n", "    \n", "    def _create_recommendation_system(self):\n", "        \"\"\"创建推荐系统\"\"\"\n", "        return {\n", "            \"business_actions\": [\"市场扩张\", \"用户留存\", \"价格优化\", \"运营改进\"],\n", "            \"data_suggestions\": [\"补充数据\", \"深入分析\", \"对比研究\"],\n", "            \"visualization_types\": [\"柱状图\", \"饼图\", \"热力图\", \"趋势图\"]\n", "        }\n", "    \n", "    def _init_question_router(self):\n", "        \"\"\"初始化问题路由器\"\"\"\n", "        return {\n", "            \"database_keywords\": [\"查询\", \"数据\", \"统计\", \"分析\", \"多少\", \"哪个\", \"排行\", \"对比\"],\n", "            \"business_keywords\": [\"建议\", \"策略\", \"如何\", \"为什么\", \"优化\", \"提升\"],\n", "            \"general_keywords\": [\"你好\", \"谢谢\", \"帮助\", \"介绍\", \"什么是\"]\n", "        }\n", "    \n", "    def _initialize_system_context(self):\n", "        \"\"\"初始化系统上下文 - 增强版\"\"\"\n", "        # 获取专家知识\n", "        expert_knowledge = self.long_memory.expert_knowledge.domain_knowledge\n", "        \n", "        # 获取表结构状态\n", "        table_status = self.system_status[\"table_structure_status\"]\n", "        table_count = len(self.query_engine.table_schemas)\n", "        \n", "        system_message = {\n", "            \"role\": \"system\",\n", "            \"content\": f\"\"\"你是共享充电宝行业的顶级数据分析专家，具备以下核心能力：\n", "\n", "## 🎯 核心能力矩阵\n", "\n", "### 1. 🧠 智能记忆管理\n", "- 上下文感知：记住完整对话历史和用户偏好\n", "- 场景识别：自动识别分析场景和业务重点\n", "- 学习能力：从每次交互中积累经验和知识\n", "- 个性化：根据用户习惯提供定制化服务\n", "\n", "### 2. 📊 专业数据分析  \n", "- 自然语言理解：准确理解复杂的业务问题\n", "- SQL智能生成：基于{table_count}表架构生成优化查询\n", "- 多维度分析：支持时间、地区、品牌、用户等维度\n", "- 深度洞察：结合行业知识提供专业见解\n", "\n", "### 3. 🎯 业务智能决策\n", "- 趋势分析：识别业务发展趋势和关键拐点\n", "- 对比分析：多维度竞争分析和基准对比\n", "- 异常检测：及时发现业务异常和风险点\n", "- 策略建议：基于数据提供可执行的行动方案\n", "\n", "### 4. 🔄 智能问答路由\n", "- 问题分类：自动识别数据库查询vs普通问答\n", "- 最佳策略：选择最适合的处理方式\n", "- 容错恢复：查询失败时智能降级处理\n", "- 质量保证：确保回答的准确性和专业性\n", "\n", "## 📚 专家知识库\n", "{expert_knowledge}\n", "\n", "## 🎨 交互风格\n", "- 专业性：使用行业术语和专业分析框架\n", "- 洞察力：不仅提供数据，更要解读数据背后的含义\n", "- 可执行：提供具体、可操作的建议和方案\n", "- 用户友好：复杂分析用简洁易懂的方式表达\n", "\n", "## 🔧 技术特性\n", "- 会话ID: {self.session_id}\n", "- 表结构状态: {table_status}\n", "- 支持模块: {', '.join(self.system_status['modules_loaded'])}\n", "- 智能缓存: 提升查询性能\n", "- 错误恢复: 多层次容错机制\n", "\n", "请始终保持专业、准确、有洞察力的回答风格，为用户提供最有价值的数据分析服务。\"\"\"\n", "        }\n", "        \n", "        self.short_memory.append_message(system_message)\n", "    \n", "    def intelligent_chat(self, user_input: str) -> str:\n", "        \"\"\"智能对话 - 核心方法\"\"\"\n", "        try:\n", "            # 更新对话计数\n", "            self.conversation_context[\"interaction_count\"] += 1\n", "            \n", "            # 添加用户消息到记忆\n", "            user_message = {\"role\": \"user\", \"content\": user_input}\n", "            self.short_memory.append_message(user_message)\n", "            \n", "            # 智能路由决策\n", "            route_decision = self._route_question(user_input)\n", "            \n", "            if route_decision[\"type\"] == \"database_query\":\n", "                # 数据库查询路径\n", "                response = self._handle_database_query(user_input, route_decision)\n", "            elif route_decision[\"type\"] == \"business_consultation\":\n", "                # 业务咨询路径\n", "                response = self._handle_business_consultation(user_input, route_decision)\n", "            else:\n", "                # 普通对话路径\n", "                response = self._handle_general_conversation(user_input, route_decision)\n", "            \n", "            # 添加助手回复到记忆\n", "            assistant_message = {\"role\": \"assistant\", \"content\": response}\n", "            self.short_memory.append_message(assistant_message)\n", "            \n", "            # 更新对话上下文\n", "            self._update_conversation_context(user_input, response, route_decision)\n", "            \n", "            return response\n", "            \n", "        except Exception as e:\n", "            error_response = f\"处理您的问题时出现错误: {e}\\n\\n请尝试重新表述您的问题，或联系技术支持。\"\n", "            return error_response\n", "    \n", "    def _route_question(self, question: str) -> Dict:\n", "        \"\"\"智能问题路由\"\"\"\n", "        question_lower = question.lower()\n", "        \n", "        # 数据库查询关键词检测\n", "        db_score = sum(1 for keyword in self.question_router[\"database_keywords\"] \n", "                      if keyword in question_lower)\n", "        \n", "        # 业务咨询关键词检测\n", "        business_score = sum(1 for keyword in self.question_router[\"business_keywords\"] \n", "                           if keyword in question_lower)\n", "        \n", "        # 普通对话关键词检测\n", "        general_score = sum(1 for keyword in self.question_router[\"general_keywords\"] \n", "                          if keyword in question_lower)\n", "        \n", "        # 决策逻辑\n", "        if db_score >= 2 or any(keyword in question_lower for keyword in [\"查询\", \"数据\", \"多少\", \"哪个\"]):\n", "            return {\n", "                \"type\": \"database_query\",\n", "                \"confidence\": db_score / len(self.question_router[\"database_keywords\"]),\n", "                \"reasoning\": \"检测到数据查询相关关键词\"\n", "            }\n", "        elif business_score >= 1 or any(keyword in question_lower for keyword in [\"建议\", \"如何\", \"策略\"]):\n", "            return {\n", "                \"type\": \"business_consultation\", \n", "                \"confidence\": business_score / len(self.question_router[\"business_keywords\"]),\n", "                \"reasoning\": \"检测到业务咨询相关关键词\"\n", "            }\n", "        else:\n", "            return {\n", "                \"type\": \"general_conversation\",\n", "                \"confidence\": general_score / len(self.question_router[\"general_keywords\"]),\n", "                \"reasoning\": \"普通对话或未明确分类\"\n", "            }\n", "    \n", "    def _handle_database_query(self, question: str, route_info: Dict) -> str:\n", "        \"\"\"处理数据库查询\"\"\"\n", "        try:\n", "            # 生成SQL查询\n", "            sql_result = self.query_engine.generate_sql_query(\n", "                question, \n", "                context=self._get_conversation_context()\n", "            )\n", "            \n", "            if not sql_result[\"success\"]:\n", "                return f\"SQL生成失败: {sql_result['error']}\\n\\n请尝试重新表述您的问题。\"\n", "            \n", "            # 执行查询\n", "            data = self.query_engine.execute_query(sql_result[\"sql_query\"])\n", "            \n", "            if not data:\n", "                return \"查询执行成功，但没有找到相关数据。请检查查询条件或数据是否存在。\"\n", "            \n", "            # 生成业务洞察\n", "            expert_knowledge = self.long_memory.expert_knowledge.domain_knowledge\n", "            insight = self.query_engine.generate_business_insight(\n", "                question, data, expert_knowledge\n", "            )\n", "            \n", "            # 格式化响应\n", "            response = f\"\"\"## 📊 数据查询结果\n", "\n", "**查询问题**: {question}\n", "**数据记录数**: {len(data)}条\n", "**执行时间**: {sql_result['response_time']:.2f}秒\n", "\n", "## 🔍 业务洞察\n", "{insight}\n", "\n", "## 📈 数据详情\n", "查询到{len(data)}条记录，涵盖了您关注的业务指标。如需查看具体数据或进行深入分析，请告诉我。\"\"\"\n", "\n", "            return response\n", "            \n", "        except Exception as e:\n", "            return f\"数据库查询处理失败: {e}\\n\\n建议检查数据库连接或重新表述问题。\"\n", "    \n", "    def _handle_business_consultation(self, question: str, route_info: Dict) -> str:\n", "        \"\"\"处理业务咨询\"\"\"\n", "        try:\n", "            # 获取专家知识和对话上下文\n", "            expert_knowledge = self.long_memory.expert_knowledge.domain_knowledge\n", "            conversation_context = self._get_conversation_context()\n", "            \n", "            # 构建业务咨询提示\n", "            consultation_prompt = f\"\"\"作为共享充电宝行业专家，请回答以下业务问题：\n", "\n", "## 用户问题\n", "{question}\n", "\n", "## 对话上下文\n", "{conversation_context}\n", "\n", "## 专家知识库\n", "{expert_knowledge}\n", "\n", "## 回答要求\n", "1. 提供专业的业务分析和建议\n", "2. 结合行业最佳实践\n", "3. 给出具体可执行的行动方案\n", "4. 如果需要数据支持，说明需要哪些数据\n", "5. 保持回答的实用性和可操作性\n", "\n", "请用专业但易懂的语言回答。\"\"\"\n", "\n", "            # 调用AI生成回答\n", "            messages = self.short_memory.get_messages()\n", "            messages.append({\"role\": \"user\", \"content\": consultation_prompt})\n", "            \n", "            response = self.query_engine.client.chat.completions.create(\n", "                model=\"deepseek-chat\",\n", "                messages=messages,\n", "                temperature=0.7\n", "            )\n", "            \n", "            return response.choices[0].message.content\n", "            \n", "        except Exception as e:\n", "            return f\"业务咨询处理失败: {e}\\n\\n请重新表述您的问题或联系专业顾问。\"\n", "    \n", "    def _handle_general_conversation(self, question: str, route_info: Dict) -> str:\n", "        \"\"\"处理普通对话\"\"\"\n", "        try:\n", "            # 使用短期记忆进行对话\n", "            messages = self.short_memory.get_messages()\n", "            \n", "            response = self.query_engine.client.chat.completions.create(\n", "                model=\"deepseek-chat\",\n", "                messages=messages,\n", "                temperature=0.8\n", "            )\n", "            \n", "            return response.choices[0].message.content\n", "            \n", "        except Exception as e:\n", "            return f\"对话处理失败: {e}\\n\\n请稍后重试或重新开始对话。\"\n", "    \n", "    def _get_conversation_context(self) -> str:\n", "        \"\"\"获取对话上下文\"\"\"\n", "        context_parts = []\n", "        \n", "        if self.conversation_context[\"current_topic\"]:\n", "            context_parts.append(f\"当前话题: {self.conversation_context['current_topic']}\")\n", "        \n", "        if self.conversation_context[\"recent_analyses\"]:\n", "            context_parts.append(f\"最近分析: {', '.join(self.conversation_context['recent_analyses'][-3:])}\")\n", "        \n", "        context_parts.append(f\"对话轮次: {self.conversation_context['interaction_count']}\")\n", "        \n", "        return \" | \".join(context_parts) if context_parts else \"新对话\"\n", "    \n", "    def _update_conversation_context(self, user_input: str, response: str, route_info: Dict):\n", "        \"\"\"更新对话上下文\"\"\"\n", "        # 更新当前话题\n", "        if route_info[\"type\"] == \"database_query\":\n", "            if \"品牌\" in user_input:\n", "                self.conversation_context[\"current_topic\"] = \"品牌分析\"\n", "            elif \"用户\" in user_input:\n", "                self.conversation_context[\"current_topic\"] = \"用户分析\"\n", "            elif \"地区\" in user_input:\n", "                self.conversation_context[\"current_topic\"] = \"地区分析\"\n", "            elif \"时间\" in user_input:\n", "                self.conversation_context[\"current_topic\"] = \"时间分析\"\n", "        \n", "        # 记录最近的分析\n", "        if route_info[\"type\"] in [\"database_query\", \"business_consultation\"]:\n", "            self.conversation_context[\"recent_analyses\"].append(user_input[:20] + \"...\")\n", "            # 只保留最近5次分析\n", "            if len(self.conversation_context[\"recent_analyses\"]) > 5:\n", "                self.conversation_context[\"recent_analyses\"].pop(0)\n", "    \n", "    def get_system_status(self) -> Dict:\n", "        \"\"\"获取系统状态 - 增强版\"\"\"\n", "        # 更新运行时状态\n", "        current_time = datetime.now()\n", "        uptime = (current_time - self.start_time).total_seconds()\n", "        \n", "        # 获取表结构详细信息\n", "        table_details = {}\n", "        if hasattr(self.query_engine, 'table_schemas'):\n", "            for table_name, schema in self.query_engine.table_schemas.items():\n", "                table_details[table_name] = {\n", "                    \"type\": schema.get('table_type', '未知'),\n", "                    \"row_count\": schema.get('row_count', 0),\n", "                    \"columns\": len(schema.get('columns', [])),\n", "                    \"business_purpose\": schema.get('business_purpose', '未定义')\n", "                }\n", "        \n", "        return {\n", "            \"session_info\": {\n", "                \"session_id\": self.session_id,\n", "                \"uptime_seconds\": uptime,\n", "                \"uptime_formatted\": f\"{int(uptime//3600)}h {int((uptime%3600)//60)}m {int(uptime%60)}s\"\n", "            },\n", "            \"modules_status\": {\n", "                \"loaded_modules\": self.system_status[\"modules_loaded\"],\n", "                \"module_count\": len(self.system_status[\"modules_loaded\"])\n", "            },\n", "            \"database_status\": {\n", "                \"table_structure_status\": self.system_status[\"table_structure_status\"],\n", "                \"total_tables\": len(self.query_engine.table_schemas) if hasattr(self.query_engine, 'table_schemas') else 0,\n", "                \"expected_tables\": 9,\n", "                \"table_details\": table_details\n", "            },\n", "            \"conversation_status\": {\n", "                \"interaction_count\": self.conversation_context[\"interaction_count\"],\n", "                \"current_topic\": self.conversation_context[\"current_topic\"],\n", "                \"memory_usage\": len(self.short_memory.get_messages()) if hasattr(self.short_memory, 'get_messages') else 0\n", "            },\n", "            \"health_status\": self.system_status[\"health\"]\n", "        }\n", "    \n", "    def diagnose_table_structure(self) -> Dict:\n", "        \"\"\"诊断表结构问题\"\"\"\n", "        custom_print(\"🔍 开始诊断表结构...\")\n", "        \n", "        diagnosis = {\n", "            \"timestamp\": datetime.now().isoformat(),\n", "            \"expected_tables\": 9,\n", "            \"found_tables\": len(self.query_engine.table_schemas),\n", "            \"missing_tables\": [],\n", "            \"extra_tables\": [],\n", "            \"table_health\": {},\n", "            \"recommendations\": []\n", "        }\n", "        \n", "        # 检查缺失的表\n", "        expected_tables = self.query_engine.expected_tables\n", "        actual_tables = list(self.query_engine.table_schemas.keys())\n", "        \n", "        diagnosis[\"missing_tables\"] = [t for t in expected_tables if t not in actual_tables]\n", "        diagnosis[\"extra_tables\"] = [t for t in actual_tables if t not in expected_tables]\n", "        \n", "        # 检查每个表的健康状态\n", "        for table_name, schema in self.query_engine.table_schemas.items():\n", "            health_score = 100\n", "            issues = []\n", "            \n", "            # 检查行数\n", "            row_count = schema.get('row_count', 0)\n", "            if row_count == 0:\n", "                health_score -= 50\n", "                issues.append(\"表为空\")\n", "            elif row_count < 10:\n", "                health_score -= 20\n", "                issues.append(\"数据量过少\")\n", "            \n", "            # 检查字段数量\n", "            column_count = len(schema.get('columns', []))\n", "            if column_count < 3:\n", "                health_score -= 30\n", "                issues.append(\"字段数量过少\")\n", "            \n", "            diagnosis[\"table_health\"][table_name] = {\n", "                \"health_score\": max(0, health_score),\n", "                \"row_count\": row_count,\n", "                \"column_count\": column_count,\n", "                \"issues\": issues,\n", "                \"table_type\": schema.get('table_type', '未知')\n", "            }\n", "        \n", "        # 生成建议\n", "        if diagnosis[\"missing_tables\"]:\n", "            diagnosis[\"recommendations\"].append(\"运行自动修复功能创建缺失的汇总表\")\n", "        \n", "        if any(info[\"health_score\"] < 80 for info in diagnosis[\"table_health\"].values()):\n", "            diagnosis[\"recommendations\"].append(\"检查数据质量，确保表中有足够的数据\")\n", "        \n", "        if diagnosis[\"found_tables\"] != diagnosis[\"expected_tables\"]:\n", "            diagnosis[\"recommendations\"].append(\"验证数据库schema和表结构完整性\")\n", "        \n", "        custom_print(f\"📊 诊断完成: {diagnosis['found_tables']}/{diagnosis['expected_tables']} 表结构\")\n", "        \n", "        return diagnosis\n", "    \n", "    def auto_repair_table_structure(self) -> Dict:\n", "        \"\"\"自动修复表结构\"\"\"\n", "        custom_print(\"🔧 开始自动修复表结构...\")\n", "        \n", "        repair_result = {\n", "            \"timestamp\": datetime.now().isoformat(),\n", "            \"repair_attempted\": <PERSON><PERSON><PERSON>,\n", "            \"tables_created\": [],\n", "            \"repair_success\": <PERSON><PERSON><PERSON>,\n", "            \"before_count\": len(self.query_engine.table_schemas),\n", "            \"after_count\": 0,\n", "            \"error_messages\": []\n", "        }\n", "        \n", "        try:\n", "            # 尝试创建缺失的汇总表\n", "            repair_result[\"repair_attempted\"] = True\n", "            success = self.query_engine.check_and_create_missing_tables()\n", "            \n", "            if success:\n", "                # 重新验证结构\n", "                verification = self.query_engine.verify_nine_table_structure()\n", "                repair_result[\"after_count\"] = verification[\"total_found\"]\n", "                repair_result[\"repair_success\"] = verification[\"structure_complete\"]\n", "                \n", "                if verification[\"structure_complete\"]:\n", "                    self.system_status[\"table_structure_status\"] = \"完整(9/9)\"\n", "                    custom_print(\"✅ 表结构修复成功！\")\n", "                else:\n", "                    self.system_status[\"table_structure_status\"] = f\"部分修复({verification['total_found']}/9)\"\n", "                    custom_print(f\"⚠️ 部分修复成功: {verification['total_found']}/9\")\n", "            else:\n", "                repair_result[\"error_messages\"].append(\"自动创建汇总表失败\")\n", "                \n", "        except Exception as e:\n", "            repair_result[\"error_messages\"].append(str(e))\n", "            custom_print(f\"❌ 修复过程出错: {e}\")\n", "        \n", "        return repair_result\n", "\n", "    def reset_conversation(self):\n", "        \"\"\"重置对话\"\"\"\n", "        # 清空短期记忆（保留系统消息）\n", "        system_messages = [msg for msg in self.short_memory.get_messages() if msg.get(\"role\") == \"system\"]\n", "        self.short_memory.messages = system_messages\n", "        \n", "        # 重置对话上下文\n", "        self.conversation_context = {\n", "            \"current_topic\": None,\n", "            \"interaction_count\": 0,\n", "            \"user_preferences\": {},\n", "            \"recent_analyses\": []\n", "        }\n", "        \n", "        custom_print(\"🔄 对话已重置\")\n", "\n", "    def get_enhanced_conversation_summary(self) -> Dict:\n", "        \"\"\"获取增强对话摘要\"\"\"\n", "        system_status = self.get_system_status()\n", "        \n", "        return {\n", "            \"session_summary\": {\n", "                \"session_id\": self.session_id,\n", "                \"total_interactions\": self.conversation_context[\"interaction_count\"],\n", "                \"current_topic\": self.conversation_context[\"current_topic\"],\n", "                \"recent_analyses\": self.conversation_context[\"recent_analyses\"]\n", "            },\n", "            \"system_health\": system_status,\n", "            \"performance_metrics\": {\n", "                \"query_engine\": self.query_engine.get_performance_stats(),\n", "                \"memory_usage\": len(self.short_memory.get_messages())\n", "            }\n", "        }\n", "\n", "custom_print(\"✅ 融合系统: 智能数据分析助手 - 9表结构完整版 加载完成\")\n", "custom_print(\"🎯 新增功能: 9表结构自动检测和修复、智能路由、完整诊断\")"]}, {"cell_type": "code", "execution_count": null, "id": "bfebf128", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 🎮 模块4: 控制台交互界面系统 - 增强版\n", "# 作用: 提供彩色控制台界面，支持ESC键退出和快捷命令\n", "# ===================================================================\n", "\n", "import keyboard\n", "import threading\n", "import sys\n", "import os\n", "import time\n", "from datetime import datetime\n", "from typing import Dict, List, Any\n", "\n", "class PowerBankConsoleInterface:\n", "    \"\"\"\n", "    共享充电宝智能分析控制台界面 - 增强版\n", "    \n", "    功能特性:\n", "    - 🎨 彩色交互界面 - 丰富的视觉体验\n", "    - ⚡ ESC键快速退出 - 即时响应\n", "    - 🔄 实时加载动画 - 优雅的等待体验\n", "    - 📚 会话历史管理 - 完整的对话记录\n", "    - 🚀 快捷命令支持 - 高效的操作方式\n", "    - 🔧 系统诊断功能 - 深度健康检查\n", "    - 📊 增强状态显示 - 详细的系统信息\n", "    - 🎯 智能结果展示 - 结构化数据呈现\n", "    \"\"\"\n", "    \n", "    def __init__(self, assistant):\n", "        self.assistant = assistant\n", "        self.running = True\n", "        self.session_history = []\n", "        self.esc_pressed = False\n", "        self._stop_loading = False\n", "        \n", "        # 控制台样式配置 - 增强版\n", "        self.colors = {\n", "            'header': '\\033[95m',\n", "            'blue': '\\033[94m', \n", "            'cyan': '\\033[96m',\n", "            'green': '\\033[92m',\n", "            'yellow': '\\033[93m',\n", "            'red': '\\033[91m',\n", "            'bold': '\\033[1m',\n", "            'underline': '\\033[4m',\n", "            'dim': '\\033[2m',\n", "            'blink': '\\033[5m',\n", "            'reverse': '\\033[7m',\n", "            'end': '\\033[0m'\n", "        }\n", "        \n", "        # 界面配置\n", "        self.interface_config = {\n", "            'banner_width': 66,\n", "            'separator_char': '=',\n", "            'loading_speed': 0.1,\n", "            'auto_save': True,\n", "            'show_timestamps': True\n", "        }\n", "        \n", "        # 启动ESC键监听\n", "        self.setup_esc_listener()\n", "        \n", "        # 初始化会话统计\n", "        self.session_stats = {\n", "            'start_time': datetime.now(),\n", "            'total_queries': 0,\n", "            'successful_queries': 0,\n", "            'failed_queries': 0,\n", "            'commands_executed': 0\n", "        }\n", "    \n", "    def setup_esc_listener(self):\n", "        \"\"\"设置ESC键监听 - 增强版\"\"\"\n", "        def esc_listener():\n", "            try:\n", "                import keyboard\n", "                keyboard.wait('esc')\n", "                self.esc_pressed = True\n", "                self.running = False\n", "                print(f\"\\n{self.colors['yellow']}🔥 检测到ESC键，正在安全退出...{self.colors['end']}\")\n", "                self._graceful_shutdown()\n", "            except ImportError:\n", "                # keyboard模块未安装，跳过ESC监听\n", "                pass\n", "            except Exception as e:\n", "                print(f\"ESC监听器错误: {e}\")\n", "        \n", "        try:\n", "            import keyboard\n", "            esc_thread = threading.Thread(target=esc_listener, daemon=True)\n", "            esc_thread.start()\n", "            self.print_colored(\"✅ ESC键快速退出功能已启用\", 'green')\n", "        except ImportError:\n", "            self.print_colored(\"⚠️  keyboard模块未安装，ESC功能不可用\", 'yellow')\n", "            self.print_colored(\"💡 运行: pip install keyboard 启用ESC功能\", 'dim')\n", "    \n", "    def _graceful_shutdown(self):\n", "        \"\"\"优雅关闭\"\"\"\n", "        self.print_colored(\"\\n🛑 正在执行优雅关闭...\", 'yellow')\n", "        self.cleanup_and_exit()\n", "        os._exit(0)\n", "    \n", "    def print_colored(self, text: str, color: str = 'end'):\n", "        \"\"\"打印彩色文本 - 增强版\"\"\"\n", "        timestamp = f\"[{datetime.now().strftime('%H:%M:%S')}] \" if self.interface_config['show_timestamps'] else \"\"\n", "        print(f\"{timestamp}{self.colors.get(color, '')}{text}{self.colors['end']}\")\n", "    \n", "    def print_banner(self):\n", "        \"\"\"打印系统横幅 - 增强版\"\"\"\n", "        # 获取系统状态信息\n", "        try:\n", "            system_status = self.assistant.get_system_status()\n", "            table_status = system_status['database_status']['table_structure_status']\n", "            module_count = system_status['modules_status']['module_count']\n", "        except:\n", "            table_status = \"未知\"\n", "            module_count = \"未知\"\n", "        \n", "        banner = f\"\"\"\n", "{self.colors['cyan']}╔══════════════════════════════════════════════════════════════════╗\n", "║                🚀 共享充电宝智能数据分析系统 v2.0                  ║\n", "║                     PowerBank AI Assistant                        ║\n", "╠══════════════════════════════════════════════════════════════════╣\n", "║  🎯 功能: 智能问答 | 数据分析 | 业务洞察 | 批量处理 | 系统诊断     ║\n", "║  ⚡ 快捷: /help /demo /batch /status /diagnose /repair /quit     ║\n", "║  🔥 退出: 按 ESC 键或输入 /quit 立即退出                         ║\n", "║  📊 状态: 表结构({table_status}) | 模块({module_count}个)           ║\n", "╚══════════════════════════════════════════════════════════════════╝{self.colors['end']}\n", "        \"\"\"\n", "        print(banner)\n", "        \n", "        # 显示启动信息\n", "        self.print_colored(\"🎉 系统启动完成！欢迎使用智能数据分析助手\", 'green')\n", "        self.print_colored(\"💡 输入 /help 查看完整命令列表\", 'cyan')\n", "        self.print_colored(\"🚀 输入 /demo 体验系统功能演示\", 'cyan')\n", "    \n", "    def safe_input(self, prompt: str) -> str:\n", "        \"\"\"安全的输入函数 - 增强版\"\"\"\n", "        try:\n", "            if self.esc_pressed:\n", "                return \"/quit\"\n", "            \n", "            # 添加输入提示样式\n", "            styled_prompt = f\"{prompt}\"\n", "            return input(styled_prompt)\n", "        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EOFError):\n", "            self.print_colored(\"\\n🔥 检测到中断信号，正在退出...\", 'yellow')\n", "            return \"/quit\"\n", "        except Exception as e:\n", "            self.print_colored(f\"输入错误: {e}\", 'red')\n", "            return \"\"\n", "    \n", "    def _show_loading(self):\n", "        \"\"\"显示加载动画 - 增强版\"\"\"\n", "        loading_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']\n", "        loading_messages = [\n", "            \"正在分析问题...\",\n", "            \"生成SQL查询...\", \n", "            \"执行数据查询...\",\n", "            \"生成业务洞察...\",\n", "            \"整理分析结果...\"\n", "        ]\n", "        \n", "        i = 0\n", "        message_index = 0\n", "        while not self._stop_loading:\n", "            current_message = loading_messages[message_index % len(loading_messages)]\n", "            print(f\"\\r{self.colors['yellow']}{loading_chars[i % len(loading_chars)]} {current_message}{self.colors['end']}\", \n", "                  end='', flush=True)\n", "            time.sleep(self.interface_config['loading_speed'])\n", "            i += 1\n", "            if i % 10 == 0:  # 每10次循环切换消息\n", "                message_index += 1\n", "        \n", "        # 清除加载信息\n", "        print(\"\\r\" + \" \" * 50 + \"\\r\", end='', flush=True)\n", "    \n", "    def display_query_result(self, result: str, question: str = \"\", query_type: str = \"\"):\n", "        \"\"\"显示查询结果 - 增强版\"\"\"\n", "        separator = self.colors['green'] + '=' * self.interface_config['banner_width'] + self.colors['end']\n", "        \n", "        print(f\"\\n{separator}\")\n", "        print(f\"{self.colors['bold']}{self.colors['cyan']}📋 查询结果{self.colors['end']}\")\n", "        print(f\"{separator}\")\n", "        \n", "        if question:\n", "            print(f\"\\n{self.colors['cyan']}🤔 问题: {self.colors['end']}{question}\")\n", "        \n", "        if query_type:\n", "            print(f\"{self.colors['yellow']}📊 类型: {self.colors['end']}{query_type}\")\n", "        \n", "        print(f\"\\n{self.colors['green']}🤖 回答:{self.colors['end']}\")\n", "        print(f\"{self.colors['dim']}{'─' * 50}{self.colors['end']}\")\n", "        \n", "        # 智能格式化结果\n", "        formatted_result = self._format_result_content(result)\n", "        print(formatted_result)\n", "        \n", "        print(f\"{self.colors['dim']}{'─' * 50}{self.colors['end']}\")\n", "        print(f\"{separator}\")\n", "        \n", "        # 更新统计\n", "        self.session_stats['successful_queries'] += 1\n", "    \n", "    def _format_result_content(self, content: str) -> str:\n", "        \"\"\"智能格式化结果内容\"\"\"\n", "        if not content:\n", "            return f\"{self.colors['yellow']}暂无结果{self.colors['end']}\"\n", "        \n", "        # 检测并高亮关键信息\n", "        lines = content.split('\\n')\n", "        formatted_lines = []\n", "        \n", "        for line in lines:\n", "            # 高亮标题行（以##开头）\n", "            if line.strip().startswith('##'):\n", "                formatted_lines.append(f\"{self.colors['bold']}{self.colors['cyan']}{line}{self.colors['end']}\")\n", "            # 高亮重要数据（包含数字和%的行）\n", "            elif any(char.isdigit() for char in line) and ('%' in line or '元' in line or '个' in line):\n", "                formatted_lines.append(f\"{self.colors['green']}{line}{self.colors['end']}\")\n", "            # 高亮建议行（包含\"建议\"、\"推荐\"等）\n", "            elif any(keyword in line for keyword in ['建议', '推荐', '应该', '可以']):\n", "                formatted_lines.append(f\"{self.colors['yellow']}{line}{self.colors['end']}\")\n", "            else:\n", "                formatted_lines.append(line)\n", "        \n", "        return '\\n'.join(formatted_lines)\n", "    \n", "    def process_command(self, command: str) -> bool:\n", "        \"\"\"处理快捷命令 - 增强版\"\"\"\n", "        cmd = command.lower().strip()\n", "        self.session_stats['commands_executed'] += 1\n", "        \n", "        command_map = {\n", "            '/quit': self._cmd_quit,\n", "            '/exit': self._cmd_quit,\n", "            '/help': self._cmd_help,\n", "            '/demo': self._cmd_demo,\n", "            '/status': self._cmd_status,\n", "            '/history': self._cmd_history,\n", "            '/batch': self._cmd_batch,\n", "            '/export': self._cmd_export,\n", "            '/clear': self._cmd_clear,\n", "            '/diagnose': self._cmd_diagnose,\n", "            '/repair': self._cmd_repair,\n", "            '/reset': self._cmd_reset,\n", "            '/stats': self._cmd_stats\n", "        }\n", "        \n", "        if cmd in command_map:\n", "            return command_map[cmd]()\n", "        else:\n", "            self.print_colored(f\"❌ 未知命令: {command}\", 'red')\n", "            self.print_colored(\"💡 输入 /help 查看可用命令\", 'yellow')\n", "            return True\n", "    \n", "    def _cmd_quit(self) -> bool:\n", "        \"\"\"退出命令\"\"\"\n", "        return False\n", "    \n", "    def _cmd_help(self) -> bool:\n", "        \"\"\"显示帮助信息 - 增强版\"\"\"\n", "        help_text = f\"\"\"\n", "{self.colors['cyan']}📖 可用命令列表:{self.colors['end']}\n", "\n", "{self.colors['bold']}基础命令:{self.colors['end']}\n", "{self.colors['green']}/help{self.colors['end']}      - 显示此帮助信息\n", "{self.colors['green']}/demo{self.colors['end']}      - 运行功能演示\n", "{self.colors['green']}/clear{self.colors['end']}     - 清屏并重新显示横幅\n", "{self.colors['green']}/quit{self.colors['end']}      - 退出系统\n", "\n", "{self.colors['bold']}分析命令:{self.colors['end']}\n", "{self.colors['green']}/batch{self.colors['end']}     - 批量分析模式\n", "{self.colors['green']}/history{self.colors['end']}   - 查看会话历史\n", "{self.colors['green']}/export{self.colors['end']}    - 导出分析报告\n", "\n", "{self.colors['bold']}系统命令:{self.colors['end']}\n", "{self.colors['green']}/status{self.colors['end']}    - 查看系统状态\n", "{self.colors['green']}/diagnose{self.colors['end']}  - 系统诊断检查\n", "{self.colors['green']}/repair{self.colors['end']}    - 自动修复系统\n", "{self.colors['green']}/reset{self.colors['end']}     - 重置对话上下文\n", "{self.colors['green']}/stats{self.colors['end']}     - 查看会话统计\n", "\n", "{self.colors['yellow']}💡 示例问题:{self.colors['end']}\n", "• \"哪个品牌的充电宝销量最好？\"\n", "• \"用户消费行为有什么特点？\"\n", "• \"各地区的使用情况如何？\"\n", "• \"给我一些运营建议\"\n", "• \"今天天气怎么样？\"\n", "\n", "{self.colors['cyan']}🔥 快捷键:{self.colors['end']}\n", "• 按 {self.colors['bold']}ESC{self.colors['end']} 键随时快速退出\n", "• 按 {self.colors['bold']}Ctrl+C{self.colors['end']} 中断当前操作\n", "        \"\"\"\n", "        print(help_text)\n", "        return True\n", "    \n", "    def _cmd_demo(self) -> bool:\n", "        \"\"\"运行演示 - 增强版\"\"\"\n", "        demo_questions = [\n", "            (\"哪个品牌的充电宝销量最好？\", \"品牌分析\"),\n", "            (\"用户的平均消费金额是多少？\", \"用户分析\"),\n", "            (\"各地区的收入排名如何？\", \"地区分析\"),\n", "            (\"给我一些提升用户留存的建议\", \"业务咨询\"),\n", "            (\"今天是星期几？\", \"普通对话\")\n", "        ]\n", "        \n", "        self.print_colored(\"🎬 开始功能演示...\", 'yellow')\n", "        self.print_colored(f\"📝 将演示 {len(demo_questions)} 个不同类型的问题\", 'cyan')\n", "        \n", "        for i, (question, category) in enumerate(demo_questions, 1):\n", "            self.print_colored(f\"\\n{'='*50}\", 'dim')\n", "            self.print_colored(f\"📝 演示 {i}/{len(demo_questions)} - {category}\", 'cyan')\n", "            self.print_colored(f\"问题: {question}\", 'blue')\n", "            \n", "            try:\n", "                # 显示加载动画\n", "                self._stop_loading = False\n", "                loading_thread = threading.Thread(target=self._show_loading)\n", "                loading_thread.daemon = True\n", "                loading_thread.start()\n", "                \n", "                # 执行查询\n", "                result = self.assistant.intelligent_chat(question)\n", "                \n", "                # 停止加载动画\n", "                self._stop_loading = True\n", "                time.sleep(0.2)\n", "                \n", "                # 显示结果\n", "                self.display_query_result(result, question, category)\n", "                \n", "                # 保存到历史\n", "                self.session_history.append({\n", "                    'question': question,\n", "                    'answer': result,\n", "                    'type': category,\n", "                    'timestamp': datetime.now().isoformat()\n", "                })\n", "                \n", "                if i < len(demo_questions):\n", "                    self.print_colored(\"⏳ 3秒后继续下一个演示...\", 'yellow')\n", "                    time.sleep(3)\n", "                    \n", "            except Exception as e:\n", "                self._stop_loading = True\n", "                self.print_colored(f\"❌ 演示出错: {e}\", 'red')\n", "                self.session_stats['failed_queries'] += 1\n", "        \n", "        self.print_colored(\"\\n🎉 功能演示完成！\", 'green')\n", "        self.print_colored(\"💡 您可以开始提问或使用其他命令\", 'cyan')\n", "        return True\n", "    \n", "    def _cmd_status(self) -> bool:\n", "        \"\"\"显示系统状态 - 增强版\"\"\"\n", "        try:\n", "            system_status = self.assistant.get_system_status()\n", "            \n", "            print(f\"\\n{self.colors['cyan']}📊 系统状态详情{self.colors['end']}\")\n", "            print(f\"{self.colors['green']}{'='*60}{self.colors['end']}\")\n", "            \n", "            # 会话信息\n", "            session_info = system_status['session_info']\n", "            print(f\"{self.colors['bold']}📱 会话信息:{self.colors['end']}\")\n", "            print(f\"  🆔 会话ID: {session_info['session_id']}\")\n", "            print(f\"  ⏰ 运行时间: {session_info['uptime_formatted']}\")\n", "            print(f\"  💬 本次对话: {len(self.session_history)} 条\")\n", "            \n", "            # 模块状态\n", "            modules_status = system_status['modules_status']\n", "            print(f\"\\n{self.colors['bold']}🔧 模块状态:{self.colors['end']}\")\n", "            print(f\"  ✅ 已加载模块: {modules_status['module_count']} 个\")\n", "            for module in modules_status['loaded_modules']:\n", "                print(f\"    • {module}\")\n", "            \n", "            # 数据库状态\n", "            db_status = system_status['database_status']\n", "            print(f\"\\n{self.colors['bold']}🗄️ 数据库状态:{self.colors['end']}\")\n", "            print(f\"  📊 表结构: {db_status['table_structure_status']}\")\n", "            print(f\"  📋 总表数: {db_status['total_tables']}/{db_status['expected_tables']}\")\n", "            \n", "            # 对话状态\n", "            conv_status = system_status['conversation_status']\n", "            print(f\"\\n{self.colors['bold']}💭 对话状态:{self.colors['end']}\")\n", "            print(f\"  🔄 交互次数: {conv_status['interaction_count']}\")\n", "            print(f\"  🎯 当前话题: {conv_status['current_topic'] or '无'}\")\n", "            print(f\"  🧠 记忆使用: {conv_status['memory_usage']} 条消息\")\n", "            \n", "            # 健康状态\n", "            health = system_status['health_status']\n", "            print(f\"\\n{self.colors['bold']}❤️ 健康状态:{self.colors['end']}\")\n", "            for key, value in health.items():\n", "                status_color = 'green' if '正常' in value or '已连接' in value else 'yellow'\n", "                print(f\"  • {key}: {self.colors[status_color]}{value}{self.colors['end']}\")\n", "            \n", "        except Exception as e:\n", "            self.print_colored(f\"❌ 获取状态失败: {e}\", 'red')\n", "        \n", "        return True\n", "    \n", "    def _cmd_diagnose(self) -> bool:\n", "        \"\"\"系统诊断 - 新增功能\"\"\"\n", "        self.print_colored(\"🔍 开始系统诊断检查...\", 'yellow')\n", "        \n", "        try:\n", "            diagnosis = self.assistant.diagnose_table_structure()\n", "            \n", "            print(f\"\\n{self.colors['cyan']}🏥 系统诊断报告{self.colors['end']}\")\n", "            print(f\"{self.colors['green']}{'='*50}{self.colors['end']}\")\n", "            \n", "            # 基本信息\n", "            print(f\"{self.colors['bold']}📊 基本信息:{self.colors['end']}\")\n", "            print(f\"  🕐 诊断时间: {diagnosis['timestamp']}\")\n", "            print(f\"  📋 期望表数: {diagnosis['expected_tables']}\")\n", "            print(f\"  ✅ 发现表数: {diagnosis['found_tables']}\")\n", "            \n", "            # 缺失表\n", "            if diagnosis['missing_tables']:\n", "                print(f\"\\n{self.colors['bold']}{self.colors['red']}❌ 缺失的表:{self.colors['end']}\")\n", "                for table in diagnosis['missing_tables']:\n", "                    print(f\"    • {table}\")\n", "            else:\n", "                print(f\"\\n{self.colors['green']}✅ 所有期望的表都存在{self.colors['end']}\")\n", "            \n", "            # 额外表\n", "            if diagnosis['extra_tables']:\n", "                print(f\"\\n{self.colors['bold']}{self.colors['yellow']}ℹ️ 额外的表:{self.colors['end']}\")\n", "                for table in diagnosis['extra_tables']:\n", "                    print(f\"    • {table}\")\n", "            \n", "            # 表健康状态\n", "            print(f\"\\n{self.colors['bold']}❤️ 表健康状态:{self.colors['end']}\")\n", "            for table_name, health in diagnosis['table_health'].items():\n", "                score = health['health_score']\n", "                if score >= 90:\n", "                    status_color = 'green'\n", "                    status_icon = '✅'\n", "                elif score >= 70:\n", "                    status_color = 'yellow'\n", "                    status_icon = '⚠️'\n", "                else:\n", "                    status_color = 'red'\n", "                    status_icon = '❌'\n", "                \n", "                print(f\"  {status_icon} {table_name}: {self.colors[status_color]}{score}分{self.colors['end']} \"\n", "                      f\"({health['row_count']}行, {health['column_count']}列)\")\n", "                \n", "                if health['issues']:\n", "                    for issue in health['issues']:\n", "                        print(f\"      • {self.colors['red']}{issue}{self.colors['end']}\")\n", "            \n", "            # 建议\n", "            if diagnosis['recommendations']:\n", "                print(f\"\\n{self.colors['bold']}💡 建议:{self.colors['end']}\")\n", "                for i, rec in enumerate(diagnosis['recommendations'], 1):\n", "                    print(f\"  {i}. {rec}\")\n", "            \n", "        except Exception as e:\n", "            self.print_colored(f\"❌ 诊断失败: {e}\", 'red')\n", "        \n", "        return True\n", "    \n", "    def _cmd_repair(self) -> bool:\n", "        \"\"\"自动修复系统 - 新增功能\"\"\"\n", "        self.print_colored(\"🔧 开始自动修复系统...\", 'yellow')\n", "        \n", "        try:\n", "            repair_result = self.assistant.auto_repair_table_structure()\n", "            \n", "            print(f\"\\n{self.colors['cyan']}🛠️ 修复结果报告{self.colors['end']}\")\n", "            print(f\"{self.colors['green']}{'='*50}{self.colors['end']}\")\n", "            \n", "            print(f\"🕐 修复时间: {repair_result['timestamp']}\")\n", "            print(f\"🔄 是否尝试修复: {'是' if repair_result['repair_attempted'] else '否'}\")\n", "            print(f\"📊 修复前表数: {repair_result['before_count']}\")\n", "            print(f\"📈 修复后表数: {repair_result['after_count']}\")\n", "            \n", "            if repair_result['repair_success']:\n", "                self.print_colored(\"✅ 修复成功！系统表结构已完整\", 'green')\n", "            elif repair_result['repair_attempted']:\n", "                self.print_colored(\"⚠️ 部分修复成功，仍有问题需要手动处理\", 'yellow')\n", "            else:\n", "                self.print_colored(\"❌ 修复失败或未执行修复\", 'red')\n", "            \n", "            if repair_result['tables_created']:\n", "                print(f\"\\n{self.colors['bold']}🆕 创建的表:{self.colors['end']}\")\n", "                for table in repair_result['tables_created']:\n", "                    print(f\"  • {table}\")\n", "            \n", "            if repair_result['error_messages']:\n", "                print(f\"\\n{self.colors['bold']}{self.colors['red']}❌ 错误信息:{self.colors['end']}\")\n", "                for error in repair_result['error_messages']:\n", "                    print(f\"  • {error}\")\n", "            \n", "        except Exception as e:\n", "            self.print_colored(f\"❌ 修复过程失败: {e}\", 'red')\n", "        \n", "        return True\n", "    \n", "    def _cmd_reset(self) -> bool:\n", "        \"\"\"重置对话 - 新增功能\"\"\"\n", "        self.print_colored(\"🔄 正在重置对话上下文...\", 'yellow')\n", "        \n", "        try:\n", "            self.assistant.reset_conversation()\n", "            self.session_history.clear()\n", "            self.session_stats['total_queries'] = 0\n", "            self.session_stats['successful_queries'] = 0\n", "            self.session_stats['failed_queries'] = 0\n", "            \n", "            self.print_colored(\"✅ 对话上下文已重置\", 'green')\n", "            self.print_colored(\"💡 您可以开始新的对话\", 'cyan')\n", "            \n", "        except Exception as e:\n", "            self.print_colored(f\"❌ 重置失败: {e}\", 'red')\n", "        \n", "        return True\n", "    \n", "    def _cmd_stats(self) -> bool:\n", "        \"\"\"查看会话统计 - 新增功能\"\"\"\n", "        uptime = datetime.now() - self.session_stats['start_time']\n", "        \n", "        print(f\"\\n{self.colors['cyan']}📈 会话统计信息{self.colors['end']}\")\n", "        print(f\"{self.colors['green']}{'='*40}{self.colors['end']}\")\n", "        \n", "        print(f\"🕐 会话开始: {self.session_stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}\")\n", "        print(f\"⏱️ 运行时长: {str(uptime).split('.')[0]}\")\n", "        print(f\"💬 总查询数: {self.session_stats['total_queries']}\")\n", "        print(f\"✅ 成功查询: {self.session_stats['successful_queries']}\")\n", "        print(f\"❌ 失败查询: {self.session_stats['failed_queries']}\")\n", "        print(f\"⚡ 命令执行: {self.session_stats['commands_executed']}\")\n", "        \n", "        if self.session_stats['total_queries'] > 0:\n", "            success_rate = (self.session_stats['successful_queries'] / self.session_stats['total_queries']) * 100\n", "            print(f\"📊 成功率: {success_rate:.1f}%\")\n", "        \n", "        return True\n", "    \n", "    def _cmd_history(self) -> bool:\n", "        \"\"\"显示会话历史 - 增强版\"\"\"\n", "        if not self.session_history:\n", "            self.print_colored(\"📝 暂无会话历史\", 'yellow')\n", "            return True\n", "        \n", "        print(f\"\\n{self.colors['cyan']}📚 会话历史 (最近10条){self.colors['end']}\")\n", "        print(f\"{self.colors['green']}{'='*60}{self.colors['end']}\")\n", "        \n", "        recent_history = self.session_history[-10:]\n", "        for i, item in enumerate(recent_history, 1):\n", "            timestamp = item.get('timestamp', '')\n", "            if timestamp:\n", "                time_str = datetime.fromisoformat(timestamp).strftime('%H:%M:%S')\n", "            else:\n", "                time_str = '未知时间'\n", "            \n", "            question = item.get('question', 'N/A')\n", "            question_preview = question[:40] + \"...\" if len(question) > 40 else question\n", "            \n", "            print(f\"{i:2d}. [{time_str}] {question_preview}\")\n", "            print(f\"    类型: {item.get('type', 'N/A')}\")\n", "            print()\n", "        \n", "        return True\n", "    \n", "    def _cmd_batch(self) -> bool:\n", "        \"\"\"批量分析模式 - 增强版\"\"\"\n", "        self.print_colored(\"📊 进入批量分析模式\", 'cyan')\n", "        self.print_colored(\"💡 输入问题，每行一个，输入 'END' 结束\", 'yellow')\n", "        self.print_colored(\"💡 输入 'CANCEL' 取消批量分析\", 'dim')\n", "        \n", "        questions = []\n", "        while True:\n", "            try:\n", "                question = input(f\"{self.colors['green']}问题 {len(questions)+1}: {self.colors['end']}\")\n", "                \n", "                if question.upper() == 'END':\n", "                    break\n", "                elif question.upper() == 'CANCEL':\n", "                    self.print_colored(\"🚫 批量分析已取消\", 'yellow')\n", "                    return True\n", "                elif question.strip():\n", "                    questions.append(question.strip())\n", "                    \n", "            except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EOFError):\n", "                self.print_colored(\"\\n🚫 批量分析已中断\", 'yellow')\n", "                return True\n", "        \n", "        if not questions:\n", "            self.print_colored(\"📝 没有输入任何问题\", 'yellow')\n", "            return True\n", "        \n", "        self.print_colored(f\"🔄 开始批量分析 {len(questions)} 个问题...\", 'yellow')\n", "        \n", "        successful = 0\n", "        failed = 0\n", "        \n", "        for i, question in enumerate(questions, 1):\n", "            self.print_colored(f\"\\n{'='*50}\", 'dim')\n", "            self.print_colored(f\"📝 处理问题 {i}/{len(questions)}\", 'cyan')\n", "            \n", "            try:\n", "                # 显示加载动画\n", "                self._stop_loading = False\n", "                loading_thread = threading.Thread(target=self._show_loading)\n", "                loading_thread.daemon = True\n", "                loading_thread.start()\n", "                \n", "                # 执行查询\n", "                result = self.assistant.intelligent_chat(question)\n", "                \n", "                # 停止加载动画\n", "                self._stop_loading = True\n", "                time.sleep(0.2)\n", "                \n", "                # 显示结果\n", "                self.display_query_result(result, question, \"批量分析\")\n", "                \n", "                # 保存到历史\n", "                self.session_history.append({\n", "                    'question': question,\n", "                    'answer': result,\n", "                    'type': '批量分析',\n", "                    'timestamp': datetime.now().isoformat()\n", "                })\n", "                \n", "                successful += 1\n", "                time.sleep(1)  # 避免请求过快\n", "                \n", "            except Exception as e:\n", "                self._stop_loading = True\n", "                self.print_colored(f\"❌ 处理失败: {e}\", 'red')\n", "                failed += 1\n", "        \n", "        # 批量分析总结\n", "        print(f\"\\n{self.colors['cyan']}📊 批量分析完成{self.colors['end']}\")\n", "        print(f\"{self.colors['green']}✅ 成功: {successful} 个{self.colors['end']}\")\n", "        if failed > 0:\n", "            print(f\"{self.colors['red']}❌ 失败: {failed} 个{self.colors['end']}\")\n", "        \n", "        return True\n", "    \n", "    def _cmd_export(self) -> bool:\n", "        \"\"\"导出报告 - 增强版\"\"\"\n", "        if not self.session_history:\n", "            self.print_colored(\"📝 暂无数据可导出\", 'yellow')\n", "            return True\n", "        \n", "        try:\n", "            timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            report_file = f\"powerbank_session_report_{timestamp}.md\"\n", "            \n", "            with open(report_file, 'w', encoding='utf-8') as f:\n", "                # 报告头部\n", "                f.write(\"# 共享充电宝智能分析会话报告\\n\\n\")\n", "                f.write(f\"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "                f.write(f\"**会话ID**: {self.assistant.session_id}\\n\")\n", "                f.write(f\"**总问题数**: {len(self.session_history)}\\n\\n\")\n", "                \n", "                # 统计信息\n", "                f.write(\"## 📊 会话统计\\n\\n\")\n", "                f.write(f\"- 总查询数: {self.session_stats['total_queries']}\\n\")\n", "                f.write(f\"- 成功查询: {self.session_stats['successful_queries']}\\n\")\n", "                f.write(f\"- 失败查询: {self.session_stats['failed_queries']}\\n\")\n", "                f.write(f\"- 命令执行: {self.session_stats['commands_executed']}\\n\\n\")\n", "                \n", "                # 问题分类统计\n", "                type_counts = {}\n", "                for item in self.session_history:\n", "                    item_type = item.get('type', '未分类')\n", "                    type_counts[item_type] = type_counts.get(item_type, 0) + 1\n", "                \n", "                f.write(\"## 📈 问题类型分布\\n\\n\")\n", "                for type_name, count in type_counts.items():\n", "                    f.write(f\"- {type_name}: {count} 个\\n\")\n", "                f.write(\"\\n\")\n", "                \n", "                # 详细对话记录\n", "                f.write(\"## 💬 详细对话记录\\n\\n\")\n", "                for i, item in enumerate(self.session_history, 1):\n", "                    f.write(f\"### 问题 {i}\\n\\n\")\n", "                    f.write(f\"**时间**: {item.get('timestamp', '未知')}\\n\")\n", "                    f.write(f\"**类型**: {item.get('type', 'N/A')}\\n\")\n", "                    f.write(f\"**问题**: {item.get('question', 'N/A')}\\n\\n\")\n", "                    f.write(f\"**回答**:\\n\")\n", "                    f.write(f\"```\\n{item.get('answer', 'N/A')}\\n```\\n\\n\")\n", "                    f.write(\"---\\n\\n\")\n", "            \n", "            self.print_colored(f\"📄 报告已导出: {report_file}\", 'green')\n", "            self.print_colored(f\"📊 包含 {len(self.session_history)} 条对话记录\", 'cyan')\n", "            \n", "        except Exception as e:\n", "            self.print_colored(f\"❌ 导出失败: {e}\", 'red')\n", "        \n", "        return True\n", "    \n", "    def _cmd_clear(self) -> bool:\n", "        \"\"\"清屏命令\"\"\"\n", "        os.system('cls' if os.name == 'nt' else 'clear')\n", "        self.print_banner()\n", "        return True\n", "    \n", "    def run(self):\n", "        \"\"\"运行控制台界面 - 增强版\"\"\"\n", "        try:\n", "            # 清屏并显示横幅\n", "            os.system('cls' if os.name == 'nt' else 'clear')\n", "            self.print_banner()\n", "            \n", "            # 主循环\n", "            while self.running and not self.esc_pressed:\n", "                try:\n", "                    user_input = self.safe_input(\n", "                        f\"\\n{self.colors['bold']}{self.colors['green']}🤖 PowerBank-AI > {self.colors['end']}\"\n", "                    ).strip()\n", "                    \n", "                    if self.esc_pressed or user_input == \"/quit\":\n", "                        break\n", "                    \n", "                    if not user_input:\n", "                        continue\n", "                    \n", "                    # 处理命令\n", "                    if user_input.startswith('/'):\n", "                        if not self.process_command(user_input):\n", "                            break\n", "                        continue\n", "                    \n", "                    # 处理智能查询\n", "                    self.print_colored(f\"🔄 正在分析您的问题: {user_input}\", 'yellow')\n", "                    self.session_stats['total_queries'] += 1\n", "                    \n", "                    # 显示加载动画\n", "                    self._stop_loading = False\n", "                    loading_thread = threading.Thread(target=self._show_loading)\n", "                    loading_thread.daemon = True\n", "                    loading_thread.start()\n", "                    \n", "                    # 执行查询\n", "                    result = self.assistant.intelligent_chat(user_input)\n", "                    \n", "                    # 停止加载动画\n", "                    self._stop_loading = True\n", "                    time.sleep(0.2)\n", "                    \n", "                    # 显示结果\n", "                    self.display_query_result(result, user_input)\n", "                    \n", "                    # 保存到会话历史\n", "                    self.session_history.append({\n", "                        'question': user_input,\n", "                        'answer': result,\n", "                        'type': '智能查询',\n", "                        'timestamp': datetime.now().isoformat()\n", "                    })\n", "                    \n", "                except KeyboardInterrupt:\n", "                    self.print_colored(\"\\n\\n🔥 检测到 Ctrl+C，正在安全退出...\", 'yellow')\n", "                    break\n", "                except Exception as e:\n", "                    self._stop_loading = True\n", "                    self.print_colored(f\"\\n❌ 处理请求时出错: {e}\", 'red')\n", "                    self.print_colored(\"💡 请检查网络连接或重试\", 'yellow')\n", "                    self.session_stats['failed_queries'] += 1\n", "        \n", "        except Exception as e:\n", "            self.print_colored(f\"❌ 系统运行出错: {e}\", 'red')\n", "        finally:\n", "            self.cleanup_and_exit()\n", "    \n", "    def cleanup_and_exit(self):\n", "        \"\"\"清理资源并退出 - 增强版\"\"\"\n", "        self.print_colored(\"\\n🧹 正在清理系统资源...\", 'yellow')\n", "        \n", "        # 显示会话统计\n", "        uptime = datetime.now() - self.session_stats['start_time']\n", "        self.print_colored(f\"📊 本次会话统计:\", 'cyan')\n", "        self.print_colored(f\"  ⏱️ 运行时长: {str(uptime).split('.')[0]}\", 'dim')\n", "        self.print_colored(f\"  💬 总查询: {self.session_stats['total_queries']} 次\", 'dim')\n", "        self.print_colored(f\"  ✅ 成功: {self.session_stats['successful_queries']} 次\", 'dim')\n", "        \n", "        # 自动保存会话历史\n", "        if self.session_history and self.interface_config['auto_save']:\n", "            try:\n", "                timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "                auto_save_file = f\"auto_save_session_{timestamp}.md\"\n", "                \n", "                with open(auto_save_file, 'w', encoding='utf-8') as f:\n", "                    f.write(\"# 自动保存的会话记录\\n\\n\")\n", "                    f.write(f\"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n\")\n", "                    \n", "                    for i, item in enumerate(self.session_history, 1):\n", "                        f.write(f\"## 问题 {i}\\n\")\n", "                        f.write(f\"**问题**: {item.get('question', 'N/A')}\\n\\n\")\n", "                        f.write(f\"**回答**: {item.get('answer', 'N/A')}\\n\\n\")\n", "                        f.write(\"---\\n\\n\")\n", "                \n", "                self.print_colored(f\"💾 会话已自动保存: {auto_save_file}\", 'green')\n", "            except Exception as e:\n", "                self.print_colored(f\"⚠️ 自动保存失败: {e}\", 'yellow')\n", "        \n", "        # 感谢信息\n", "        self.print_colored(\"🎉 感谢使用共享充电宝智能数据分析系统！\", 'green')\n", "        self.print_colored(\"💡 期待您的下次使用\", 'cyan')\n", "        \n", "        self.running = False\n", "\n", "custom_print(\"✅ 模块4: 控制台交互界面系统 - 增强版 加载完成\")\n", "custom_print(\"🎯 新增功能: 系统诊断、自动修复、会话统计、智能格式化\")\n", "custom_print(\"🚀 增强特性: 更丰富的命令、更好的用户体验、完整的错误处理\")"]}, {"cell_type": "code", "execution_count": 1, "id": "4260d641", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "╔══════════════════════════════════════════════════════════════════╗\n", "║            🚀 共享充电宝智能数据分析系统 v2.0 启动器              ║\n", "║                    PowerBank AI System Launcher                   ║\n", "╠══════════════════════════════════════════════════════════════════╣\n", "║  🧠 智能记忆管理 | 📚 专家知识库 | 🔍 智能查询引擎 | 🎮 控制台界面  ║\n", "║  ⚡ 一键启动 | 🔧 自动诊断 | 💾 配置管理 | 🛡️ 错误恢复           ║\n", "╚══════════════════════════════════════════════════════════════════╝\n", "    \n", "🕐 启动时间: 2025-07-22 17:45:04\n", "======================================================================\n", "🔍 正在检查运行环境...\n", "  📦 检查Python版本...\n", "  ✅ Python版本: 3.12.11\n", "  📚 检查必需依赖...\n", "  ✅ pandas: 数据处理\n", "  ✅ pymysql: 数据库连接\n", "  ✅ openai: AI模型接口\n", "  ✅ httpx: HTTP客户端\n", "  ✅ rich: 控制台美化\n", "  ✅ dashscope: Token计算\n", "  ✅ keyboard: ESC键监听(可选)\n", "  ✅ 环境检查通过\n", "⚙️ 正在检查系统配置...\n", "  ✅ 已加载现有配置文件\n", "  💾 配置已保存: C:\\Users\\<USER>\\powerbank_memory\\system_config.json\n", "🗄️ 正在测试数据库连接...\n", "  🔌 正在连接数据库...\n", "  📊 检查数据库状态...\n", "  ✅ MySQL版本: 8.0.40\n", "  📋 发现表数量: 9\n", "  ✅ 关键表结构完整\n", "  ✅ 数据库连接测试通过\n", "🌐 正在测试API连接...\n", "  🌐 正在测试API连接...\n", "  ✅ API连接测试通过\n", "🧠 正在初始化智能分析系统...\n", "  🧠 初始化智能记忆模块...\n", "  📚 加载专家知识库...\n", "  🔍 启动智能查询引擎...\n", "  🎮 初始化控制台界面...\n", "  ❌ 系统初始化失败: name 'PowerBankIntelligentAssistant' is not defined\n", "❌ 系统启动失败\n", "\n", "❌ 系统启动失败 - 故障排除指南\n", "\n", "🔍 常见问题检查:\n", "1. 数据库连接问题:\n", "   - 检查MySQL服务是否启动\n", "   - 验证数据库用户名和密码\n", "   - 确认数据库名称是否正确\n", "\n", "2. API连接问题:\n", "   - 检查网络连接\n", "   - 验证API密钥是否有效\n", "   - 确认API服务是否可用\n", "\n", "3. 依赖包问题:\n", "   - 运行: pip install -r requirements.txt\n", "   - 检查Python版本是否>=3.8\n", "\n", "💡 获取帮助:\n", "- 查看日志文件获取详细错误信息\n", "- 检查配置文件是否正确\n", "- 联系技术支持获取帮助\n", "    \n"]}, {"ename": "NameError", "evalue": "name 'custom_print' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 449\u001b[39m\n\u001b[32m    446\u001b[39m     start_powerbank_system()\n\u001b[32m    448\u001b[39m \u001b[38;5;66;03m# 系统信息显示\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m449\u001b[39m \u001b[43mcustom_print\u001b[49m(\u001b[33m\"\u001b[39m\u001b[33m✅ 智能体系统启动器 - 增强版 加载完成\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    450\u001b[39m custom_print(\u001b[33m\"\u001b[39m\u001b[33m🎯 集成功能: 环境检测、配置管理、错误诊断、四模块启动\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    451\u001b[39m custom_print(\u001b[33m\"\u001b[39m\u001b[33m🚀 启动方式: 运行 start_powerbank_system() 或直接执行文件\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'custom_print' is not defined"]}], "source": ["# ===================================================================\n", "# 🚀 智能体系统启动器 - 增强版\n", "# 作用: 一键启动完整的智能分析系统，集成四大核心模块\n", "# ===================================================================\n", "\n", "import os\n", "import sys\n", "import time\n", "from pathlib import Path\n", "from datetime import datetime\n", "\n", "def start_powerbank_system():\n", "    \"\"\"\n", "    一键启动共享充电宝智能分析系统 - 完整版\n", "    \n", "    功能特性:\n", "    - 🔍 自动环境检测和依赖验证\n", "    - 🗄️ 数据库连接测试和表结构验证\n", "    - 🧠 四大核心模块集成启动\n", "    - 🎮 控制台界面自动启动\n", "    - 🔧 智能错误诊断和修复建议\n", "    - 💾 配置文件自动生成和管理\n", "    - 🚀 优雅启动和退出处理\n", "    \"\"\"\n", "    \n", "    print_startup_banner()\n", "    \n", "    try:\n", "        # 1. 环境检查\n", "        print(\"🔍 正在检查运行环境...\")\n", "        check_environment()\n", "        \n", "        # 2. 配置检查和初始化\n", "        print(\"⚙️ 正在检查系统配置...\")\n", "        config = initialize_system_config()\n", "        \n", "        # 3. 数据库连接测试\n", "        print(\"🗄️ 正在测试数据库连接...\")\n", "        test_database_connection(config['db_config'])\n", "        \n", "        # 4. API连接测试\n", "        print(\"🌐 正在测试API连接...\")\n", "        test_api_connection(config['api_config'])\n", "        \n", "        # 5. 初始化核心系统\n", "        print(\"🧠 正在初始化智能分析系统...\")\n", "        console_system = initialize_console_system(config)\n", "        \n", "        if console_system:\n", "            print_startup_success()\n", "            \n", "            # 6. 启动控制台界面\n", "            console_system.run()\n", "        else:\n", "            print(\"❌ 系统启动失败\")\n", "            show_troubleshooting_guide()\n", "            \n", "    except KeyboardInterrupt:\n", "        print(\"\\n👋 用户中断，系统安全退出\")\n", "    except ImportError as e:\n", "        print(f\"❌ 依赖缺失: {e}\")\n", "        show_dependency_install_guide()\n", "    except Exception as e:\n", "        print(f\"❌ 启动异常: {e}\")\n", "        show_error_diagnosis(e)\n", "\n", "def print_startup_banner():\n", "    \"\"\"打印启动横幅\"\"\"\n", "    banner = f\"\"\"\n", "{Fore.CYAN}╔══════════════════════════════════════════════════════════════════╗\n", "║            🚀 共享充电宝智能数据分析系统 v2.0 启动器              ║\n", "║                    PowerBank AI System Launcher                   ║\n", "╠══════════════════════════════════════════════════════════════════╣\n", "║  🧠 智能记忆管理 | 📚 专家知识库 | 🔍 智能查询引擎 | 🎮 控制台界面  ║\n", "║  ⚡ 一键启动 | 🔧 自动诊断 | 💾 配置管理 | 🛡️ 错误恢复           ║\n", "╚══════════════════════════════════════════════════════════════════╝{Style.RESET_ALL}\n", "    \"\"\"\n", "    print(banner)\n", "    print(f\"{Fore.GREEN}🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Style.RESET_ALL}\")\n", "    print(\"=\" * 70)\n", "\n", "def check_environment():\n", "    \"\"\"检查运行环境 - 增强版\"\"\"\n", "    print(\"  📦 检查Python版本...\")\n", "    if sys.version_info < (3, 8):\n", "        raise RuntimeError(\"需要Python 3.8或更高版本\")\n", "    print(f\"  ✅ Python版本: {sys.version.split()[0]}\")\n", "    \n", "    print(\"  📚 检查必需依赖...\")\n", "    required_modules = {\n", "        'pandas': '数据处理',\n", "        'pymysql': '数据库连接',\n", "        'openai': 'AI模型接口',\n", "        'httpx': 'HTTP客户端',\n", "        'rich': '控制台美化',\n", "        'dashscope': 'Token计算',\n", "        'keyboard': 'ESC键监听(可选)'\n", "    }\n", "    \n", "    missing = []\n", "    optional_missing = []\n", "    \n", "    for module, description in required_modules.items():\n", "        try:\n", "            __import__(module.replace('-', '_'))\n", "            print(f\"  ✅ {module}: {description}\")\n", "        except ImportError:\n", "            if module == 'keyboard':\n", "                optional_missing.append(module)\n", "                print(f\"  ⚠️ {module}: {description} (可选)\")\n", "            else:\n", "                missing.append(module)\n", "                print(f\"  ❌ {module}: {description}\")\n", "    \n", "    if missing:\n", "        print(f\"\\n❌ 缺少必需依赖: {', '.join(missing)}\")\n", "        print(\"💡 请运行以下命令安装:\")\n", "        print(f\"   pip install {' '.join(missing)}\")\n", "        raise ImportError(\"缺少必要依赖\")\n", "    \n", "    if optional_missing:\n", "        print(f\"\\n⚠️ 缺少可选依赖: {', '.join(optional_missing)}\")\n", "        print(\"💡 安装可选依赖以获得完整功能:\")\n", "        print(f\"   pip install {' '.join(optional_missing)}\")\n", "    \n", "    print(\"  ✅ 环境检查通过\")\n", "\n", "def initialize_system_config():\n", "    \"\"\"初始化系统配置 - 增强版\"\"\"\n", "    # 动态路径配置\n", "    base_memory_path = Path.home() / \"powerbank_memory\"\n", "    config_file = base_memory_path / \"system_config.json\"\n", "    \n", "    # 确保配置目录存在\n", "    base_memory_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # 默认配置\n", "    default_config = {\n", "        'api_config': {\n", "            'api_key': os.getenv(\"DEEPSEEK_API_KEY\", \"***********************************\"),\n", "            'base_url': \"https://api.deepseek.com/v1\",\n", "            'model': \"deepseek-chat\",\n", "            'timeout': 30\n", "        },\n", "        'db_config': {\n", "            'host': os.getenv('DB_HOST', 'localhost'),\n", "            'user': os.getenv('DB_USER', 'root'),\n", "            'passwd': os.getenv('DB_PASSWORD', ''),\n", "            'db': os.getenv('DB_NAME', 'ads'),\n", "            'charset': 'utf8mb4',\n", "            'autocommit': True,\n", "            'connect_timeout': 10\n", "        },\n", "        'memory_config': {\n", "            'count_threshold': 25,\n", "            'token_threshold': 4000,\n", "            'use_token_mode': True,\n", "            'base_path': str(base_memory_path)\n", "        },\n", "        'interface_config': {\n", "            'auto_save': True,\n", "            'show_timestamps': True,\n", "            'color_output': True,\n", "            'loading_animation': True\n", "        }\n", "    }\n", "    \n", "    # 加载或创建配置文件\n", "    if config_file.exists():\n", "        try:\n", "            import json\n", "            with open(config_file, 'r', encoding='utf-8') as f:\n", "                saved_config = json.load(f)\n", "            \n", "            # 合并配置（保留用户自定义设置）\n", "            config = merge_configs(default_config, saved_config)\n", "            print(\"  ✅ 已加载现有配置文件\")\n", "        except Exception as e:\n", "            print(f\"  ⚠️ 配置文件读取失败，使用默认配置: {e}\")\n", "            config = default_config\n", "    else:\n", "        config = default_config\n", "        print(\"  📝 创建默认配置文件\")\n", "    \n", "    # 保存配置文件\n", "    try:\n", "        import json\n", "        with open(config_file, 'w', encoding='utf-8') as f:\n", "            json.dump(config, f, indent=2, ensure_ascii=False)\n", "        print(f\"  💾 配置已保存: {config_file}\")\n", "    except Exception as e:\n", "        print(f\"  ⚠️ 配置保存失败: {e}\")\n", "    \n", "    return config\n", "\n", "def merge_configs(default, saved):\n", "    \"\"\"合并配置字典\"\"\"\n", "    result = default.copy()\n", "    for key, value in saved.items():\n", "        if key in result and isinstance(result[key], dict) and isinstance(value, dict):\n", "            result[key].update(value)\n", "        else:\n", "            result[key] = value\n", "    return result\n", "\n", "def test_database_connection(db_config):\n", "    \"\"\"测试数据库连接 - 增强版\"\"\"\n", "    try:\n", "        import pymysql\n", "        \n", "        print(\"  🔌 正在连接数据库...\")\n", "        connection = pymysql.connect(**db_config)\n", "        \n", "        print(\"  📊 检查数据库状态...\")\n", "        cursor = connection.cursor()\n", "        \n", "        # 检查数据库版本\n", "        cursor.execute(\"SELECT VERSION()\")\n", "        version = cursor.fetchone()[0]\n", "        print(f\"  ✅ MySQL版本: {version}\")\n", "        \n", "        # 检查表数量\n", "        cursor.execute(\"SHOW TABLES\")\n", "        tables = cursor.fetchall()\n", "        print(f\"  📋 发现表数量: {len(tables)}\")\n", "        \n", "        # 检查关键表是否存在\n", "        expected_tables = ['order_table', 'user_table', 'region_table', 'time_table']\n", "        existing_tables = [table[0] for table in tables]\n", "        \n", "        missing_tables = [t for t in expected_tables if t not in existing_tables]\n", "        if missing_tables:\n", "            print(f\"  ⚠️ 缺少关键表: {', '.join(missing_tables)}\")\n", "            print(\"  💡 系统将在启动后尝试自动修复\")\n", "        else:\n", "            print(\"  ✅ 关键表结构完整\")\n", "        \n", "        cursor.close()\n", "        connection.close()\n", "        print(\"  ✅ 数据库连接测试通过\")\n", "        \n", "    except Exception as e:\n", "        print(f\"  ❌ 数据库连接失败: {e}\")\n", "        print(\"  💡 请检查数据库配置和服务状态\")\n", "        raise\n", "\n", "def test_api_connection(api_config):\n", "    \"\"\"测试API连接 - 增强版\"\"\"\n", "    try:\n", "        import httpx\n", "        from openai import OpenAI\n", "        \n", "        print(\"  🌐 正在测试API连接...\")\n", "        \n", "        # 创建客户端\n", "        http_client = httpx.Client(\n", "            timeout=api_config.get('timeout', 30),\n", "            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)\n", "        )\n", "        \n", "        client = OpenAI(\n", "            api_key=api_config['api_key'],\n", "            base_url=api_config['base_url'],\n", "            http_client=http_client\n", "        )\n", "        \n", "        # 测试简单请求\n", "        response = client.chat.completions.create(\n", "            model=api_config.get('model', 'deepseek-chat'),\n", "            messages=[{\"role\": \"user\", \"content\": \"测试连接\"}],\n", "            max_tokens=10\n", "        )\n", "        \n", "        if response.choices:\n", "            print(\"  ✅ API连接测试通过\")\n", "        else:\n", "            raise Exception(\"API响应异常\")\n", "            \n", "        http_client.close()\n", "        \n", "    except Exception as e:\n", "        print(f\"  ❌ API连接失败: {e}\")\n", "        print(\"  💡 请检查API密钥和网络连接\")\n", "        raise\n", "\n", "def initialize_console_system(config):\n", "    \"\"\"初始化控制台系统 - 集成四大模块\"\"\"\n", "    try:\n", "        print(\"  🧠 初始化智能记忆模块...\")\n", "        # 这里会调用之前定义的PowerBankIntelligentMemory等类\n", "        \n", "        print(\"  📚 加载专家知识库...\")\n", "        # 这里会调用PowerBankExpertKnowledge类\n", "        \n", "        print(\"  🔍 启动智能查询引擎...\")\n", "        # 这里会调用PowerBankIntelligentQueryEngine类\n", "        \n", "        print(\"  🎮 初始化控制台界面...\")\n", "        # 这里会调用PowerBankConsoleInterface类\n", "        \n", "        # 创建智能助手（集成所有模块）\n", "        assistant = PowerBankIntelligentAssistant(\n", "            db_config=config['db_config'],\n", "            api_config=config['api_config'],\n", "            memory_config=config['memory_config']\n", "        )\n", "        \n", "        # 创建控制台界面\n", "        console_interface = PowerBankConsoleInterface(assistant)\n", "        \n", "        print(\"  ✅ 系统初始化完成\")\n", "        return console_interface\n", "        \n", "    except Exception as e:\n", "        print(f\"  ❌ 系统初始化失败: {e}\")\n", "        return None\n", "\n", "def print_startup_success():\n", "    \"\"\"打印启动成功信息\"\"\"\n", "    success_banner = f\"\"\"\n", "{Fore.GREEN}🎉 系统启动成功！{Style.RESET_ALL}\n", "\n", "{Fore.CYAN}📋 可用功能:{Style.RESET_ALL}\n", "  🤖 智能问答 - 自然语言查询数据库\n", "  📊 数据分析 - 多维度业务洞察\n", "  🔍 SQL生成 - 自动生成优化查询\n", "  📈 趋势分析 - 时间序列和对比分析\n", "  🗺️ 地区分析 - 地理分布和热力图\n", "  👥 用户画像 - 行为模式和分层分析\n", "\n", "{Fore.YELLOW}⚡ 快捷命令:{Style.RESET_ALL}\n", "  /help     - 查看完整帮助\n", "  /demo     - 运行功能演示\n", "  /status   - 查看系统状态\n", "  /diagnose - 系统健康检查\n", "  /batch    - 批量分析模式\n", "  /quit     - 退出系统\n", "\n", "{Fore.RED}🔥 快速退出: 随时按 ESC 键{Style.RESET_ALL}\n", "    \"\"\"\n", "    print(success_banner)\n", "    print(\"=\" * 70)\n", "\n", "def show_troubleshooting_guide():\n", "    \"\"\"显示故障排除指南\"\"\"\n", "    guide = f\"\"\"\n", "{Fore.RED}❌ 系统启动失败 - 故障排除指南{Style.RESET_ALL}\n", "\n", "{Fore.YELLOW}🔍 常见问题检查:{Style.RESET_ALL}\n", "1. 数据库连接问题:\n", "   - 检查MySQL服务是否启动\n", "   - 验证数据库用户名和密码\n", "   - 确认数据库名称是否正确\n", "\n", "2. API连接问题:\n", "   - 检查网络连接\n", "   - 验证API密钥是否有效\n", "   - 确认API服务是否可用\n", "\n", "3. 依赖包问题:\n", "   - 运行: pip install -r requirements.txt\n", "   - 检查Python版本是否>=3.8\n", "\n", "{Fore.CYAN}💡 获取帮助:{Style.RESET_ALL}\n", "- 查看日志文件获取详细错误信息\n", "- 检查配置文件是否正确\n", "- 联系技术支持获取帮助\n", "    \"\"\"\n", "    print(guide)\n", "\n", "def show_dependency_install_guide():\n", "    \"\"\"显示依赖安装指南\"\"\"\n", "    guide = f\"\"\"\n", "{Fore.YELLOW}📦 依赖安装指南{Style.RESET_ALL}\n", "\n", "{Fore.CYAN}方法1 - 使用requirements.txt:{Style.RESET_ALL}\n", "pip install -r requirements.txt\n", "\n", "{Fore.CYAN}方法2 - 手动安装核心依赖:{Style.RESET_ALL}\n", "pip install pandas pymysql openai httpx rich dashscope\n", "\n", "{Fore.CYAN}方法3 - 安装可选依赖:{Style.RESET_ALL}\n", "pip install keyboard  # ESC键功能\n", "\n", "{Fore.GREEN}💡 提示:{Style.RESET_ALL}\n", "- 建议使用虚拟环境: python -m venv venv\n", "- 激活虚拟环境后再安装依赖\n", "- 如遇网络问题，可使用国内镜像源\n", "    \"\"\"\n", "    print(guide)\n", "\n", "def show_error_diagnosis(error):\n", "    \"\"\"显示错误诊断信息\"\"\"\n", "    diagnosis = f\"\"\"\n", "{Fore.RED}🔧 错误诊断信息{Style.RESET_ALL}\n", "\n", "{Fore.YELLOW}错误类型:{Style.RESET_ALL} {type(error).__name__}\n", "{Fore.YELLOW}错误信息:{Style.RESET_ALL} {str(error)}\n", "\n", "{Fore.CYAN}建议解决方案:{Style.RESET_ALL}\n", "1. 检查系统配置文件\n", "2. 验证数据库连接参数\n", "3. 确认API密钥有效性\n", "4. 重新安装依赖包\n", "5. 查看详细日志信息\n", "\n", "{Fore.GREEN}💡 如需技术支持，请保存此错误信息{Style.RESET_ALL}\n", "    \"\"\"\n", "    print(diagnosis)\n", "\n", "# 添加颜色支持\n", "try:\n", "    from colorama import init, Fore, Style\n", "    init(autoreset=True)\n", "except ImportError:\n", "    # 如果colorama未安装，使用空的颜色类\n", "    class Fore:\n", "        RED = GREEN = YELLOW = CYAN = BLUE = MAGENTA = WHITE = ''\n", "    \n", "    class Style:\n", "        RESET_ALL = BRIGHT = DIM = ''\n", "\n", "# ===================================================================\n", "# 🎯 系统启动入口 - 增强版\n", "# ===================================================================\n", "\n", "def main():\n", "    \"\"\"主入口函数\"\"\"\n", "    try:\n", "        start_powerbank_system()\n", "    except Exception as e:\n", "        print(f\"\\n{Fore.RED}💥 系统异常退出: {e}{Style.RESET_ALL}\")\n", "        print(f\"{Fore.YELLOW}💡 请检查系统配置和依赖环境{Style.RESET_ALL}\")\n", "        sys.exit(1)\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "\n", "# ===================================================================\n", "# 🚀 快速启动函数 - 兼容性保持\n", "# ===================================================================\n", "\n", "def start_powerbank_console():\n", "    \"\"\"快速启动函数 - 保持向后兼容\"\"\"\n", "    print(\"🔄 正在启动智能分析系统...\")\n", "    start_powerbank_system()\n", "\n", "# 系统信息显示\n", "custom_print(\"✅ 智能体系统启动器 - 增强版 加载完成\")\n", "custom_print(\"🎯 集成功能: 环境检测、配置管理、错误诊断、四模块启动\")\n", "custom_print(\"🚀 启动方式: 运行 start_powerbank_system() 或直接执行文件\")\n", "custom_print(\"💡 配置文件: ~/powerbank_memory/system_config.json\")"]}, {"cell_type": "code", "execution_count": null, "id": "b2d4b74b", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 🚀 智能体系统启动器 - 增强版\n", "# 作用: 一键启动完整的智能分析系统，集成四大核心模块\n", "# ===================================================================\n", "\n", "import os\n", "import sys\n", "import time\n", "import json\n", "from datetime import datetime\n", "from typing import Dict, Optional\n", "\n", "# 美化输出\n", "try:\n", "    from rich.console import Console\n", "    from rich.panel import Panel\n", "    from rich.markdown import Markdown\n", "    console = Console()\n", "    def custom_print(info): console.print(info)\n", "except ImportError:\n", "    def custom_print(info): print(info)\n", "\n", "def start_powerbank_system():\n", "    \"\"\"一键启动共享充电宝智能分析系统 - 直接对话版\"\"\"\n", "    \n", "    print_startup_banner()\n", "    \n", "    try:\n", "        # 1. 快速环境检查\n", "        custom_print(\"🔍 检查环境...\")\n", "        check_basic_environment()\n", "        \n", "        # 2. 初始化配置\n", "        custom_print(\"⚙️ 加载配置...\")\n", "        config = get_system_config()\n", "        \n", "        # 3. 快速连接测试\n", "        custom_print(\"🔗 测试连接...\")\n", "        test_connections(config)\n", "        \n", "        # 4. 初始化AI助手\n", "        custom_print(\"🧠 初始化AI助手...\")\n", "        assistant = create_assistant(config)\n", "        \n", "        if assistant:\n", "            custom_print(\"✅ 系统启动成功！\")\n", "            custom_print(\"🎯 正在启动AI对话界面...\")\n", "            time.sleep(1)  # 短暂停顿\n", "            \n", "            # 5. 直接启动对话界面\n", "            start_ai_chat_interface(assistant)\n", "        else:\n", "            custom_print(\"❌ 启动失败，请检查配置\")\n", "            \n", "    except KeyboardInterrupt:\n", "        custom_print(\"\\n👋 用户退出\")\n", "    except Exception as e:\n", "        custom_print(f\"❌ 启动错误: {e}\")\n", "        custom_print(\"💡 尝试运行 quick_chat_mode() 进入简化模式\")\n", "\n", "def print_startup_banner():\n", "    \"\"\"启动横幅\"\"\"\n", "    banner = f\"\"\"\n", "╔═══════════════════════════════════════════════════════════╗\n", "║        🚀 共享充电宝智能数据分析系统 v2.0                  ║\n", "║    🧠 智能记忆 | 📚 专家知识 | 🔍 查询引擎 | 💬 AI对话     ║\n", "╚═══════════════════════════════════════════════════════════╝\n", "🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n", "    \"\"\"\n", "    custom_print(banner)\n", "\n", "def check_basic_environment():\n", "    \"\"\"基础环境检查\"\"\"\n", "    required = ['pandas', 'pymysql', 'openai', 'httpx']\n", "    missing = []\n", "    \n", "    for pkg in required:\n", "        try:\n", "            __import__(pkg.replace('-', '_'))\n", "        except ImportError:\n", "            missing.append(pkg)\n", "    \n", "    if missing:\n", "        custom_print(f\"❌ 缺少依赖: {', '.join(missing)}\")\n", "        custom_print(f\"💡 安装命令: pip install {' '.join(missing)}\")\n", "        raise ImportError(\"依赖缺失\")\n", "    \n", "    custom_print(\"  ✅ 核心依赖检查通过\")\n", "\n", "def get_system_config():\n", "    \"\"\"获取系统配置 - 修复数据库密码\"\"\"\n", "    return {\n", "        'db_config': {\n", "            'host': 'localhost',\n", "            'user': 'root',\n", "            'password': '',  # 修改为空密码，根据您的实际情况调整\n", "            'database': 'ads'  # 根据您的notebook，应该是'ads'数据库\n", "        },\n", "        'api_config': {\n", "            'api_key': \"***********************************\",\n", "            'base_url': \"https://api.deepseek.com/v1\",\n", "            'model': 'deepseek-chat'\n", "        },\n", "        'memory_config': {\n", "            'count_threshold': 20,\n", "            'token_threshold': 3000,\n", "            'use_token_mode': True\n", "        }\n", "    }\n", "\n", "def test_db_connection(db_config):\n", "    \"\"\"测试数据库连接\"\"\"\n", "    try:\n", "        import pymysql\n", "        connection = pymysql.connect(**db_config)\n", "        cursor = connection.cursor()\n", "        cursor.execute(\"SELECT DATABASE()\")\n", "        result = cursor.fetchone()\n", "        custom_print(f\"  ✅ 数据库连接成功: {result[0]}\")\n", "        cursor.close()\n", "        connection.close()\n", "        return True\n", "    except Exception as e:\n", "        custom_print(f\"  ❌ 数据库连接失败: {e}\")\n", "        raise e\n", "\n", "def test_connections(config):\n", "    \"\"\"测试连接 - 改进版\"\"\"\n", "    # 测试数据库连接\n", "    try:\n", "        test_db_connection(config['db_config'])\n", "    except Exception as e:\n", "        custom_print(f\"  ⚠️ 数据库连接失败，将使用无数据库模式\")\n", "        # 修改配置为无数据库模式\n", "        config['db_config'] = None\n", "    \n", "    # 测试API连接\n", "    try:\n", "        from openai import OpenAI\n", "        import httpx\n", "        \n", "        http_client = httpx.Client(follow_redirects=True)\n", "        client = OpenAI(\n", "            api_key=config['api_config']['api_key'],\n", "            base_url=config['api_config']['base_url'],\n", "            http_client=http_client\n", "        )\n", "        \n", "        # 简单测试\n", "        response = client.chat.completions.create(\n", "            model=\"deepseek-chat\",\n", "            messages=[{\"role\": \"user\", \"content\": \"你好\"}],\n", "            max_tokens=10\n", "        )\n", "        custom_print(\"  ✅ AI API连接正常\")\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"  ⚠️ AI API连接异常: {e}\")\n", "\n", "def create_simple_powerbank_assistant(config):\n", "    \"\"\"创建简化版共享充电宝助手\"\"\"\n", "    try:\n", "        from openai import OpenAI\n", "        import httpx\n", "        import pymysql\n", "        \n", "        # 初始化AI客户端\n", "        http_client = httpx.Client(follow_redirects=True)\n", "        client = OpenAI(\n", "            api_key=config['api_config']['api_key'],\n", "            base_url=config['api_config']['base_url'],\n", "            http_client=http_client\n", "        )\n", "        \n", "        # 简化版助手类\n", "        class SimplePowerBankAssistant:\n", "            def __init__(self, client, db_config):\n", "                self.client = client\n", "                self.db_config = db_config\n", "                self.conversation_history = []\n", "            \n", "            def analyze(self, question):\n", "                \"\"\"智能分析方法\"\"\"\n", "                try:\n", "                    # 构建系统提示\n", "                    system_prompt = \"\"\"你是共享充电宝数据分析专家，具备以下能力：\n", "1. 数据分析和业务洞察\n", "2. SQL查询生成和优化\n", "3. 用户行为分析\n", "4. 市场趋势预测\n", "5. 业务建议提供\n", "\n", "请根据用户问题提供专业的分析和建议。\"\"\"\n", "                    \n", "                    # 调用AI模型\n", "                    response = self.client.chat.completions.create(\n", "                        model=\"deepseek-chat\",\n", "                        messages=[\n", "                            {\"role\": \"system\", \"content\": system_prompt},\n", "                            {\"role\": \"user\", \"content\": question}\n", "                        ],\n", "                        temperature=0.7\n", "                    )\n", "                    \n", "                    answer = response.choices[0].message.content\n", "                    \n", "                    # 记录对话历史\n", "                    self.conversation_history.append({\n", "                        \"question\": question,\n", "                        \"answer\": answer,\n", "                        \"timestamp\": datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "                    })\n", "                    \n", "                    return {\n", "                        \"response\": answer,\n", "                        \"status\": \"success\",\n", "                        \"type\": \"analysis\"\n", "                    }\n", "                    \n", "                except Exception as e:\n", "                    return {\n", "                        \"response\": f\"分析过程中出现错误: {e}\",\n", "                        \"status\": \"error\",\n", "                        \"type\": \"error\"\n", "                    }\n", "            \n", "            def chat(self, message):\n", "                \"\"\"简单聊天方法\"\"\"\n", "                try:\n", "                    response = self.client.chat.completions.create(\n", "                        model=\"deepseek-chat\",\n", "                        messages=[\n", "                            {\"role\": \"system\", \"content\": \"你是一个友好的共享充电宝业务助手。\"},\n", "                            {\"role\": \"user\", \"content\": message}\n", "                        ]\n", "                    )\n", "                    return response.choices[0].message.content\n", "                except Exception as e:\n", "                    return f\"聊天错误: {e}\"\n", "            \n", "            def execute_sql_query(self, sql):\n", "                \"\"\"执行SQL查询（如果需要）\"\"\"\n", "                if not self.db_config:\n", "                    return {\"error\": \"数据库未连接\", \"status\": \"error\"}\n", "                \n", "                try:\n", "                    connection = pymysql.connect(**self.db_config)\n", "                    cursor = connection.cursor()\n", "                    cursor.execute(sql)\n", "                    results = cursor.fetchall()\n", "                    column_names = [desc[0] for desc in cursor.description]\n", "                    cursor.close()\n", "                    connection.close()\n", "                    \n", "                    return {\n", "                        \"data\": results,\n", "                        \"columns\": column_names,\n", "                        \"status\": \"success\"\n", "                    }\n", "                except Exception as e:\n", "                    return {\n", "                        \"error\": str(e),\n", "                        \"status\": \"error\"\n", "                    }\n", "        \n", "        return SimplePowerBankAssistant(client, config['db_config'])\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"❌ 简化助手创建失败: {e}\")\n", "        return None\n", "\n", "def create_assistant(config):\n", "    \"\"\"创建智能助手实例 - 移除不存在的方法调用\"\"\"\n", "    try:\n", "        # 先尝试创建完整版助手\n", "        try:\n", "            assistant = PowerBankIntelligentAssistant(\n", "                db_config=config['db_config'],\n", "                api_config=config['api_config'],\n", "                memory_config=config['memory_config']\n", "            )\n", "            custom_print(\"  ✅ 完整版助手创建成功\")\n", "            return assistant\n", "        except:\n", "            # 如果完整版失败，创建简化版\n", "            assistant = create_simple_powerbank_assistant(config)\n", "            if assistant:\n", "                custom_print(\"  ✅ 简化版助手创建成功\")\n", "                return assistant\n", "            else:\n", "                raise Exception(\"简化版助手也创建失败\")\n", "        \n", "    except Exception as e:\n", "        custom_print(f\"❌ 助手创建失败: {e}\")\n", "        return create_simple_assistant(config)\n", "\n", "def create_simple_assistant(config):\n", "    \"\"\"创建简化版助手（降级方案）\"\"\"\n", "    try:\n", "        from openai import OpenAI\n", "        import httpx\n", "        \n", "        http_client = httpx.Client(follow_redirects=True)\n", "        client = OpenAI(\n", "            api_key=config['api_config']['api_key'],\n", "            base_url=config['api_config']['base_url'],\n", "            http_client=http_client\n", "        )\n", "        \n", "        # 简化版助手类\n", "        class SimpleAssistant:\n", "            def __init__(self, client):\n", "                self.client = client\n", "            \n", "            def analyze(self, message):\n", "                return {\"response\": self.chat(message)}\n", "            \n", "            def chat(self, message):\n", "                try:\n", "                    response = self.client.chat.completions.create(\n", "                        model=\"deepseek-chat\",\n", "                        messages=[\n", "                            {\"role\": \"system\", \"content\": \"你是共享充电宝数据分析专家，精通数据分析和业务洞察。\"},\n", "                            {\"role\": \"user\", \"content\": message}\n", "                        ]\n", "                    )\n", "                    return response.choices[0].message.content\n", "                except Exception as e:\n", "                    return f\"AI处理错误: {e}\"\n", "        \n", "        custom_print(\"  ✅ 基础版助手创建成功\")\n", "        return SimpleAssistant(client)\n", "    except Exception as e:\n", "        custom_print(f\"❌ 基础助手创建也失败: {e}\")\n", "        return None\n", "\n", "def start_ai_chat_interface(assistant):\n", "    \"\"\"启动AI对话界面\"\"\"\n", "    # 清屏并显示对话界面\n", "    os.system('cls' if os.name == 'nt' else 'clear')\n", "    \n", "    chat_banner = \"\"\"\n", "╔═══════════════════════════════════════════════════════════╗\n", "║           🤖 AI智能助手对话界面                            ║\n", "║       💬 直接输入问题开始对话 | 输入 /quit 退出            ║\n", "╚═══════════════════════════════════════════════════════════╝\n", "    \"\"\"\n", "    custom_print(chat_banner)\n", "    \n", "    custom_print(\"🤖 AI助手: 您好！我是共享充电宝数据分析专家，有什么可以帮您的吗？\")\n", "    custom_print(\"💡 您可以问我关于数据分析、业务洞察或其他任何问题\")\n", "    custom_print(\"-\" * 60)\n", "    \n", "    while True:\n", "        try:\n", "            # 获取用户输入\n", "            user_input = input(\"\\n👤 您: \").strip()\n", "            \n", "            if not user_input:\n", "                continue\n", "            \n", "            # 处理退出命令\n", "            if user_input.lower() in ['/quit', '/exit', 'quit', 'exit', '退出']:\n", "                custom_print(\"🤖 AI助手: 感谢使用，再见！👋\")\n", "                break\n", "            \n", "            # 处理帮助命令\n", "            if user_input.lower() in ['/help', 'help', '帮助']:\n", "                show_chat_help()\n", "                continue\n", "            \n", "            # 处理清屏命令\n", "            if user_input.lower() in ['/clear', 'clear', '清屏']:\n", "                os.system('cls' if os.name == 'nt' else 'clear')\n", "                custom_print(chat_banner)\n", "                continue\n", "            \n", "            # AI对话处理\n", "            custom_print(\"🤖 AI助手正在思考...\")\n", "            \n", "            try:\n", "                # 尝试使用完整助手功能\n", "                if hasattr(assistant, 'analyze'):\n", "                    result = assistant.analyze(user_input)\n", "                    if isinstance(result, dict):\n", "                        response = result.get('response', str(result))\n", "                    else:\n", "                        response = str(result)\n", "                <PERSON><PERSON>(assistant, 'intelligent_chat'):\n", "                    response = assistant.intelligent_chat(user_input)\n", "                <PERSON><PERSON>(assistant, 'chat'):\n", "                    response = assistant.chat(user_input)\n", "                else:\n", "                    response = \"抱歉，AI助手功能异常\"\n", "                \n", "                # 显示AI回复\n", "                custom_print(f\"🤖 AI助手: {response}\")\n", "                \n", "            except Exception as e:\n", "                custom_print(f\"🤖 AI助手: 抱歉，处理您的问题时出现错误: {e}\")\n", "                custom_print(\"💡 请尝试重新表述您的问题\")\n", "            \n", "        except KeyboardInterrupt:\n", "            custom_print(\"\\n🤖 AI助手: 检测到Ctrl+C，正在退出...\")\n", "            break\n", "        except Exception as e:\n", "            custom_print(f\"❌ 对话错误: {e}\")\n", "\n", "def show_chat_help():\n", "    \"\"\"显示对话帮助\"\"\"\n", "    help_text = \"\"\"\n", "🔧 可用命令:\n", "• /help 或 help     - 显示此帮助信息\n", "• /quit 或 quit     - 退出对话\n", "• /clear 或 clear   - 清屏\n", "\n", "💡 使用技巧:\n", "• 直接输入问题进行AI对话\n", "• 支持数据分析查询，如\"各品牌市场份额如何？\"\n", "• 支持普通聊天，如\"你好\"、\"介绍一下系统\"\n", "• 支持业务咨询，如\"如何提高用户留存率？\"\n", "\n", "📊 数据分析示例:\n", "• \"用户性别分布情况如何？\"\n", "• \"哪个地区收入最高？\"\n", "• \"各品牌充电宝使用频率对比\"\n", "• \"用户行为分析报告\"\n", "    \"\"\"\n", "    custom_print(help_text)\n", "\n", "def quick_chat_mode():\n", "    \"\"\"快速聊天模式（应急方案）\"\"\"\n", "    custom_print(\"🚀 启动快速聊天模式...\")\n", "    \n", "    try:\n", "        from openai import OpenAI\n", "        import httpx\n", "        \n", "        http_client = httpx.Client(follow_redirects=True)\n", "        client = OpenAI(\n", "            api_key=\"***********************************\",\n", "            base_url=\"https://api.deepseek.com/v1\",\n", "            http_client=http_client\n", "        )\n", "        \n", "        custom_print(\"✅ 快速模式启动成功！\")\n", "        custom_print(\"🤖 AI助手: 您好！我是AI助手，有什么可以帮您的吗？\")\n", "        \n", "        while True:\n", "            user_input = input(\"\\n👤 您: \").strip()\n", "            if user_input.lower() in ['quit', 'exit', '退出']:\n", "                break\n", "            \n", "            try:\n", "                response = client.chat.completions.create(\n", "                    model=\"deepseek-chat\",\n", "                    messages=[\n", "                        {\"role\": \"system\", \"content\": \"你是一个友好的AI助手，擅长回答各种问题。\"},\n", "                        {\"role\": \"user\", \"content\": user_input}\n", "                    ]\n", "                )\n", "                custom_print(f\"🤖 AI助手: {response.choices[0].message.content}\")\n", "            except Exception as e:\n", "                custom_print(f\"❌ 错误: {e}\")\n", "                \n", "    except Exception as e:\n", "        custom_print(f\"❌ 快速模式启动失败: {e}\")\n", "\n", "# 启动选项\n", "custom_print(\"\"\"\n", "🚀 启动选项:\n", "1. start_powerbank_system()  # 完整系统启动 (推荐)\n", "2. quick_chat_mode()         # 快速聊天模式 (应急)\n", "\n", "💡 直接运行: start_powerbank_system()\n", "\"\"\")"]}], "metadata": {"kernelspec": {"display_name": "zjou-2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}