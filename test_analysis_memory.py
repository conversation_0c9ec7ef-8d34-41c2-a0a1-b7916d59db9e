#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试分析记忆保存功能 - analysis_history.json
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.getcwd())

def test_analysis_memory():
    """测试分析记忆保存功能"""
    print("🧪 测试分析记忆保存功能 (analysis_history.json)")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from pathlib import Path
        import json
        from datetime import datetime
        
        # 创建测试用长期记忆类
        class TestAnalysisMemory:
            """测试用分析记忆类"""
            
            def __init__(self, base_path: str = None):
                """初始化分析记忆"""
                self.base_path = Path(base_path) if base_path else Path.cwd() / "powerbank_memory"
                self.base_path.mkdir(exist_ok=True)
                
                self.analysis_file = self.base_path / "analysis_history.json"
                
                # 确保文件存在
                self._ensure_files_exist()
                print(f"📊 分析记忆系统初始化完成: {self.base_path}")
            
            def _ensure_files_exist(self):
                """确保记忆文件存在"""
                try:
                    if not self.analysis_file.exists():
                        self.analysis_file.write_text("[]", encoding='utf-8')
                except Exception as e:
                    print(f"⚠️ 分析文件创建失败: {e}")
            
            def save_analysis(self, analysis_type: str, question: str, sql_query: str, 
                             data_result: list, insights: str, metadata: dict = None) -> bool:
                """保存数据分析记录"""
                try:
                    # 读取现有数据
                    analysis_data = []
                    if self.analysis_file.exists():
                        try:
                            analysis_data = json.loads(self.analysis_file.read_text(encoding='utf-8'))
                        except:
                            analysis_data = []
                    
                    # 添加新记录
                    new_record = {
                        "analysis_type": analysis_type,
                        "question": question,
                        "sql_query": sql_query,
                        "data_count": len(data_result) if data_result else 0,
                        "data_sample": data_result[:3] if data_result else [],  # 保存前3条数据作为样本
                        "insights": insights,
                        "timestamp": datetime.now().isoformat(),
                        "metadata": metadata or {}
                    }
                    analysis_data.append(new_record)
                    
                    # 保持最近500条分析记录
                    if len(analysis_data) > 500:
                        analysis_data = analysis_data[-500:]
                    
                    # 保存
                    self.analysis_file.write_text(json.dumps(analysis_data, ensure_ascii=False, indent=2), encoding='utf-8')
                    return True
                    
                except Exception as e:
                    print(f"❌ 分析记录保存失败: {e}")
                    return False
            
            def get_analysis_count(self) -> int:
                """获取分析记录数量"""
                try:
                    if not self.analysis_file.exists():
                        return 0
                    analysis_data = json.loads(self.analysis_file.read_text(encoding='utf-8'))
                    return len(analysis_data)
                except:
                    return 0
            
            def get_analysis_stats(self) -> dict:
                """获取分析统计信息"""
                try:
                    if not self.analysis_file.exists():
                        return {"total_analysis": 0, "analysis_types": {}}
                    
                    analysis_data = json.loads(self.analysis_file.read_text(encoding='utf-8'))
                    
                    # 统计分析类型
                    analysis_types = {}
                    for record in analysis_data:
                        analysis_type = record.get('analysis_type', '未知')
                        analysis_types[analysis_type] = analysis_types.get(analysis_type, 0) + 1
                    
                    return {
                        "total_analysis": len(analysis_data),
                        "analysis_types": analysis_types,
                        "latest_analysis": analysis_data[-1] if analysis_data else None
                    }
                    
                except Exception as e:
                    print(f"❌ 分析统计失败: {e}")
                    return {"total_analysis": 0, "analysis_types": {}}
        
        # 创建测试实例
        memory = TestAnalysisMemory()
        
        print(f"📊 当前分析记录数: {memory.get_analysis_count()}")
        
        # 测试保存功能 - 模拟不同类型的数据分析
        test_cases = [
            {
                "analysis_type": "品牌分析",
                "question": "各品牌市场份额如何？",
                "sql_query": "SELECT 品牌名称, 市场份额 FROM brand_revenue_summary ORDER BY 市场份额 DESC",
                "data_result": [
                    {"品牌名称": "搜电", "市场份额": 20.3},
                    {"品牌名称": "街电", "市场份额": 19.2},
                    {"品牌名称": "小电", "市场份额": 15.8},
                    {"品牌名称": "怪兽充电", "市场份额": 12.5},
                    {"品牌名称": "来电", "市场份额": 10.1},
                    {"品牌名称": "其他", "市场份额": 22.1}
                ],
                "insights": "搜电以20.3%的份额领先市场，前五大品牌占据77.9%的市场份额，市场集中度较高。",
                "metadata": {
                    "processing_time": 2.5,
                    "has_visualization": True,
                    "chart_type": "pie_chart",
                    "success": True
                }
            },
            {
                "analysis_type": "用户分析",
                "question": "用户年龄分布情况？",
                "sql_query": "SELECT 年龄段, COUNT(*) as 用户数量 FROM user_behavior_summary GROUP BY 年龄段",
                "data_result": [
                    {"年龄段": "18-25", "用户数量": 15420},
                    {"年龄段": "26-35", "用户数量": 28350},
                    {"年龄段": "36-45", "用户数量": 12680},
                    {"年龄段": "46-55", "用户数量": 5240},
                    {"年龄段": "55+", "用户数量": 1890}
                ],
                "insights": "用户主要集中在26-35岁年龄段，占比最高达44.7%，18-25岁年龄段次之，占比24.3%。",
                "metadata": {
                    "processing_time": 1.8,
                    "has_visualization": True,
                    "chart_type": "bar_chart",
                    "success": True
                }
            },
            {
                "analysis_type": "地区分析",
                "question": "哪个地区收入最高？",
                "sql_query": "SELECT 地区, SUM(收入) as 总收入 FROM regional_revenue GROUP BY 地区 ORDER BY 总收入 DESC",
                "data_result": [
                    {"地区": "北京", "总收入": 1250000},
                    {"地区": "上海", "总收入": 1180000},
                    {"地区": "深圳", "总收入": 980000},
                    {"地区": "广州", "总收入": 850000},
                    {"地区": "杭州", "总收入": 720000}
                ],
                "insights": "北京地区收入最高，达125万元，一线城市收入明显高于其他地区。",
                "metadata": {
                    "processing_time": 2.1,
                    "has_visualization": True,
                    "chart_type": "bar_chart",
                    "success": True
                }
            },
            {
                "analysis_type": "时间分析",
                "question": "最近3个月的收入趋势如何？",
                "sql_query": "SELECT 月份, SUM(收入) as 月收入 FROM monthly_revenue WHERE 月份 >= '2024-04' ORDER BY 月份",
                "data_result": [
                    {"月份": "2024-04", "月收入": 2800000},
                    {"月份": "2024-05", "月收入": 3200000},
                    {"月份": "2024-06", "月收入": 3500000}
                ],
                "insights": "最近3个月收入呈上升趋势，6月份达到350万元的新高，环比增长9.4%。",
                "metadata": {
                    "processing_time": 1.9,
                    "has_visualization": True,
                    "chart_type": "line_chart",
                    "success": True
                }
            }
        ]
        
        print("\n🔄 开始测试分析记录保存功能...")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📊 测试 {i}: {test_case['analysis_type']} - {test_case['question'][:30]}...")
            
            success = memory.save_analysis(
                analysis_type=test_case["analysis_type"],
                question=test_case["question"],
                sql_query=test_case["sql_query"],
                data_result=test_case["data_result"],
                insights=test_case["insights"],
                metadata=test_case["metadata"]
            )
            
            if success:
                print(f"  ✅ 保存成功")
            else:
                print(f"  ❌ 保存失败")
            
            time.sleep(0.5)  # 间隔0.5秒
        
        # 验证保存结果
        print(f"\n📊 保存后分析记录数: {memory.get_analysis_count()}")
        
        # 获取统计信息
        stats = memory.get_analysis_stats()
        print(f"📈 分析类型统计: {stats['analysis_types']}")
        
        # 检查文件内容
        if memory.analysis_file.exists():
            file_size = memory.analysis_file.stat().st_size
            print(f"📁 分析文件大小: {file_size} 字节")
            
            # 读取并显示最新记录
            try:
                analysis_data = json.loads(memory.analysis_file.read_text(encoding='utf-8'))
                if analysis_data:
                    latest_record = analysis_data[-1]
                    print(f"📋 最新分析记录:")
                    print(f"  类型: {latest_record['analysis_type']}")
                    print(f"  问题: {latest_record['question'][:50]}...")
                    print(f"  数据量: {latest_record['data_count']}条")
                    print(f"  时间: {latest_record['timestamp']}")
            except Exception as e:
                print(f"⚠️ 读取记录失败: {e}")
        
        print("\n" + "=" * 60)
        print("🎉 分析记忆保存测试完成！")
        print(f"📁 文件位置: {memory.analysis_file}")
        print("💡 现在你可以检查analysis_history.json文件，应该包含详细的分析记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_analysis_files():
    """检查当前分析文件状态"""
    print("\n🔍 检查当前分析记忆文件状态")
    print("-" * 40)
    
    memory_dir = Path("powerbank_memory")
    analysis_file = memory_dir / "analysis_history.json"
    
    if analysis_file.exists():
        file_size = analysis_file.stat().st_size
        print(f"📁 analysis_history.json: {file_size} 字节")
        
        try:
            content = json.loads(analysis_file.read_text(encoding='utf-8'))
            print(f"  📊 分析记录数: {len(content)}")
            if content:
                # 统计分析类型
                types = {}
                for record in content:
                    analysis_type = record.get('analysis_type', '未知')
                    types[analysis_type] = types.get(analysis_type, 0) + 1
                print(f"  📈 分析类型: {types}")
                print(f"  📅 最新记录时间: {content[-1].get('timestamp', '未知')}")
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
    else:
        print(f"❌ analysis_history.json: 文件不存在")

def main():
    """主函数"""
    print("🚀 分析记忆保存功能测试")
    
    # 检查当前状态
    check_analysis_files()
    
    # 运行测试
    success = test_analysis_memory()
    
    # 再次检查状态
    check_analysis_files()
    
    if success:
        print("\n✅ 测试成功！分析记忆保存功能正常工作")
        print("💡 建议：重新运行智能体系统，现在数据查询问题会自动保存到analysis_history.json")
        print("\n🎯 触发保存的问题类型:")
        print("  • 品牌相关: '各品牌市场份额如何？'")
        print("  • 用户相关: '用户年龄分布情况？'")
        print("  • 地区相关: '哪个地区收入最高？'")
        print("  • 时间相关: '最近的收入趋势如何？'")
        print("  • 市场相关: '市场占有率分析'")
    else:
        print("\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
