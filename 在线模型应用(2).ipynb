{"cells": [{"cell_type": "markdown", "id": "de767b8f", "metadata": {}, "source": ["# 1. 对话类模型使用初体验"]}, {"cell_type": "code", "execution_count": null, "id": "1d639de6", "metadata": {}, "outputs": [], "source": ["! pip install openai==1.54.0"]}, {"cell_type": "code", "execution_count": null, "id": "ccf2f76d", "metadata": {}, "outputs": [], "source": ["! pip install rich"]}, {"cell_type": "code", "execution_count": null, "id": "42056669", "metadata": {}, "outputs": [], "source": ["! pip show openai"]}, {"cell_type": "code", "execution_count": null, "id": "b910afd7", "metadata": {}, "outputs": [], "source": ["! pip install httpx"]}, {"cell_type": "code", "execution_count": null, "id": "4b453978", "metadata": {}, "outputs": [], "source": ["# 清除所有代理环境变量\n", "# for var in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']:\n", "#     os.environ.pop(var, None)"]}, {"cell_type": "code", "execution_count": null, "id": "b0d0dbd4", "metadata": {}, "outputs": [], "source": ["# 创建HTTP客户端时不使用proxies参数\n", "import httpx\n", "http_client = httpx.Client(follow_redirects=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2de99b93", "metadata": {}, "outputs": [], "source": ["import os\n", "import openai\n", "from openai import OpenAI\n", "from rich.console import Console\n", "\n", "http_client = httpx.Client(\n", "    follow_redirects=True\n", ")\n", "console = Console()\n", "\n", "def custom_print(info):\n", "    console.print(info)\n", "\n", "# api_key = \"sk-5617b89342844050a48268b477327b44\"  # 注意保护你的API密钥\n", "api_key=\"sk-e2bad2bca73343a38f4f8d60f7435192\"\n", "base_url = \"https://api.deepseek.com/v1\"\n", "\n", "# 使用自定义HTTP客户端创建OpenAI实例\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")\n", "\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"你好呀\",\n", "        }\n", "    ]\n", ")\n", "\n", "# 打印回复内容\n", "print(type(response))\n", "custom_print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "3ac6f753", "metadata": {}, "outputs": [], "source": ["custom_print(response.choices)"]}, {"cell_type": "code", "execution_count": null, "id": "2c03c9bf", "metadata": {}, "outputs": [], "source": ["custom_print(response.choices[0])"]}, {"cell_type": "code", "execution_count": null, "id": "1938aaec", "metadata": {}, "outputs": [], "source": ["custom_print(response.choices[0].message)"]}, {"cell_type": "code", "execution_count": null, "id": "37ee3f7c", "metadata": {}, "outputs": [], "source": ["custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "1959ba76", "metadata": {}, "source": ["# 2.参数使用"]}, {"cell_type": "markdown", "id": "d530bb4a", "metadata": {}, "source": ["## 2.1 presence_penalty 参数"]}, {"cell_type": "code", "execution_count": null, "id": "7171f573", "metadata": {}, "outputs": [], "source": ["import os\n", "import openai\n", "from openai import OpenAI\n", "from rich.console import Console\n", "\n", "api_key = \"sk-5617b89342844050a48268b477327b44\"\n", "base_url = \"https://api.deepseek.com/v1\"\n", "\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")\n", "\n", "# 示例问题：要求模型生成不同类型的水果清单\n", "prompt = \"请列出10个水果种类。\"\n", "\n", "# 使用不同的presence_penalty值进行对比\n", "def get_response(presence_penalty_value):\n", "\n", "    response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        presence_penalty=presence_penalty_value\n", "    )\n", "    \n", "    return response.choices[0].message.content\n", "\n", "# 使用较高的presence_penalty，鼓励更多样化的回答\n", "diverse_response = get_response(2.0)\n", "print(\"使用高presence_penalty (2.0)的回答：\")\n", "print(diverse_response)\n", "print(\"\\n\" + \"-\"*50 + \"\\n\")\n", "\n", "# 使用较低的presence_penalty，可能会更聚焦于常见水果\n", "focused_response = get_response(-2.0)\n", "print(\"使用低presence_penalty (-2.0)的回答：\")\n", "print(focused_response)"]}, {"cell_type": "code", "execution_count": null, "id": "e634feaa", "metadata": {}, "outputs": [], "source": ["prompt = \"请给我编写一首诗，和冬天有关的\"\n", "\n", "def get_response(presence_penalty_value):\n", "    response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        presence_penalty=presence_penalty_value\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "diverse_response = get_response(2.0)\n", "print(\"使用高presence_penalty (2.0)的回答：\")\n", "print(diverse_response)\n", "print(\"\\n\" + \"-\"*50 + \"\\n\")\n", "\n", "focused_response = get_response(-2.0)\n", "print(\"使用低presence_penalty (-2.0)的回答：\")\n", "print(focused_response)"]}, {"cell_type": "markdown", "id": "02e067ac", "metadata": {}, "source": ["## 2.2 n参数"]}, {"cell_type": "code", "execution_count": null, "id": "a0ea45a4", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"生命只有一次，活着的意义是什么\",\n", "        }\n", "    ],\n", "    max_tokens=150,\n", "    n=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6d089356", "metadata": {}, "outputs": [], "source": ["custom_print(response.choices)"]}, {"cell_type": "code", "execution_count": null, "id": "38f84ccb", "metadata": {}, "outputs": [], "source": ["custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "cfde210e", "metadata": {}, "source": ["## 2.3 stop参数"]}, {"cell_type": "code", "execution_count": null, "id": "afa148c8", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"生成10个关于人工智能的面试问题\"}\n", "    ],\n", "    stop=[\"6.\"],  # 当生成到\"6.\"时停止生成内容\n", "    max_tokens=300\n", ")\n", "\n", "custom_print(response)"]}, {"cell_type": "markdown", "id": "e2bc82d2", "metadata": {}, "source": ["## 2.4 stream参数"]}, {"cell_type": "code", "execution_count": null, "id": "5717ea24", "metadata": {}, "outputs": [], "source": ["\"\" is not None"]}, {"cell_type": "code", "execution_count": null, "id": "8d4fff57", "metadata": {}, "outputs": [], "source": ["# 创建流式响应\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[{\"role\": \"user\", \"content\": \"写一首关于春天的诗\"}],\n", "    stream=True  # 启用流式输出\n", ")\n", "\n", "# <openai.Stream object at 0x000001F86C75FE10>\n", "print(response)\n", "\n", "# 逐步处理和显示生成的内容\n", "for chunk in response:\n", "    # custom_print(chunk)\n", "    if len(chunk.choices) > 0 and chunk.choices[0].delta.content is not None:\n", "        print(chunk.choices[0].delta.content, end=\"\")"]}, {"cell_type": "markdown", "id": "210e8b1b", "metadata": {}, "source": ["## 2.5 temperature参数"]}, {"cell_type": "code", "execution_count": null, "id": "2437424e", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"第一次到四川成都怎么玩儿\",\n", "        }\n", "    ],\n", "    n=1,\n", "    max_tokens=500,\n", "    temperature=0.2\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "3b2e6860", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"第一次到四川成都怎么玩儿\",\n", "        }\n", "    ],\n", "    n=1,\n", "    max_tokens=500,\n", "    temperature=0.6\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "afb83ed1", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"第一次到四川成都怎么玩儿\",\n", "        }\n", "    ],\n", "    n=1,\n", "    max_tokens=500,\n", "    temperature=1.5,\n", "    \n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "90540f93", "metadata": {}, "source": ["# 3.消息的综合应用"]}, {"cell_type": "markdown", "id": "7e0c1efc", "metadata": {}, "source": ["## 3.1 消息的组成"]}, {"cell_type": "code", "execution_count": null, "id": "30a9f5f1", "metadata": {}, "outputs": [], "source": ["import os\n", "import openai\n", "from openai import OpenAI\n", "from rich.console import Console\n", "\n", "api_key = \"sk-5617b89342844050a48268b477327b44\"\n", "base_url = \"https://api.deepseek.com/v1\"\n", "\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "90b1f6f0", "metadata": {}, "outputs": [], "source": ["messages = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"在一个寒冷的冬天，天空中飘着雪花，我坐在户外开始烧烤，一边吃一边烤火。针对此情此景，给我编写一篇散文，100字左右吧\"\n", "        }\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0d0438c2", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", "    temperature=0.2\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "75b5e685", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", "    temperature=1.2\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "9747b39a", "metadata": {}, "source": ["## 3.2 大模型回复用户问题机制"]}, {"cell_type": "code", "execution_count": null, "id": "dda10657", "metadata": {}, "outputs": [], "source": ["messages1 = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"在一个炎热的夏日，我吹着空调\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"一边吹空调，一边吃雪糕\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"针对此情此景，给我编写一篇散文，100字左右吧\"\n", "        }\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "de1e277b", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages1,\n", "    temperature=1.0\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "f7907657", "metadata": {}, "outputs": [], "source": ["messages_test = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"我上一个问题，问了什么\"\n", "        }\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e5402187", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages_test,\n", "    temperature=1.0\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "c2b17fa3", "metadata": {}, "outputs": [], "source": ["messages2 = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"请你告诉我，近期什么歌曲最好听\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"请你告诉我，近期什么电影最好看\"\n", "        },\n", "]\n", "\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages2,\n", "    temperature=1.0,\n", "    max_tokens=200\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "292e1593", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages2,\n", "    temperature=1.0,\n", "    max_tokens=1000\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "aacf9608", "metadata": {}, "source": ["## 3.3 通用背景信息设置"]}, {"cell_type": "code", "execution_count": null, "id": "102fa6c7", "metadata": {}, "outputs": [], "source": ["system1 = \"a + d = 1\"\n", "system2 = \"q + e = 2\"\n", "system3 = \"z + c = 3 \"\n", "messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system1\n", "        },\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system2\n", "        },\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system3\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"a + d = ？\"\n", "\n", "        }\n", "]\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", "    max_tokens=1000\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "4a5c65f0", "metadata": {}, "outputs": [], "source": ["system1 = \"你是一名脱口秀演员\"\n", "messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system1\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"给我讲个故事，不超过200字\"\n", "\n", "        }\n", "]\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", "    max_tokens=1000\n", ")\n", "custom_print(response.choices[0].message.content)"]}], "metadata": {"kernelspec": {"display_name": "zjou-2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}