# 共享充电宝数据仓库项目 .gitignore

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# 数据库相关
*.db
*.sqlite
*.sqlite3

# 日志文件
*.log
logs/

# 配置文件（包含敏感信息）
config.ini
.env
secrets.json
database.conf

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 备份文件
*.bak
*.backup
backup/

# 大数据相关
# Hadoop
*.jar
hadoop-logs/
.hadoop/

# Hive
metastore_db/
derby.log
warehouse/

# Spark
spark-warehouse/
.spark-warehouse/

# 数据文件（可选，根据需要调整）
# data/*.csv  # 如果不想提交原始数据文件
# data/*.json
# data/*.parquet

# 输出文件
output/
results/
reports/

# 缓存文件
.cache/
*.cache

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 文档生成
docs/_build/
site/

# 测试覆盖率
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 打包文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 项目特定
# 如果有敏感的数据库连接信息，可以忽略
# scripts/config.py
# scripts/database_config.py

# 忽略文件
/Hive-record
/data/ADS/brand_revenue_summary
/data/ADS/region_usage_summary
/data/ADS/time_summary
/data/ADS/user_behavior_summary
/data/ADS/region_heatmap
/data/cleaned_data/order_table
/data/cleaned_data/region_table
/data/cleaned_data/time_table
/data/cleaned_data/user_table