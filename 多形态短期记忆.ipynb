{"cells": [{"cell_type": "markdown", "id": "3419e8f6", "metadata": {}, "source": ["# 1.前置代码"]}, {"cell_type": "code", "execution_count": 39, "id": "4e2d645f", "metadata": {}, "outputs": [], "source": ["from rich.console import Console\n", "console = Console()\n", "def custom_print(info):\n", "    console.print(info)"]}, {"cell_type": "markdown", "id": "3effdbce", "metadata": {}, "source": ["需要通过 pip install tiktoken 安装 tiktoken"]}, {"cell_type": "code", "execution_count": 40, "id": "399f2dc6", "metadata": {}, "outputs": [], "source": ["import os\n", "import io\n", "import json\n", "import httpx\n", "import inspect\n", "import numpy as np\n", "import pandas as pd\n", "import pymysql\n", "import openai\n", "import tiktoken\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 41, "id": "83a9cbc0", "metadata": {}, "outputs": [], "source": ["api_key = \"sk-e2bad2bca73343a38f4f8d60f7435192\"\n", "base_url = \"https://api.deepseek.com/v1\"\n", "http_client = httpx.Client(follow_redirects=True)\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")"]}, {"cell_type": "code", "execution_count": 42, "id": "9fdea827", "metadata": {}, "outputs": [], "source": ["def get_user_info(sql,username='root',password='',db='user_info'):\n", "    \"\"\"\n", "    当前函数，用于查询指定数据库下的相关表中的数据\n", "    ：param sql：参数为必要参数,字符串类型的SQL语句,\n", "    ：param username：参数为可选参数,代表:用户名,字符串类型,\n", "    ：param password：参数为可选参数,代表:用户密码,字符串类型,\n", "    ：param db：参数为可选参数,代表:需要连接的数据库名字,字符串类型,\n", "    ：return:当前SQL语句执行完的查询结果\n", "    \"\"\"\n", "    conn = pymysql.connect(\n", "        host = 'localhost',\n", "        user = username,\n", "        password = password,\n", "        db = db,\n", "        charset = 'utf8'\n", "    )\n", "    try: \n", "        with conn.cursor() as cursor:\n", "            cursor.execute(sql)\n", "            results = cursor.fetchall()\n", "    finally:\n", "        cursor.close()\n", "        conn.close()\n", "    column_names = [desc[0] for desc in cursor.description]\n", "    df = pd.DataFrame(results, columns = column_names)\n", "    return df.to_json(orient = 'records') "]}, {"cell_type": "code", "execution_count": 43, "id": "378cb691", "metadata": {}, "outputs": [], "source": ["tools_list = {\"get_user_info\": get_user_info}\n", "tools = [{'type': 'function',\n", "         'function': {\n", "            'name': 'get_user_info',\n", "            'description': '当前函数，用于查询指定数据库下的相关表中的数据。输入参数为字符串类型的SQL语句，输出为当前SQL语句执行完的查询结果',\n", "            'parameters': {'type': 'object',\n", "                           'properties': {'sql': {'description': '必要参数，字符串类型的SQL语句','type': 'string'},\n", "                                          'username': {'description': '用户名,字符串类型','type': 'string'},\n", "                                          'password': {'description': '用户密码,字符串类型','type': 'string'},\n", "                                          'db': {'description': '需要连接的数据库名字,字符串类型','type': 'string'}\n", "                                         },\n", "         'required': ['sql']},\n", "        }}]"]}, {"cell_type": "code", "execution_count": 44, "id": "7c2bc6bb", "metadata": {}, "outputs": [], "source": ["def auto_call(client,messages,tools=None,model='deepseek-chat'):\n", "    if tools == None :\n", "        response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages\n", "        )\n", "        return response.choices[0].message.content\n", "    else :\n", "        #函数第一次调用\n", "        first_response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages,\n", "            tools = tools,\n", "            tool_choice=\"auto\",\n", "        )\n", "        message_call = first_response.choices[0].message\n", "        tools_calls = message_call.tool_calls\n", "        if tools_calls != None:\n", "            tool_call_id = tools_calls[0].id\n", "            func_name = first_response.choices[0].message.tool_calls[0].function.name\n", "            func_args = json.loads(tools_calls[0].function.arguments)['sql']\n", "            func_result = tools_list[func_name](func_args)\n", "            messages.append(message_call)\n", "            messages.append(\n", "                {\n", "                    \"role\":\"tool\",\n", "                    \"tool_call_id\":tool_call_id,\n", "                    \"name\":func_name,\n", "                    \"content\":func_result\n", "                }\n", "            )\n", "            #二次调用\n", "            second_response = client.chat.completions.create(\n", "                model=model,\n", "                messages=messages\n", "            )\n", "            custom_print(\"【【【经过二次调用场景】】】\")\n", "            custom_print(messages)\n", "            return second_response.choices[0].message.content\n", "        else:\n", "            custom_print(\"【【【未经过二次调用场景】】】\")\n", "            custom_print(messages)\n", "            return first_response.choices[0].message.content"]}, {"cell_type": "markdown", "id": "afc26b97", "metadata": {}, "source": ["# 2.多轮对话核心流程"]}, {"cell_type": "code", "execution_count": 45, "id": "9e9aaab4", "metadata": {}, "outputs": [], "source": ["with open(r'C:\\Users\\<USER>\\Desktop\\暑期实训2周\\数据预处理数据集\\data.md', 'r', encoding='utf-8') as f:\n", "    system_info = f.read()"]}, {"cell_type": "code", "execution_count": 46, "id": "05fe3240", "metadata": {}, "outputs": [], "source": ["def auto_chat(client, \n", "              user_input,\n", "              system_base_info=\"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\",\n", "              system_extend_info=\"\",\n", "              tools = None,\n", "              model=\"deepseek-chat\"):\n", "    system_message = {\"role\":\"system\",\"content\":system_base_info + \"%s\" %system_extend_info}\n", "    user_message={\"role\":\"user\",\"content\":user_input}\n", "    messages = []\n", "    messages.append(system_message)\n", "    messages.append(user_message)\n", "    while True:           \n", "        result = auto_call(client,messages,tools,model=model)\n", "        print(f\"模型回复: {result}\")\n", "        user_input = input(\"您还有想要提问的问题？(输入0结束对话): \")\n", "        messages.clear()\n", "        messages.append(system_message)\n", "        messages.append({\"role\":\"user\",\"content\":user_input})\n", "        if user_input == \"0\":\n", "            break"]}, {"cell_type": "code", "execution_count": null, "id": "1900bcb7", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">【【【经过二次调用场景】】】\n", "</pre>\n"], "text/plain": ["【【【经过二次调用场景】】】\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，0 表示乘客没有在泰坦尼克号事故中幸存，1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">性别，只有 “male”（男性）和 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'我来帮你查询user_base_info表中所有人员的平均年龄。'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_f7cbeb24-4d45-4af9-b226-83f67ab12c14'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\": \"SELECT ROUND(AVG(age)) AS average_age FROM user_base_info WHERE age IS NOT </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">NULL\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_f7cbeb24-4d45-4af9-b226-83f67ab12c14'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"average_age\":30.0}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# \u001b[0m\n", "\u001b[32m表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，\u001b[0m\n", "\u001b[32m需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mpassenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mage 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch \u001b[0m\n", "\u001b[32m字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked \u001b[0m\n", "\u001b[32m字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 \u001b[0m\n", "\u001b[32msurvived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 \u001b[0m\n", "\u001b[32mID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量\u001b[0m\n", "\u001b[32m，0 表示乘客没有在泰坦尼克号事故中幸存，1 \u001b[0m\n", "\u001b[32m表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 \u001b[0m\n", "\u001b[32m代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 \u001b[0m\n", "\u001b[32m代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存\u001b[0m\n", "\u001b[32m概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测\u001b[0m\n", "\u001b[32m生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” \u001b[0m\n", "\u001b[32m等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理\u001b[0m\n", "\u001b[32m性别，只有 “male”（男性）和 \u001b[0m\n", "\u001b[32m“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” \u001b[0m\n", "\u001b[32m的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，\u001b[0m\n", "\u001b[32m不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在\u001b[0m\n", "\u001b[32m灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直\u001b[0m\n", "\u001b[32m系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如\u001b[0m\n", "\u001b[32m，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`\u001b[0m\n", "\u001b[32m``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp \u001b[0m\n", "\u001b[32m类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩\u001b[0m\n", "\u001b[32m子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预\u001b[0m\n", "\u001b[32m测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare\u001b[0m\n", "\u001b[32m（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经\u001b[0m\n", "\u001b[32m济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置\u001b[0m\n", "\u001b[32m，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口\u001b[0m\n", "\u001b[32m，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并\u001b[0m\n", "\u001b[32m且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m'我来帮你查询user_base_info表中所有人员的平均年龄。'\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_f7cbeb24-4d45-4af9-b226-83f67ab12c14'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\": \"SELECT ROUND\u001b[0m\u001b[32m(\u001b[0m\u001b[32mAVG\u001b[0m\u001b[32m(\u001b[0m\u001b[32mage\u001b[0m\u001b[32m)\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS average_age FROM user_base_info WHERE age IS NOT \u001b[0m\n", "\u001b[32mNULL\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_f7cbeb24-4d45-4af9-b226-83f67ab12c14'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"average_age\":30.0\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["模型回复: user_base_info数据表中所有人员的平均年龄是30岁（结果已四舍五入保留整数）。\n"]}], "source": ["auto_chat(client,user_input=\"请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？\",system_extend_info=system_info,tools=tools)"]}, {"cell_type": "markdown", "id": "b6bef960", "metadata": {}, "source": ["# 3.多形态短期记忆"]}, {"cell_type": "markdown", "id": "a85bd80b", "metadata": {}, "source": ["```\n", "threshold：阈值，用于决定消息的保留量，默认为 0。\n", "counts_or_tokens：当前消息的计数或令牌数，默认为 0。\n", "use_token：布尔值，True 表示按令牌数控制，False 表示按计数控制，默认为 False。\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "6ddd1d12", "metadata": {}, "outputs": [], "source": ["from dashscope import get_tokenizer\n", "class ShortMemory:\n", "    def __init__(self,\n", "                 messages=None,\n", "                 threshold=0,\n", "                 counts_or_tokens=0,\n", "                 use_token=False,\n", "                 model=\"qwen-turbo\"):\n", "        if messages is None:\n", "            self.messages = []\n", "        else:\n", "            self.messages = messages\n", "        self.threshold = threshold\n", "        self.counts_or_tokens = counts_or_tokens\n", "        self.use_token = use_token\n", "        self.model = model\n", "        if self.use_token:\n", "            self.encoding = get_tokenizer(model)\n", "\n", "    def control_based_on_condition(self):\n", "        if self.use_token:\n", "            # 按令牌数控制的逻辑\n", "            while self.counts_or_tokens >= self.threshold:\n", "                if(len(self.messages)>=2):\n", "                    if type(self.messages[1]) is not dict and \"tool_call_id\" in self.messages[2]:\n", "                        self.messages.pop(1)\n", "                        self.messages.pop(1)\n", "                    self.counts_or_tokens -= len(self.encoding.encode(self.messages.pop(1)[\"content\"]))\n", "        else:\n", "            # 按计数控制的逻辑\n", "            if self.counts_or_tokens > self.threshold:\n", "                diff = self.counts_or_tokens - self.threshold\n", "                self.counts_or_tokens -= diff\n", "                del self.messages[1:(1 + diff)]\n", "                if len(self.messages) >= 2 and \"tool_call_id\" in self.messages[1]:\n", "                    del self.messages[1]\n", "                    self.counts_or_tokens -= 1 \n", "                    \n", "    def append_message(self, message: dict):\n", "        if self.use_token:\n", "            # 如果按令牌数控制，计算消息内容的令牌长度并累加\n", "            self.counts_or_tokens += len(self.encoding.encode(message['content']))\n", "        else:\n", "            # 如果按计数控制，简单计数加1\n", "            self.counts_or_tokens += 1\n", "        self.messages.append(message)\n", "        self.control_based_on_condition()    \n", "\n", "    def get_messages(self):\n", "        return self.messages                   "]}, {"cell_type": "code", "execution_count": null, "id": "d076d8e7", "metadata": {}, "outputs": [], "source": ["def auto_chat(client,\n", "              user_input,\n", "              system_base_info=\"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\",\n", "              system_extend_info=\"\",\n", "              tools = None,\n", "              model=\"deepseek-chat\"):\n", "    system_message = {\"role\":\"system\",\"content\":system_base_info + \"%s\" %system_extend_info}\n", "    user_message={\"role\":\"user\",\"content\":user_input}\n", "    global memory\n", "    memory = ShortMemory(threshold=1700,use_token=True)\n", "    memory.append_message(system_message)\n", "    memory.append_message(user_message)\n", "    while True:           \n", "        result = auto_call(client,memory.get_messages(),tools=tools,model=model)\n", "        print(f\"模型回复: {result}\")\n", "        memory.append_message({\"role\": \"assistant\", \"content\": result})\n", "        print(memory.get_messages())\n", "        user_input = input(\"您还有其他问题？(输入0结束对话): \")\n", "        memory.append_message({\"role\":\"user\",\"content\":user_input})\n", "        if user_input == \"0\":\n", "            break"]}, {"cell_type": "markdown", "id": "094fe7c0", "metadata": {}, "source": ["# 4.测试"]}, {"cell_type": "markdown", "id": "e974cdbc", "metadata": {}, "source": ["## 4.1 基于数量的测试"]}, {"cell_type": "code", "execution_count": null, "id": "fcc897d8", "metadata": {}, "outputs": [], "source": ["memory = ShortMemory(threshold=4)"]}, {"cell_type": "code", "execution_count": null, "id": "7e5fa3b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'user': '我问你第一个问题，你是谁？'}]\n", "----------------------------------------------\n", "[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第二个问题，重新回答你是谁？'}]\n", "----------------------------------------------\n", "[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第二个问题，重新回答你是谁？'}, {'user': '我问你第三个问题，你是谁？'}]\n"]}], "source": ["memory.append_message(message={\"user\":\"我问你第一个问题，你是谁？\"})\n", "print(memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "memory.append_message(message={\"user\":\"我问你第二个问题，重新回答你是谁？\"})\n", "print(memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "memory.append_message(message={\"user\":\"我问你第三个问题，你是谁？\"})\n", "print(memory.get_messages())"]}, {"cell_type": "code", "execution_count": null, "id": "f3c6f12a", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第二个问题，重新回答你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第四个问题，重新回答你是谁？'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'user'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'user'\u001b[0m: \u001b[32m'我问你第二个问题，重新回答你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'user'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'user'\u001b[0m: \u001b[32m'我问你第四个问题，重新回答你是谁？'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["memory.append_message(message={\"user\":\"我问你第四个问题，重新回答你是谁？\"})\n", "custom_print(memory.get_messages())"]}, {"cell_type": "code", "execution_count": null, "id": "f1955654", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第四个问题，重新回答你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第五个问题，重新回答你是谁？'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'user'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'user'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'user'\u001b[0m: \u001b[32m'我问你第四个问题，重新回答你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'user'\u001b[0m: \u001b[32m'我问你第五个问题，重新回答你是谁？'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["memory.append_message(message={\"user\":\"我问你第五个问题，重新回答你是谁？\"})\n", "custom_print(memory.get_messages())"]}, {"cell_type": "markdown", "id": "d2ffff7d", "metadata": {}, "source": ["## 4.2 基于Token的测试"]}, {"cell_type": "markdown", "id": "a23f4789", "metadata": {}, "source": ["第二个问题和第三个问题全部被弹出的版本"]}, {"cell_type": "code", "execution_count": null, "id": "19188a32", "metadata": {}, "outputs": [], "source": ["memory = ShortMemory(threshold=110 , use_token=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e3361166", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'content': '我问你第一个问题，你是谁？'}]\n", "----------------------------------------------\n", "[{'content': '我问你第一个问题，你是谁？'}, {'content': '我问你第二个问题，重新回答你是谁？'}]\n", "----------------------------------------------\n", "[{'content': '我问你第一个问题，你是谁？'}, {'content': '我问你第二个问题，重新回答你是谁？'}, {'content': '我问你第三个问题，你是谁？'}]\n"]}], "source": ["memory.append_message(message={\"content\":\"我问你第一个问题，你是谁？\"})\n", "print(memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "memory.append_message(message={\"content\":\"我问你第二个问题，重新回答你是谁？\"})\n", "print(memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "memory.append_message(message={\"content\":\"我问你第三个问题，你是谁？\"})\n", "print(memory.get_messages())"]}, {"cell_type": "code", "execution_count": null, "id": "c55692f5", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m29\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(memory.counts_or_tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "bb9bf4d6", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第二个问题，重新回答你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第二个问题，重新回答你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">105</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m105\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["memory.append_message(message={\"content\":\"5月19日下午，华为在成都召开nova 14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。\"})\n", "custom_print(memory.get_messages())\n", "custom_print(memory.counts_or_tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "45ddc5c1", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'作为华为史上最大折叠屏，华为MateBook Fold采用轻薄折叠设计'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'作为华为史上最大折叠屏，华为MateBook Fold采用轻薄折叠设计'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">101</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m101\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["memory.append_message(message={\"content\":\"作为华为史上最大折叠屏，华为MateBook Fold采用轻薄折叠设计\"})\n", "custom_print(memory.get_messages())\n", "custom_print(memory.counts_or_tokens)"]}, {"cell_type": "markdown", "id": "2477afd7", "metadata": {}, "source": ["只弹出第二个问题的版本"]}, {"cell_type": "code", "execution_count": null, "id": "7bb269d6", "metadata": {}, "outputs": [], "source": ["memory = ShortMemory(threshold=110 , use_token=True)"]}, {"cell_type": "code", "execution_count": null, "id": "87525d8d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'content': '我问你第一个问题，你是谁？'}]\n", "----------------------------------------------\n", "[{'content': '我问你第一个问题，你是谁？'}, {'content': '我问你第二个问题，重新回答你是谁？'}]\n", "----------------------------------------------\n", "[{'content': '我问你第一个问题，你是谁？'}, {'content': '我问你第二个问题，重新回答你是谁？'}, {'content': '我问你第三个问题，你是谁？'}]\n"]}], "source": ["memory.append_message(message={\"content\":\"我问你第一个问题，你是谁？\"})\n", "print(memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "memory.append_message(message={\"content\":\"我问你第二个问题，重新回答你是谁？\"})\n", "print(memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "memory.append_message(message={\"content\":\"我问你第三个问题，你是谁？\"})\n", "print(memory.get_messages())"]}, {"cell_type": "code", "execution_count": null, "id": "71e55925", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["29\n"]}], "source": ["print(memory.counts_or_tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "f190094a", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第二个问题，重新回答你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第二个问题，重新回答你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">105</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m105\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["memory.append_message(message={\"content\":\"5月19日下午，华为在成都召开nova 14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。\"})\n", "custom_print(memory.get_messages())\n", "custom_print(memory.counts_or_tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "6fc36940", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'作为华为史上最大折叠屏，华为采用'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'作为华为史上最大折叠屏，华为采用'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">103</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m103\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["memory.append_message(message={\"content\":\"作为华为史上最大折叠屏，华为采用\"})\n", "custom_print(memory.get_messages())\n", "custom_print(memory.counts_or_tokens)"]}, {"cell_type": "markdown", "id": "6cca1f24", "metadata": {}, "source": ["测试对象和tools版本"]}, {"cell_type": "markdown", "id": "9d005ad9", "metadata": {}, "source": ["```\n", "{'role': 'tool',\n", "  'tool_call_id': 'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3',\n", "  'name': 'get_user_info',\n", "  'content': '[{\"survived\":\"0\",\"count\":1647},{\"survived\":\"1\",\"count\":1026}]'}]\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "d0aef46d", "metadata": {}, "outputs": [], "source": ["class Test :\n", "    def __init__(self):\n", "        print(\"对象已经被创建了\")\n", "    def print_test():\n", "        print(\"这个测试类还是挺好用的\")"]}, {"cell_type": "code", "execution_count": null, "id": "c18172be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["对象已经被创建了\n"]}], "source": ["test = Test()"]}, {"cell_type": "code", "execution_count": null, "id": "3be5801e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'content': '我问你第一个问题，你是谁？'}]\n", "----------------------------------------------\n", "[{'content': '我问你第一个问题，你是谁？'}, {'content': '我问你第二个问题，重新回答你是谁？'}]\n", "----------------------------------------------\n", "[{'content': '我问你第一个问题，你是谁？'}, {'content': '我问你第二个问题，重新回答你是谁？'}, {'content': '我问你第三个问题，你是谁？'}]\n"]}], "source": ["memory = ShortMemory(threshold=110 , use_token=True)\n", "memory.append_message(message={\"content\":\"我问你第一个问题，你是谁？\"})\n", "print(memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "memory.append_message(message={\"content\":\"我问你第二个问题，重新回答你是谁？\"})\n", "print(memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "memory.append_message(message={\"content\":\"我问你第三个问题，你是谁？\"})\n", "print(memory.get_messages())"]}, {"cell_type": "code", "execution_count": null, "id": "a38557fe", "metadata": {}, "outputs": [], "source": ["memory.messages.append(memory)"]}, {"cell_type": "code", "execution_count": null, "id": "4f932714", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'content': '我问你第一个问题，你是谁？'},\n", " {'content': '我问你第二个问题，重新回答你是谁？'},\n", " {'content': '我问你第三个问题，你是谁？'},\n", " <__main__.Short<PERSON><PERSON>ory at 0x19425ad38f0>]"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["memory.get_messages()"]}, {"cell_type": "code", "execution_count": null, "id": "32b559d2", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'content': '我问你第一个问题，你是谁？'},\n", " {'content': '我问你第二个问题，重新回答你是谁？'},\n", " {'content': '我问你第三个问题，你是谁？'},\n", " <__main__.Short<PERSON><PERSON>ory at 0x19425ad38f0>,\n", " {'tool_call_id': 'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'}]"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["tool_dic = {'tool_call_id': 'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'}\n", "memory.messages.append(tool_dic)\n", "memory.get_messages()"]}, {"cell_type": "code", "execution_count": null, "id": "4c5723c1", "metadata": {}, "outputs": [{"data": {"text/plain": ["29"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["memory.counts_or_tokens"]}, {"cell_type": "code", "execution_count": null, "id": "1737d930", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第二个问题，重新回答你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">__main__.ShortMemory</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x0000019425AD38F0</span><span style=\"font-weight: bold\">&gt;</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第二个问题，重新回答你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m<\u001b[0m\u001b[1;95m__main__.ShortMemory\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x0000019425AD38F0\u001b[0m\u001b[1m>\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">105</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m105\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["memory.append_message(message={\"content\":\"5月19日下午，华为在成都召开nova 14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。\"})\n", "custom_print(memory.get_messages())\n", "custom_print(memory.counts_or_tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "3060eae4", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">__main__.ShortMemory</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x0000019425AD38F0</span><span style=\"font-weight: bold\">&gt;</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m<\u001b[0m\u001b[1;95m__main__.ShortMemory\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x0000019425AD38F0\u001b[0m\u001b[1m>\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">99</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m99\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["memory.append_message(message={\"content\":\"1+1等于几\"})\n", "custom_print(memory.get_messages())\n", "custom_print(memory.counts_or_tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "bc4bf7e1", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">__main__.ShortMemory</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x0000019425AD38F0</span><span style=\"font-weight: bold\">&gt;</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m<\u001b[0m\u001b[1;95m__main__.ShortMemory\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x0000019425AD38F0\u001b[0m\u001b[1m>\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(memory.get_messages())"]}, {"cell_type": "code", "execution_count": null, "id": "fc0b01db", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">__main__.ShortMemory</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x0000019425AD38F0</span><span style=\"font-weight: bold\">&gt;</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m<\u001b[0m\u001b[1;95m__main__.ShortMemory\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x0000019425AD38F0\u001b[0m\u001b[1m>\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["104\n"]}], "source": ["memory.append_message(message={\"content\":\"1+1等于几\"})\n", "custom_print(memory.get_messages())\n", "print(memory.counts_or_tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "553190cd", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第三个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">__main__.ShortMemory</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x0000019425AD38F0</span><span style=\"font-weight: bold\">&gt;</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+2等于几'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第三个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m<\u001b[0m\u001b[1;95m__main__.ShortMemory\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x0000019425AD38F0\u001b[0m\u001b[1m>\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+2等于几'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["109\n"]}], "source": ["memory.append_message(message={\"content\":\"1+2等于几\"})\n", "custom_print(memory.get_messages())\n", "print(memory.counts_or_tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "1f663bc3", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">__main__.ShortMemory</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x0000019425AD38F0</span><span style=\"font-weight: bold\">&gt;</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'5月19日下午，华为在成都召开nova </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+2等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+3等于几'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m<\u001b[0m\u001b[1;95m__main__.ShortMemory\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x0000019425AD38F0\u001b[0m\u001b[1m>\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'chatcmpl-8s9xZtJnOn9sBImV1RmYrgvjfr1B3'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'5月19日下午，华为在成都召开nova \u001b[0m\n", "\u001b[32m14系列及鸿蒙电脑新品发布会。会上，华为常务董事、终端BG董事长余承东连发两款鸿蒙电脑，补齐华为在电脑操作系统上的生态\u001b[0m\n", "\u001b[32m缺口。至此，鸿蒙全面覆盖了手机、电脑、手表、大屏、平板、座舱等全场景终端设备。'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+2等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+3等于几'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["105\n"]}], "source": ["memory.append_message(message={\"content\":\"1+3等于几\"})\n", "custom_print(memory.get_messages())\n", "print(memory.counts_or_tokens)"]}, {"cell_type": "code", "execution_count": null, "id": "05d1ee9d", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'我问你第一个问题，你是谁？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+1等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+2等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+3等于几'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'1+3等于几呀'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'我问你第一个问题，你是谁？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+1等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+2等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+3等于几'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'content'\u001b[0m: \u001b[32m'1+3等于几呀'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["35\n"]}], "source": ["memory.append_message(message={\"content\":\"1+3等于几呀\"})\n", "custom_print(memory.get_messages())\n", "print(memory.counts_or_tokens)"]}, {"cell_type": "markdown", "id": "cfb0784d", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "3d92fab1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "zjou-2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}