{"cells": [{"cell_type": "markdown", "id": "58bb386e", "metadata": {}, "source": ["# 1.2.2 Numpy基础知识补充"]}, {"cell_type": "code", "execution_count": 1, "id": "5813b2f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n"]}], "source": ["# 创建一维数组\n", "import numpy as np\n", "print(np.array([1,2,3,4,5]))\n", "# 类(属性[变量]、方法[函数])\n", "# print(type(np.array([1,2,3,4,5])))\n", "# arr = np.array([1,2,3,4,5])"]}, {"cell_type": "code", "execution_count": 2, "id": "f0ce558d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[90 91]\n", " [93 94]\n", " [92 98]]\n"]}], "source": ["# 创建二维数组\n", "arr = np.array([[90, 91], [93, 94], [92, 98]]) \n", "print(arr)"]}, {"cell_type": "code", "execution_count": 3, "id": "5088a7e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([93, 94])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取下标为1的数据\n", "arr[1]"]}, {"cell_type": "code", "execution_count": 4, "id": "0ebf6e21", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[93, 94],\n", "       [92, 98]])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取下标为1和2的数据   [1,3)\n", "arr[1:3]"]}, {"cell_type": "code", "execution_count": 5, "id": "345f30c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[93, 94],\n", "       [92, 98]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["arr[1:]"]}, {"cell_type": "code", "execution_count": 6, "id": "ea186d9e", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[90, 91],\n", "       [93, 94],\n", "       [92, 98]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取所有行的数据\n", "arr[:]"]}, {"cell_type": "markdown", "id": "7c25501e", "metadata": {}, "source": ["**问题：如果只想获取前两行数据怎么办？**"]}, {"cell_type": "code", "execution_count": 7, "id": "83163fb4", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([90, 93, 92])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取数据第一列的数据\n", "arr[:,0]"]}, {"cell_type": "code", "execution_count": 8, "id": "9856a1df", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'a' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[8], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# 获取数据第二列的数据\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m a[:,\u001b[38;5;241m1\u001b[39m]\n", "\u001b[1;31mNameError\u001b[0m: name 'a' is not defined"]}], "source": ["# 获取数据第二列的数据\n", "a[:,1]"]}, {"cell_type": "markdown", "id": "a8acb889", "metadata": {}, "source": ["**问题：如何获取索引为1的第2列数据？**"]}, {"cell_type": "markdown", "id": "62c18806", "metadata": {}, "source": ["# 1.2.3 距离计算"]}, {"cell_type": "code", "execution_count": 21, "id": "e0b9e7a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["2.23606797749979"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np \n", "def distance(a,b):\n", "    # print(a-b)\n", "    # print((a-b)**2)\n", "    # print(np.sum((a-b)**2))\n", "    return np.sqrt(np.sum((a-b)**2))\n", "a=np.array([5,3])\n", "b=np.array([6,1])\n", "distance(a,b)"]}, {"cell_type": "markdown", "id": "67f70e13", "metadata": {}, "source": ["# 1.2.4 绘制成绩散点图"]}, {"cell_type": "code", "execution_count": 18, "id": "02a2acc0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x2b0700fe790>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制散点图\n", "import matplotlib.pyplot as plt\n", "plt.rcParams[\"font.family\"] = \"SimHei\"\n", "plt.rcParams[\"font.size\"] = 16\n", "good = np.array([[90, 91], [93, 94], [92, 98]])\n", "plt.scatter(good[:, 0], good[:, 1], color=\"r\", label=\"优等学生\")\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": 17, "id": "1219aef7", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x2b070000a60>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***************************/zP/+DyMhI9OzZE56enlU+zuTJk+Hu7o6AgAB0796dU3aJiOohhhGSRffu3WvkOF5eXpgxY0aNHIuIiOTB2TREREQkK4YRIiIikhXDCBEREcmKYYSIiIhkxTBCREREsmIYISIiIlkxjBAREZGsuM4IERFRTdLrgcREICsL8PEBgoIAC+++ov9iGCEiIqopcXHAnDnAtWv/LfPzA6KjgbD6+xb3h42PacjEzZs3y912/vx5GAwGq47zMJeSt0Sn01ksLyoqwqVLl6p83OLiYiQnJyMlJaXKxyCiRiIuDggPNw0iAHD9ell5XJw87aoHGEZI8u9//xuBgYGIiYkx2yaEwLBhw9C1a1erftxDQkLQrVs3JCcnl1vn1KlT6NWrFz7++GOr25icnIxrD/wfPT4+Hh07dsTu3bvN6u/YsQNdunTBrFmzoNfrKzy28S3F97tx4wb69OmDcePGWd1GImqE9PqyHhEhzLcZyyIjy+qRGYYRkvTs2RNOTk6YO3cuLl++bLLtl19+QWZmJtq3b49HHnmkwuMcOHAA8fHxaNKkCVxcXJCamorU1FScO3cOv/32G65cuQIAWLp0KU6fPo0mTZogNzdX+mRnZyMzM1N6a7BRUlISgoKC0LdvX6SmpkrleXl5SE9Px6RJk5Ceni6VCyGwevVq6HQ6eHh4QFXBM9u0tDS0bNkS06dPx927d6VyZ2dnAKjWW4uJqBFITDTvEbmfEEBmZlk9Mifqgby8PAFA5OXlVVq3qKhInDt3ThQVFdVCy+o3jUYjCgoKhF6vl8q++uorER4eLm7evGlS95lnnhFNmjQRGRkZQgghDAaD0Ol0Ij8/X5SWlkr1cnNzRfv27QWAcj+TJ08W+/btq7AOALF48WKzNs+bN08AEL6+vuLq1atS+YwZMwQAMWTIEKksNjZWABBDhw41+Y6WbN68WQAQ/fv3FwaDQSrPzs4WAMSAAQOsu6hW4D1K1AB9840QZZGj4s8338jd0lpl7e83w0gj9uSTT1YaCKz5nDp1SgghREFBgRgyZIgAINasWSOEECImJkYAEO+//74wGAxCq9WKw4cPi6ZNmwo/Pz9x8eJFkZ2dLeLj4wUAMXr0aJGRkSEuXLggbt26ZbHd48aNE82bNxeHDh2SynJzc8WTTz4pEhMThRBCFBcXi7Zt2wo3Nzdx7dq1Sq/FuHHjBADxww8/mJQzjBCRVeLjrQsj8fFyt7RWWfv7rRDC0gOu8p04cQIffvghfvnlF+Tm5qJ79+6YNWsWJk6caFZ3w4YNiI6OxqVLl+Dv748lS5Zg/PjxtpwOAKDRaODu7o68vDy4ublVWLe4uBhpaWlo27YtHB0dbT5XY/L1118jJycHjo6OcHBwMNkWHx+Pb775BjNnzkSvXr3M9jUYDCgtLUVhYSHGjx+PFi1aIDo6Gq+99homTZqEDRs2AAAKCgrg4+ODTp06ITExEVFRUUhISMDIkSMxZMgQdOvWTTrmc889h7179+L777/HiBEjTM6XlpaGvLw8AGWDUjMzM9GxY8dyv9uuXbvw7rvvYs6cOXjppZekcpVKhe7du5vUzc3NRatWreDp6YmMjAyTxzk5OTnw8vLCgAEDkJCQYLKfXq+HVqtFaWlppffl/XiPEjVAej3Qpk3ZYFVLP6sKRdmsmrS0RjXN1+rfb1sSTmxsrLCzsxOenp5i2rRpYvbs2SIgIEDqer+fsTvd399fREZGSv/GvHHjRluDFXtGZPDqq68KAOL8+fM27ffbb7+J4uJikZeXJ7RarTAYDCI7O1vaPnHiRAFAXLx40WQ/vV4vEhMTxfr168Wff/5pdty//e1vNdKL4+XlZXbstWvXVuuYw4cPt+ka8R4laqB27RJCoSj73N8bYizbtUvuFtY6a3+/rV5nJDs7G1OmTIGnpyd+/fVX+Pn5AQBWrlyJv/zlL9iwYQOGDRuG0aNH4/Dhw/jggw8QGBiIo0ePwtPTEwAwduxYREZGIiQkBF5eXtaemh6iX375BefPn4darYZCoZDK4+PjoVQqceTIERw7dsxkHyEESktLUVxcjLFjx6JZs2bSth49eiA9PR1t27at8LwdOnSwWN61a1e88sorZuXGgaRbtmxB8+bNrf5+95s4caJ0HKOSkhKsXr0aSqUSM2fONBvkWlxcjJiYGPj6+iI8PNxsX61Wa9K7Q0SNWFgYsHOn5XVG1qzhOiMVsDqMfPfdd7h37x7eeustKYgAZT8SCxcuxKhRo7Bnzx6MHj0an376KQAgKipKCiIAsGzZMsTGxiIuLg7Tpk2rwa8hv/q64N4///lPREdHl7t98uTJFe4/aNAgkzACAM2bN8fmzZvh6OgIe3t7k5Czbt06/Pzzz4iJiYG3t7dUrtfrUVpaWu5jC61WCwAYOHCgyf1nCycnJ+k4RmvWrMHVq1cRERGBtWvXmu2Tk5ODmJgYtG/fHmvWrKnSeYmoEQkLA0JD6+cPgoysDiPGRZ+eeOIJs23Gf1PNzs4GABw8eBBOTk549tlnTeoFBgYiICAABw4caFBhpD4vuPfOO+9g6dKlcHZ2hp1d2e2wfft2vPjii/jkk08wY8YMs32MwaGwsNAkbBq5uLhgwoQJFs/3448/AgCGDh2KNm3aWN3OLl26YNiwYXB0dERycjI++eQTqNVqODg4wN7evsJ9g4ODERwcjPDwcJPF2M6ePYu3334bKpUKb731ltVtISKqkEoFDBwodyvqFavDiPGHKicnx2ybMai0bt0aQgjcvHkTjz76KNRqtVndDh06mK1hUZ8ZF9x7cLySccG9nTvrZiC5ceMGhBCwt7eHUqnEvXv3pG0xMTFQqVQYOnQocnNzyz2G8X/rkpIS+Pj4YMuWLXB2doaDg4NJb8j9jOuA7N27t9xHdcZHQF26dJHC77Jly6Tthw8fxnfffQcnJyfY29tbXD8kOztbWi3Ww8MDwcHBWL16tUmddu3aYfv27cjOzuajFiIiGVkdRjp37gygrFt71KhRUji5e/cuoqKiAADDhw9HQUEB9Ho9WrRoYfE4np6eOH36dDWbXTdUtuCeQlG24F5oaN3roZswYQL+7//+r8I6nTp1supYAQEBSE5OxtSpU60+/8yZMyuts3jxYos9caGhoWaPW4ySkpKwZMkS7N+/X+qheXCsh5GzszNCQkKkv2fPno3MzEw4OTlBqSxbD9B4nvPnz5uswlpaWoqioiLMmDEDwcHBlX4XIiIqn9Vh5MUXX8SKFStw+PBh9OvXDy+//DLy8/MRExODtLQ0+Pv7IyQkRHpHyIMDBY3UanWl7y3RarUmPzYajcbaZtYqWxbcq2s9dq+//jomT54Me3t7KVjqdDpMnToVJSUl2LBhQ6XTTo2PaxQKBZo2bYr09HSpt6K8npEHHTp0CCNGjMCUKVOwatUqCCGkgaGurq42fadx48Zh27ZtUKvVeO2117BkyRI0bdrU6v2PHz+OM2fOwNHRUQoj4j9J886dO9i3b59U19h7M3LkSJvaSERE5qwOI+7u7oiPj8fEiRNx9OhRnDhxwmT7kiVLYGdnB5VKBZVKVe6/uZaUlKCkpKTCc61cuRLLly+3tmmyycqq2Xq16cHxPACwcOFC5Obm4v3338cLL7xg8zEDAgJs3sfFxQUA4ODgAA8PD5v3v9/vv/8OpVKJS5cuVWmQ6/Hjx83KjOuM9OvXz2ydESIiqhlWhxGgbLzHkSNHcOnSJaSlpSEhIQErVqxA586dpVkXCoUCvr6+0vtHHnTz5k2Lgx7vt3DhQsydO1f6W6PRwN/f35am1gofn5qtJ6fdu3dj1apVcHJyQkFBAVatWmXSQ3A/vV6PkpISdO3aFc8995zJttOnTyM2NhZqtRpqtbrC98EAkO6TX3/9FStWrJAef0yfPt0k3PTs2RO5ublQq9UW2wQAGRkZMBgMGDx4sMXtOp0OBQUFSEpKqpP3ExFRY2VTGDEyvixt/vz5AIAPP/zQ5EenV69e+Omnn5CXlwd3d3epvLS0FCdPnqx0DQrjD1ldFxRUNmumsgX3goJqv222+Ne//oXnn38eLVu2xPPPP4933nkHAKSBqFqtFkqlUpq1UlpaCr1ej1dffdUsjPzxxx+Ijo6Go6Oj9PinIsbHer/++itSUlJQUlICnU6HUaNGmYQRjUaD3Nxck8dKDyotLQWAcgfdlpSUoKCgQBrYSkREdURVV1WLi4sTAERISIjZto0bNwoA4oMPPjAp//bbbwUAERkZadO56vIKrPV9wb0NGzYIBwcH0bRpU3H69Gmh0+mEVquVtmdmZgoAYuLEiSb76XQ6UVJSUuGx8/PzxfTp08XevXvLrWN8J82rr75are8hhBADBgwQKpWq2se5H99NQ0RUddb+flvu765EaWkpFi1aBLVajY8//ths+wsvvAA/Pz8sW7YM33//PYCyWQ5z586FSqWq0vtp6irjgnu+vqblfn51d1ovUPZ+l1mzZmHy5Mlwc3PDzz//jJ49e8LOzs7sPTWW2NnZVbq+x/Xr1xEbG4uIiAicO3eupppOREQNTJUe03z22WdITU3F4sWL0b59e7PtTk5O2LlzJ0JCQhAaGgo3NzdpRsx7772Hxx57rHqtrmPq24J7f/75JwYOHIg//vgDrVu3xv79+xEYGFjj5wkMDMS2bdsQEhKCMWPGICkpySzoFBcXA4DVs28qUtnAaEu2bNmCQ4cOwcHBweL4FmP7Ll26hMjISLPt4r6l8ZcvX17l1WGJiBozm8PI3bt3sXz5cvj7+2PRokXl1nvyySeRkpKCqKgoHDt2DM2bN8fs2bPLHVxY39WnBfdatmyJ0aNH48CBA9i9ezeEEHj55Zctvr3XuBjaiRMnzH6MdTodtFot/va3v1mcnQOUrT0ze/Zs9O7dWzq2TqfDqlWrYDAYpBVZqzqTZuPGjbhz5w7u3LmDkydPwsnJyab9T5w4gU2bNkmruVrSrFkzFBcXY+vWrWbbSktLpRlic+fOZRghIqoChRCWhl7WLVa/ghh8PbstSktLYWdnhz/++AOPPvooHB0dK539cj9jGHnvvffw+uuv23Ru47uMFAoF+vbti2+++QatW7e29SsgKioKCxculP7++9//jk8++cTm49Qm3qNE1FhY+/tdpcc01DAYZ6UEBgaiqKioVs/9P//zP4iMjETPnj0rnepdkcmTJ8Pd3R0BAQHo3r07p+wSEdVDDCMki+7du9fIcby8vCy+zI+IiOqPKs2mISIiIqopDCNEREQkK4YRIiIikhXDCBEREcmKYYSIiIhkxTBCREREsmIYISIiIlkxjBAREZGsGEaIiIhIVgwj1KBptdoq7yuEQG5uLvLz82uwRURE9CCGEaoxf/zxB44fP17l/QsLCxEeHo4VK1aUW6dv37544YUXzMoPHjyILl264MKFC1KZTqdD+/bt8cILL0Cn09ncHo1GA09PTzz99NM270tERNbju2kaoevXr+PcuXNwdHSEWq2GUmmaSR9//HF8/PHHcHR0hL29PRQKhdkxDAYDiouL0aJFCzz//PMQQmDMmDE4c+YMli5diiVLluDevXuIiYkptx0REREmb+q1s7PDrl27UNGLpM+cOWMxWHh6euL8+fNYu3Yt1q5dCwD45z//iWvXrqF58+awt7ev9Lo8yMnJCQDQpEkTm/clIiLrMYw0QgcOHMCkSZPK3a7T6TBv3jyo1Wo4ODjAzs4Ot2/fhr29vfQKaL1ej+LiYjz11FN4/vnnoVAo8M033+DFF1/E0qVLcfLkScTExGDevHlS6DEqKSlBUVER+vfvbxJGjIGhouDg6OgIR0dHs/Lu3btjyJAh2LRpE1auXAkXFxd89tlnaNKkCZYtW2bzNQIABwcHKJVK6e3GRET0cPAxTSM0atQopKam4sqVK7h+/TqysrLw97//HQCwcOFC2NnZQa/Xo7CwELm5ubh8+TIAYOLEicjJyUFOTg7u3r2LoqIi/Pvf/5aO26lTJxw+fBgREREYMWIEXFxcAAArV65Ebm6u9Pnss88AlP3YHz9+HH5+fvjyyy8t9sA8SKVSQaVSmZRlZWWhqKgIq1evxtGjR+Hi4oJTp07hl19+wSuvvAIvLy9otVrk5+fDYDDYdK2saRMREVUP/5WvEfLw8ICHhwcuX76Mpk2b4tixY1i3bh3eeOMNi+M1bty4AQB49NFHKz22o6MjvvvuOwBAcXGxVK7RaHDnzh2TnhClUgmVSoXr16+XezytVguNRgO1Wi31rgghUFBQgMLCQjRr1gwdOnRAQUEB7O3tpUdOer0eALBu3Tp88skn0qOf8+fPo1OnTtJ/j4qKknpbynscdenSJURGRppt0+v10Ol0KCwsxLp16+Dq6lrp9SEiInMMI42UTqdDcHAwPDw8cPPmTTzzzDN4//33pe2FhYXIyMgAAMTHxwMoGzuRmpoq1VEqlejYsaP09+3bt9GsWTOL50tJSUHfvn0xc+ZM9OnTRyo3PgIp71FIfHw8nn32WZOymzdvSr0uWVlZWLp0Kezt7aXHO8ePH8fWrVsxc+ZMdO7cGQBQWlqKoqIiNG/eXDrOrVu3EBsbK4WR+8fOFBQU4N69ewDKxthER0ejefPmJr0yxjBSUFCAjz76iGGEiKiKGEYaKXt7e3z//fcYO3YsMjMzsXbtWpMf46SkJAwcONBknwfHmbi6ukKj0QAom80SEhKCdevWYfz48Wbnc3d3B4Byw0p5OnXqhDVr1kCtVuP06dOIiYlBYGAgIiMjUVhYCFdXV8yfP99kHxcXF2zduhUjRoxAcHBwucceMGAASkpKLG4bMmQIEhMTUVJSAjc3N+Tl5SE6Ohpjx461qf1ERFQ5jhmpIQa9AekJ6TgTewbpCekw6G0bm1CbCgsLUVJSgs6dO+PIkSOYOXOm2aBQ40yS9evXQwiBrKwsnDp1Cnfu3IEQAqGhoSaDUh0dHeHm5oYJEyZg0aJFZuc09nwYB8Baq02bNpgzZw6mT58OBwcHAEDLli0xffp0zJ0796HMdElJScHPP/+M559/HkqlEo8++ig6deqEr7/+usbPRUREDCM14nzceUS3icbmQZsRNzYOmwdtRnSbaJyPOy930yz6y1/+ArVaDYVCAWdnZ3z22WcIDg6GQqGAQqFAhw4dpDBitHPnTvTq1Qs//fSTVGYMBwDwxBNP4OTJk+jWrRuioqJw+PBhk/2N4zGcnZ2r1GYhBP73f/9X+vvWrVvSf79w4QJ++OEHHDx4EMnJyUhPTwcAXLx4EcnJyThy5Aj+/e9/48yZM1ada82aNQBgMk5k4sSJ2L9/v9XHICIi6zGMVNP5uPPYHr4dmmsak3LNdQ22h2+vk4EkJiYGSUlJSElJwZIlSwAA//rXv3DixAkoFAoMGjTIbDBnaWkpAKBHjx5S2YN1WrRogQMHDuCLL75Av379LO5vaVquNf7973/jypUrAID09HR06dIFX331FQBg//79GDlyJAYMGIA+ffpg+fLlAIDZs2ejT58+6Nevn9mYmPKcOXMGmzdvxvDhw9GrVy+pfOLEiVCr1Xjrrbeq1H4iIiofx4xUg0FvwL45+wBLa3QJAApgX+Q+BIYGQqmqO7mvZ8+e0n9v0aIFAKBdu3a4cuUKhBBmY0UASIM5W7VqVeGxW7ZsiVdeeQUATKbRZmVlASgbz1FYWAigrLfD2qmzn376KZo2bQqFQgFfX1/cvn0bf//73zFw4ECEhobiscceg1qthkqlwvfff49ly5Zh3bp16Nu3rzTQtLIBpgaDAVOmTIFerzebVeTj44P58+fjnXfewc6dOxEeHm5Vu4mIqHJ15xeyHspIzDDrETEhAE2mBhmJGbXXqGpo3bo1PvjgAwwZMsRsW25uLuzt7dG0adNKj/Pee+8hLi7OZGqvcXqwp6enVHb/9ookJydj9+7dmDZtmrQq7Mcff4yCggK8+uqr8Pf3R9++ffH444/j0UcflaYPt2vXDo8++igef/xx/L//9//QtWvXCs8THR2N48ePY/r06SY9QEZvvvkm2rRpg8mTJ/NxDRFRDWIYqYb8LOteoGZtPbl16tQJb7zxBry8vMyWZL969Sp8fHxMejIsLduu0+nwySef4KWXXsLt27el8t9//x0A0LZtW6msuLhYOkZ5744xGAyIjIyEs7OzyRiOZ599FnPmzMGHH35o+xe1YPfu3Zg3bx7atWuHqKgoi3WcnJzw5ZdfoqCgAMOGDWMgISKqIXxMUw2uPtatK2FtvbrkwXBw7tw5BAYGmpRZmhb73Xff4datW1i0aBF8fHzw+eefo1+/fhg7dixcXV3Rtm1bODg44J///Ce6du2Kq1evAij/7bpr167F4cOH8eabb8Lb29tk25o1a/Dvf/8bJ0+ehJubG1xcXKBUKqW1UH7//XdpjEpJSQkKCgrg7e1tNp7ll19+wZgxY+Dg4IAdO3ZU+DjnmWeewZo1a/D3v/8dTz/9NLZu3Yrhw4eXW5+IiCrHMFINrYNaw83PDZrrGsvjRhSAm58bWge1trCxbisqKpL+u/HFeg8uPvZggBBCYOXKlXB1dcXcuXPh4uKC6dOn4+rVqzh79ixCQkJw9OhRFBcXY9SoUQDK3vQLlP/IplOnTmjVqhUWLlxocfvGjRuxbds2i9sWLFhgVhYaGmoSRr744gtERkZCp9Nh165d2LhxIz7//HM4OjpCpVKZrMCq0+lQXFyMdu3aYdWqVZg/fz5GjBiBadOmYfny5WZhicgaeoMeiRmJyMrPgo+rD4JaB0GlVFW+I1EDwjBSDUqVEsHRwdgevh1QwDSQ/OdpRvCa4Do1eNVabdq0wdq1a9G3b198+umnAMp+yG/evIkWLVrg3Xffxd27d032+frrr3Hu3DksWrTIZHEz41t0Q0NDERoaipKSEhw9ehTdunVDUFBQhW/pHTZsGA4ePFju+iTvvPMOXn/9dTg4OMDBwQEKhQJxcXFYsGABNm7ciKCgIABlj3tKS0ultVHy8/MxadIk7Nq1CyqVClu2bEFoaCheffVV5ObmwsnJCSqVCkII3Lp1C99++y1KSkqklwP+3//9H3x9fTF58mT84x//QMeOHfHaa69V/YJToxR3Pg5z9s3BNc01qczPzQ/RwdEI6xwmY8uIahfDSDV1DuuMiJ0R2Ddnn8lgVjc/NwSvCUbnsM4ytq7qfH19MWvWLJw5cwZr165F586d0bt3b/Tu3Rs9evTAl19+iW7dukn1NRoNFi5cCCcnJ8ydO1cqT0tLw7p169CxY0dMnDgRLVu2RGhoKP7617/i1KlT0rLuFXnkkUek/y6EMAkv7dq1M6tv7KHw8fFB+/btLR7TxcUFTz31FPbt24dt27YhNDQUAHDt2jWTegqFAk8++SQSExPNjjF27Fj06NEDe/bsYRAhm8Wdj0P49nCIB7pVr2uuI3x7OHZG7GQgoUaDYaQGdA7rjMDQQGQkZiA/Kx+uPq5oHdS6XvSIGAeZ3r8UvNG5c+cwfPhwlJSUYOvWrbC3t8f48eOxbNkypKSkYPfu3VIYmDFjBrKysjB9+nSpV6S0tBTjx49HcXExVq9eDXt7e4SEhGDBggVYuXIl5syZgw0bNkjnM864qWi6r1arLXcJd1soFAq8/vrrGDdunDS9+UHG81R0vm7dupmEMiJr6A16zNk3xyyIAICAgAIKRO6LRGhgKB/ZUKNQ938t6wmlSok2A9ug+5juaDOwTZ0PIuPGjUP//v2xevVquLq6ws/PT9qm1WoRHR2N3r17Iy8vDz/88AMee+wx2NnZ4c0330R8fDxu3LiBJ554QuoxGDp0KNzd3TF79mwAZT0YU6dOxeHDhxEWFoYRI0ZIx1+6dCnat2+P48eP4969e9DpdHj00UfRoUMHADB5s++DtFptuYNdH2RcaK0i5QURwLowQlQViRmJJo9mHiQgkKnJRGKGeY8cUUNUt38x6aFp3749zp49i549eyI2NlZa2v3OnTvo2bMnIiMj0bVrVxw9ehTDhg0z2bdv375ITEyEg4MDXnnlFWi1WkycOBHnz5+X3pKr1+vRr18/PPXUU9JKqUaOjo7YuXMnjh07BhcXF9jb2+Opp55CQEAAXnvtNWlVWEvc3Nysfh+NcXG1qjLOKGIYoZqWlZ9Vo/WI6juFqGj0YB2h0Wjg7u6OvLy8Sl+0VlxcjLS0NLRt27bKS483dleuXMGBAwfwyiuvQKUqv4v4woULcHJygr+/fy22rv7jPUoJ6QkYtHlQpfXiJ8ZjYJuBD79BRA+Jtb/fHDNCZtq1a4dp06ZVWq9jx4610BqihieodRD83PxwXXPd4rgRBRTwc/NDUOsgGVpHVPuq9Jjm3LlzCA0Nha+vL5ydndGxY0e8/vrryM3NNak3b9486U2wD37KW+WSiKihUylViA6OBlAWPO5n/HtN8BoOXqVGw+aekcuXL6N///6wt7fHqFGj4OjoiCNHjuCjjz7C/v37kZSUJHU9Jycnw8vLC1OnTjU7zoOrYBIRNSZhncOwM2KnxXVG1gSv4bRealRsDiNvv/02ioqKcOrUKQQEBEjl06ZNwxdffIHY2FhMmjQJBoMBJ0+exKBBg/Duu+/WaKOJiBqCsM5hCA0M5Qqs1OjZHEaSkpLQrVs3kyACACNGjMAXX3yBixcvAgBSU1ORn5+P3r1710xLbVQPxuVSI8V7k+6nUqo4SJUaPZvHjLi4uCAtLc3k3SWA+VtZT5w4AaBsGmhtsrMry1fWrkVBVNuMU4YrmqlERNSY2BxGxowZg9u3b2PixIlIT0+HRqPBjh07EBUVBW9vb4SFlT3nNIaRzz//HH5+fnB0dESHDh3w5ptvIi8vr2a/xX3s7OzQpEkT3LlzB3q9/qGdh6gqhBDIy8uDWq2Gvb293M0hIqoTbF5nRAiB+fPnY/Xq1Sbljz/+OLZt2ya9Zv6xxx7DqVOn4O3tjeeeew4uLi6Ij49HSkoKunXrhuPHj8PZ2dniOR5cZVOj0cDf39+qdUaAssWuMjMzoVKp4O7uLr30rKJlxokeJiEEdDod8vLycO/ePfj6+lp1LxMR1WcPbZ2RlJQU7NixA0DZIxsXFxfcvHkT586dw/r167Fq1SoIIdCyZUuMHTsWX375JZycnACU/QP5pZdewtdff41NmzZh5syZFs+xcuVKLF++3NamSZydndG2bVvcunULd+/eRU5OTpWPRVST1Go1gwgR0QNs6hkRQqBLly64fPky1q9fj3HjxkGlUuGPP/7ApEmTcPToUSxfvhxLly4t9xi3b9+Gt7c3+vTpg2PHjlmsU92ekQfbrNPpYDAYbNqPqKapVCo+miGiRsXanhGbwsiRI0fQr18/zJ49G9HR0Sbb7ty5A19fX7Rq1QqXL1+u8Dg+Pj4oKSmR3hhbGVuWgyciIqK6wdrfb5sGsGZkZAAAunTpYratadOmaNWqFa5duybVNc4auJ9er0dubi6Ki4ttOTURERE1UDaFEW9vbwCQ1hK5X3Z2Nq5evYrmzZtj8+bNCAgIwN69e83q7d+/H8XFxejRo0cVm0xEREQNiU1hpGvXrlCpVNiwYQMSExOl8qtXr2LcuHHQ6/UYPnw4Bg8eDDs7OyxYsMDkUczly5cxa9YsAMDkyZNr6CsQERFRfWbz1N6FCxdKL7nz8PCAnZ2dNFulRYsWOHXqFHx8fPDOO+9g2bJl8PDwQO/evaHT6XDkyBHodDq8+OKL2LJli9VTbTlmhIiIqP55KGNGgLJpt9u2bUNQUBAMBgNyc3Ph4+OD8ePH49ixY/Dx8QEALF26FLGxsXjkkUdw6NAhnDlzBgMHDsT27duxdetWrvlBREREAKrQMyIH9owQERHVPw+tZ4SIiIioJjGMEBERkawYRoiIiEhWDCNEREQkK4YRIiIikhXDCBEREcmKYYSIiIhkxTBCREREsmIYISIiIlkxjBAREZGsGEaIiIhIVgwjREREJCuGESIiIpIVwwgRERHJimGEiIiIZMUwQkRERLKyk7sBREQNgd6gR2JGIrLys+Dj6oOg1kFQKVVyN4uoXmAYISKqprjzcZizbw6uaa5JZX5ufogOjkZY5zAZW0ZUP/AxDRFRNcSdj0P49nCTIAIA1zXXEb49HHHn42RqGVH9wTBCRFRFeoMec/bNgYAw22Ysi9wXCb1BX9tNI6pXGEaIiKooMSPRrEfkfgICmZpMJGYk1mKriOofhhEioirKys+q0XpEjRXDCBFRFfm4+tRoPaLGimGEiKiKgloHwc/NDwooLG5XQAF/N38EtQ6q5ZYR1S8MI0REVaRSqhAdHA0AZoHE+Pea4DVcb4SoEgwjRETVENY5DDsjdsLXzdek3M/NDzsjdnKdESIrKIQQ5nPS6hiNRgN3d3fk5eXBzc1N7uYQEZnhCqxE5qz9/eYKrERENUClVGFgm4FyN4OoXuJjGiIiIpIVwwgRERHJimGEiIiIZMUwQkRERLJiGCEiIiJZVSmMnDt3DqGhofD19YWzszM6duyI119/Hbm5uWZ1N2zYgB49esDZ2RmBgYHYsmVLddtMREREDYjNYeTy5cvo378/jh07hpCQEEyZMgXu7u746KOPEBQUhOLiYqnu/Pnz8corryA3NxfTpk1DQEAAJkyYgK+++qpGvwQRERHVXzavM/L222+jqKgIp06dQkBAgFQ+bdo0fPHFF4iNjcWkSZNw+PBhfPDBBwgMDMTRo0fh6ekJABg7diwiIyMREhICLy+vmvsmREREVC/Z3DOSlJSEbt26mQQRABgxYgQA4OLFiwCATz/9FAAQFRUlBREAWLZsGTQaDeLi4qrcaCIiImo4bA4jLi4uSEtLQ1FRkUn577//DgBo27YtAODgwYNwcnLCs88+a1IvMDAQAQEBOHDgQFXbTERERA2IzWFkzJgxuH37NiZOnIj09HRoNBrs2LEDUVFR8Pb2RlhYGIQQuHnzJrp06QK1Wm12jA4dOuDy5cs18gWIiIiofrN5zMjcuXPx559/YvXq1dixY4dU/vjjj2Pbtm1o1qwZ7t27B71ejxYtWlg8hqenJ06fPl3uObRaLbRarfS3RqOxtZlERERUT9jcM5KSkiKFEBcXF7Rs2RIKhQLnzp3D+vXrYTAYoFKVvanS2dnZ4jHUajUKCwvLPcfKlSvh7u4uffz9/W1tJhEREdUTNoURIQRGjx6NGzduYNOmTcjNzUVWVhbOnz+PRx99FB9++CHeffddODo6QqVSmfRu3K+kpAQlJSXlnmfhwoXIy8uTPpmZmbZ9KyIiIqo3bAojR48eRWpqKmbMmIGJEydKPSCBgYH48ccf4ejoiM2bN0OhUMDX1xdXrlyxeJybN2+azLB5kFqthpubm8mHiIiIGiabwkhGRgYAoEuXLmbbmjZtilatWuHatWsAgF69euHChQvIy8szqVdaWoqTJ0+iZcuWVW0zERERNSA2hRFvb28A/11L5H7Z2dm4evUqmjdvDgAIDQ2FTqfD+vXrTert2rUL9+7dwzPPPFPVNhMREVEDYlMY6dq1K1QqFTZs2IDExESp/OrVqxg3bhz0ej2GDx8OAHjhhRfg5+eHZcuW4fvvvwdQtmDa3LlzoVKpMH78+Br8GkRE1ac36JGQnoDYM7FISE+A3qCXu0lEjYJCCCFs2WHhwoWIiooCAHh4eMDOzg45OTkAgBYtWuDUqVPw8fEBABw/fhwhISHIycmBm5ubNEX3vffew6JFi6w+p0ajgbu7O/Ly8jh+hIgeirjzcZizbw6uaa5JZX5ufogOjkZY5zAZW0ZUf1n7+21zGAGAb775Bv/4xz/w22+/obCwEF5eXhg8eDDeeecdtGnTxqTurVu3EBUVhWPHjqF58+aYPXs2Bg8e/FC+DBFRVcSdj0P49nAImP7jUAEFAGBnxE4GEqIqeKhhpLYxjBDRw6I36NEmuo1Jj8j9FFDAz80PaXPSoFKqarl1RPWbtb/fNi96RkTUkCRmJJYbRABAQCBTk4nEjMRy6xBR9TCMEFGjlpWfVaP1iMh2DCNE1Kj5uPrUaD0ish3DCBE1akGtg+Dn5icNVn2QAgr4u/kjqHVQLbeMqPFgGCGiRk2lVCE6OBoAzAKJ8e81wWs4eJXoIWIYIaJGL6xzGHZG7ISvm69JuZ+bH6f1EtUCTu0lIvoPvUGPxIxEZOVnwcfVB0Gtg9gjQlQN1v5+29Vim4iI6jSVUoWBbQbK3QyiRoePaYiIiEhWDCNEREQkK4YRIiIikhXDCBEREcmKYYSIiIhkxTBCREREsmIYISIiIlkxjBAREZGsGEaIiIhIVgwjREREJCuGESIiIpIVwwgRERHJimGEiIiIZMUwQkRERLJiGCEiIiJZMYwQERGRrBhGiIiISFYMI0RERCQrhhEiIiKSFcMIERERyYphhIiIiGTFMEJERESyYhghIiIiWTGMEBERkawYRoiIiEhWDCNEREQkK5vCiEKhqPSTkJAg1Z83b1659aKiomr6uxAREVE9ZGdL5cWLF5e7bceOHbhw4QK8vb2lsuTkZHh5eWHq1Klm9fv162fLqYmIiKiBUgghRHUPcvXqVQQGBmLkyJHYvn07AMBgMMDDwwODBg3Cnj17qnV8jUYDd3d35OXlwc3NrbrNJSIiolpg7e93jYwZWbp0KQwGA1auXCmVpaamIj8/H717966JUxAREVEDVe0wcvbsWWzduhXTp0/HI488IpWfOHECANC3b9/qnoKIiIgasGqHkRUrVkCpVGL+/Pkm5cYw8vnnn8PPzw+Ojo7o0KED3nzzTeTl5VX3tERERNRAVCuMZGZmYseOHYiIiICfn5/JtmPHjgEAEhMTMWTIEEyZMgVqtRrvv/8++vfvj8LCwnKPq9VqodFoTD5ERETUMFUrjHzyyScoLS3F3LlzTcr1ej1atmyJsWPHIj09HV999RXWrl2LM2fOYMKECTh79iw2bdpU7nFXrlwJd3d36ePv71+dZhIREVEdVuXZNFqtFi1btkRgYKDUC2KN27dvw9vbG3369Cl3P61WC61WK/2t0Wjg7+/P2TRERET1iLWzaWxaZ+R+33//PXJzczFmzBib9mvWrBm8vb1x8eLFcuuo1Wqo1eqqNo2IiIjqkSo/ptm6dSuUSiUiIiIsbs/IyIBOpzMr1+v1yM3NRXFxcVVPTURERA1IlcLI3bt3sXfvXvTv3x8+Pj5m2zdv3oyAgADs3bvXbNv+/ftRXFyMHj16VOXURERE1MBUKYwcOHAAOp0OzzzzjMXtgwcPhp2dHRYsWIDbt29L5ZcvX8asWbMAAJMnT67KqYmIiKiBqdKYkf379wMAnn76aYvbfX198dZbb2HZsmVo3749evfuDZ1OhyNHjkCn0+HFF19kGCEiIiIAVZxNExAQgD///BO5ublwcnIqt963336L1atXIyUlBc7Oznj88ccxZcoUjB492qbz8d00RERE9Y+1v9818qK8h41hhIiIqP6p1RflEREREVUVwwgRERHJimGEiIiIZMUwQkRERLJiGCEiIiJZMYwQERGRrBhGiIiISFYMI0RERCQrhhEiIiKSFcMIERERyYphhIiIiGTFMEJERESyYhghIiIiWTGMEBERkawYRoiIiEhWDCNEREQkK4YRIiIikhXDCBEREcmKYYSIiIhkxTBCREREsmIYISIiIlkxjBAREZGsGEaIiIhIVgwjREREJCuGESIiIpIVwwgRERHJimGEiIiIZMUwQkRERLJiGCEiIiJZMYwQERGRrBhGiIiISFYMI0RERCQrhhEiIiKSlU1hRKFQVPpJSEgw2WfDhg3o0aMHnJ2dERgYiC1bttRk+4mIiKies7Ol8uLFi8vdtmPHDly4cAHe3t5S2fz58/HBBx/A398f06ZNQ0pKCiZMmIDS0lJMmjSp6q0mIiKiBkMhhBDVPcjVq1cRGBiIkSNHYvv27QCAw4cPo3///ggMDMTRo0fh6ekJABg7diz+93//F5cuXYKXl5dVx9doNHB3d0deXh7c3Nyq21wiIosMegMyEjOQn5UPVx9XtA5qDaWKT7OJqsra32+bekbKs3TpUhgMBqxcuVIq+/TTTwEAUVFRUhABgGXLliE2NhZxcXGYNm1aTZyeiKjazsedx745+6C5ppHK3PzcEBwdjM5hnWVsGVHDV+3If/bsWWzduhXTp0/HI488IpUfPHgQTk5OePbZZ03qBwYGIiAgAAcOHKjuqYmIasT5uPPYHr7dJIgAgOa6BtvDt+N83HmZWkbUOFQ7jKxYsQJKpRLz58+XyoQQuHnzJrp06QK1Wm22T4cOHXD58uXqnpqIqNoMegP2zdkHWHpg/Z+yfZH7YNAbarVdRI1JtcJIZmYmduzYgYiICPj5+UnlBQUF0Ov1aNGihcX9PD09ce3atXKPq9VqodFoTD5ERA9DRmKGWY+ICQFoMjXISMyovUYRNTLVCiOffPIJSktLMXfuXJNylUoFAHB2dra4n1qtRmFhYbnHXblyJdzd3aWPv79/dZpJRFSu/Kz8Gq1HRLarchjRarX48ssv8eSTT+Lxxx832ebo6AiVSgWtVmtx35KSEpSUlJR77IULFyIvL0/6ZGZmVrWZREQVcvVxrdF6RGS7Ks+m+f7775Gbm4sxY8aYbVMoFPD19cWVK1cs7nvz5k2TGTYPUqvVFseaEBHVtNZBreHm5wbNdY3lcSOKslk1rYNa13rbiBqLKveMbN26FUqlEhERERa39+rVCxcuXEBeXp5JeWlpKU6ePImWLVtW9dRERDVGqVIiODq47A/FAxv/83fwmmCuN0L0EFXp/113797F3r170b9/f/j4+FisExoaCp1Oh/Xr15uU79q1C/fu3cMzzzxTlVMTEdW4zmGdEbEzAm6+posyufm5IWJnBNcZIXrIqvSY5sCBA9DpdBUGihdeeAFLly7FsmXL0LFjR4wcORJJSUmYO3cuVCoVxo8fX+VGExHVtM5hnREYGsgVWIlkUKUwsn//fgDA008/XW4dJycn7Ny5EyEhIQgNDYWbm5s0Rfe9997DY489VpVTExE9NEqVEm0GtpG7GUSNTpXeTRMQEIA///wTubm5cHJyqrDurVu3EBUVhWPHjqF58+aYPXs2Bg8ebNP5+G4aIiKi+sfa3+8aeVHew8YwQkREVP9Y+/vNh6FEREQkK4YRIiIikhXDCBEREcmKYYSIiIhkxTBCREREsmIYISIiIlkxjBAREZGsGEaIiIhIVgwjREREJCuGESIiIpIVwwgRERHJimGEiIiIZMUwQkRERLJiGCEiIiJZMYwQERGRrBhGiIiISFYMI0RERCQrhhEiIiKSFcMIERERyYphhIiIiGTFMEJERESyYhghIiIiWTGMEBERkawYRoiIiEhWDCNEREQkK4YRIiIikpWd3A1obPR6IDERyMoCfHyAoCBApZK7VURERPJhGKlFcXHAnDnAtWv/LfPzA6KjgbAw+dpFREQkJz6mqSVxcUB4uGkQAYDr18vK4+LkaRcREZHcGEZqgV5f1iMihPk2Y1lkZFk9IiKixoZhpBYkJpr3iNxPCCAzs6weERFRY8MwUguysmq2HhERUUPCMFILfHxqth4REVFDUu0wEh8fD6VSieeee85s27x586BQKCx+oqKiqnvqeiMoqGzWjEJhebtCAfj7l9UjIiJqbKo1tbegoACTJ0+Gk5MTPvvsM7PtycnJ8PLywtSpU8229evXrzqnrldUqrLpu+HhZcHj/oGsxoCyZg3XGyEiosapWmFkwYIFSEtLw+rVq9GmTRuTbQaDASdPnsSgQYPw7rvvVuc0DUJYGLBzp+V1Rtas4TojRETUeFU5jBw8eBCfffYZevXqhcjISLPtqampyM/PR+/evavTvoeqtldDDQsDQkO5AisREdH9qhRGCgsL8fLLL0OpVOKLL76AysKv6YkTJwAAffv2rV4LHxK5VkNVqYCBAx/e8YmIiOqbKg1gfeutt3D58mX07NkTe/bswdy5c7Fx40YUFhZKdYxh5PPPP4efnx8cHR3RoUMHvPnmm8jLy6uZ1lcRV0MlIiKqOxRCWFoXtHznzp1Dz549UVpaaratQ4cOiIuLQ7du3fDYY4/h1KlT8Pb2xnPPPQcXFxfEx8cjJSUF3bp1w/Hjx+Hs7GzVOTUaDdzd3ZGXlwc3NzdbmmtGrwfatCl/ETKFoqyHJC2Nj0+IiIiqw9rfb5t7RpYtW4bS0lJ4enpi69atyMjIwKlTpzBlyhRcvHgRI0eOREFBAVq2bImxY8ciPT0dX331FdauXYszZ85gwoQJOHv2LDZt2lTuObRaLTQajcmnpnA1VCIiorrFpp6RkpISuLm5QavVYs+ePRg5cqTJ9pEjR+KHH37A7t27ERoaavEYt2/fhre3N/r06YNjx45ZrPP2229j+fLlZuU10TMSGwuMHVt5vW++AcaMqdapiIiIGrWH0jOSk5MDrVYLLy8vhISEmG0fOnQoAOCPP/4o9xjNmjWDt7c3Ll68WG6dhQsXIi8vT/pkZmba0swKcTVUIiKiusWm2TTGVNO2bVsoleY5RqfTAQAcHByQkZEBHx8f2Nvbm9TR6/XIzc21uL+RWq2GWq22pWlWM66Gev265bfoGseMcDVUIiKi2mFTz4iLiwvat2+PzMxMWHq6c/r0aQCAp6cnAgICsHfvXrM6+/fvR3FxMXr06FG1FleTcTVUwHx5dq6GSkREVPtsHsA6btw4ZGVl4aOPPjIpT0xMxDfffIOAgAAMHDgQdnZ2WLBgAW7fvi3VuXz5MmbNmgUAmDx5cjWbXnXG1VB9fU3L/fzKyrkaqnz0eiAhoWxsT0JC2d9ERNSw2Ty1t6CgAIMGDUJSUhIGDBiAXr16IT09HT/88AMA4IcffsCzzz6Ld955B8uWLYOHhwd69+4NnU6HI0eOQKfT4cUXX8SWLVugKO/NcQ+oyam996vtFVipYnItREdERA+Htb/fNocRACgqKsKnn36K2NhYXL58GUqlEj169MA777yDAQMGSPW+/fZbrF69GikpKXB2dsbjjz+OKVOmYPTo0Q/ly1D9ZVyI7sG70ZhX2WNFRFT/PNQwUtsYRho2LkRHRNQwPbRFz4hqGheiIyJq3BhGSHZZWTVbj4iI6heGEZIdF6IjImrcGEZIdsaF6MqbXKVQAP7+XIiOiKihYhgh2XEhOiKixo1hhOoELkRHRNR42fRuGqKHKSwMCA3lQnRERI0NwwjVKSoVMHCg3K0gIqLaxMc0REREJCuGESIiIpIVwwgRERHJimGEiIiIZMUwQkRERLJiGCEiIiJZMYwQERGRrBhGiIiISFYMI0RERCSrerECqxACAKDRaGRuCREREVnL+Ltt/B0vT70II/n5+QAAf39/mVtCREREtsrPz4e7u3u52xWisrhSBxgMBty4cQOurq5QPPiO+UZEo9HA398fmZmZcHNzk7s5dRKvUeV4jSrG61M5XqPK8RqVEUIgPz8frVq1glJZ/siQetEzolQq4efnJ3cz6gw3N7dGfXNbg9eocrxGFeP1qRyvUeV4jVBhj4gRB7ASERGRrBhGiIiISFYMI/WIWq3GsmXLoFar5W5KncVrVDleo4rx+lSO16hyvEa2qRcDWImIiKjhYs8IERERyYphhIiIiGTFMEJERESyYhipw+Lj46FUKvHcc8+ZbZs3bx4UCoXFT1RUlAytffjK+773fxISEkz22bBhA3r06AFnZ2cEBgZiy5Yt8jS+Fth6fRrjPWR07tw5hIaGwtfXF87OzujYsSNef/115ObmmtVtTPfQ/ay9Ro31Pjpx4gSef/55tGzZEo6OjujTpw82b95ssW5jvYdsUS8WPWuMCgoKMHnyZDg5OeGzzz4z256cnAwvLy9MnTrVbFu/fv1qo4m1bvHixeVu27FjBy5cuABvb2+pbP78+fjggw/g7++PadOmISUlBRMmTEBpaSkmTZpUG02uVbZen8Z4DwHA5cuX0b9/f9jb22PUqFFwdHTEkSNH8NFHH2H//v1ISkqCo6MjgMZ3DxnZco0a43307bffYvz48XB1dUVERATUajX27NmDl156CYmJifjyyy+luo31HrKZoDrp1VdfFQDE6tWrzbbp9Xrh6uoqRo4cKUPL6p709HShVqvF6NGjpbJDhw4JACIwMFDcuXNHKh8zZoxwc3MTt27dkqOpsrB0fRrzPTRu3Djh6Ogo0tPTTcqnTp0qAIiNGzcKIRr3PWTtNWqM99GtW7eEi4uL8PLyEpmZmVJ5QUGBePLJJwUAsX37diFE476HbMXHNHXQwYMH8dlnn6FXr16IjIw0256amor8/Hz07t279htXBy1duhQGgwErV66Uyj799FMAQFRUFDw9PaXyZcuWQaPRIC4urtbbKRdL16cx30NJSUno1q0bAgICTMpHjBgBALh48SKAxn0PWXuNGuN99N133+HevXt44403TF5T4uzsjIULFwIA9uzZA6Bx30O2YhipYwoLC/Hyyy9DqVTiiy++gEqlMqtz4sQJAEDfvn1ru3l1ztmzZ7F161ZMnz4djzzyiFR+8OBBODk54dlnnzWpHxgYiICAABw4cKC2myqL8q5PY76HXFxckJaWhqKiIpPy33//HQDQtm1bAI37HrL2GjXG+yglJQUA8MQTT5hta968OQAgOzsbQOO+h2zFMFLHvPXWW7h8+TJ69uyJPXv2YO7cudi4cSMKCwulOsZ/AHz++efw8/ODo6MjOnTogDfffBN5eXlyNV0WK1asgFKpxPz586UyIQRu3ryJLl26WFz9sEOHDrh8+XJtNlM2lq4P0LjvoTFjxuD27duYOHEi0tPTodFosGPHDkRFRcHb2xthYWGN/h6y5hoBjfM+srMrG2qZk5Njts0YVFq3bt3o7yGbyfyYiO6TkpIi7OzsBACzT4cOHcSZM2eEEEL06tVLABDe3t7ipZdeErNmzRJdu3YVAES3bt1EQUGBzN+kdmRkZAg7OzsxduxYk/L8/HwBQDz33HMW9xs9erRo3rx5bTRRVuVdHyEa9z1kMBjEG2+8Yfb/sccff1ykpqYKIXgPWXONhGic99G6desEANGvXz+h0+mk8jt37oi2bdsKAOKf//xno7+HbMUwUoeEh4cLAMLT01Ns3bpVZGRkiFOnTokpU6YIAKJt27bi3r174tlnnxVjx44VhYWF0r4Gg0FMmDBBABDr1q2T8VvUHuM/LJOTk03KCwsLBQARHh5ucb9x48YJZ2fn2miirMq7PqWlpY36Hjpz5owICAgQAISLi4to2bKlUCgUwsnJSbz++utCr9c3+nvImmvUWO+j3Nxc4evrKwCIJ554QvzjH/8QH3zwgWjfvr0AIPz9/YVOp2v095CtGEbqCK1WK9RqtQAg9uzZY7Z9xIgRAoDYvXt3ucfIyckRSqVSPPnkkw+zqXVCcXGx8PDwsPhdDQaDUKlUYsSIERb3jYiIEHZ2dg+7ibKq6PpUpKHfQwaDQXTq1EnY29uLTZs2idLSUiGEEKmpqeKpp54SAMTy5csb9T1k7TWqSEO/jy5cuCBdiwc/MTExQgj+c8hWHDNSR+Tk5ECr1cLLywshISFm24cOHQoA+OOPP8o9RrNmzeDt7S2NdG/Ivv/+e+Tm5mLMmDFm2xQKBXx9fXHlyhWL+968edNkZHtDVNH1qUhDv4eOHj2K1NRUzJgxAxMnTpQGiAcGBuLHH3+Eo6MjNm/e3KjvIWuvUUUa+n3UoUMHHDlyBBcvXsT+/fuxaNEiAEDnzp0xefJkAPznkK0YRuoINzc3AGWj1JVK8/9ZdDodAMDBwQEZGRnS3/fT6/XIzc1FcXHxw21sHbB161YolUpERERY3N6rVy9cuHDBbBBdaWkpTp48iZYtW9ZGM2VT2fVprPdQRkYGAKBLly5m25o2bYpWrVrh2rVrABrvPWTLNWqs95FR+/btMXjwYPz0008AgA8//NBkBmRjvYeqgmGkjnBxcUH79u2RmZkJIYTZ9tOnTwMAPD09ERAQgL1795rV2b9/P4qLi9GjR4+H3VxZ3b17F3v37kX//v3h4+NjsU5oaCh0Oh3Wr19vUr5r1y7cu3cPzzzzTG00VRaVXZ/Nmzc32nvIuAKtpX9jz87OxtWrV6XpmY31HrL2GjXm++h+u3fvxunTpxESEmI2hbex3kNVIvdzIvqvt99+2+KqqwcPHhR2dnYiICBApKenCzs7O9GpUyeRk5Mj1bl06ZJo166dACDWr19f202vVd99912lz60LCwuFn5+fcHZ2lsbgnDhxQrRq1UqoVCpx8uTJ2mpuravs+ly7dq3R3kN//vmnUKlUwsPDQxw8eFAqT09PF0OHDhUAxJQpU4QQjfcesvYaNeb7yEin04lOnToJtVotLl68aLa9sd5DVcEwUofcu3dP9OnTRwAQAwYMEJGRkWLUqFFCpVIJlUolfvrpJyGEEMuXLxcAhIeHhxg8eLAYMGCAsLe3FwDEiy++KAwGg8zf5OGaPHmyACDi4+MrrHfs2DHRvHlzAUC4ublJA8zee++92mmoTKy5Po35HnrzzTele8HDw0O6RwCIFi1aiBs3bkh1G+s9ZO01asz3kRBCREdHCwBi8eLF5dZprPeQrRhG6pjCwkKxatUq0atXL+Hm5iY8PDzE008/LRISEkzqxcbGiscff1w4OjqKpk2biiFDhkjvQ2joWrduLRwcHEymE5bn5s2b4rXXXhNPPfWUGDFihDhw4EAttFBe1l6fxnwPbdu2TQQFBQk3NzdhZ2cnfHx8xPjx40VaWppZ3cZ4Dwlh/TVqrPfRnTt3RNOmTYW/v3+l66k01nvIFgohLAxQICIiIqolHMBKREREsmIYISIiIlkxjBAREZGsGEaIiIhIVgwjREREJCuGESIiIpIVwwgRERHJimGEiIiIZMUwQkRERLJiGCEiIiJZMYwQERGRrBhGiIiISFb/H5oF9yl066gcAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "plt.rcParams[\"font.family\"] = \"SimHei\"\n", "plt.rcParams[\"font.size\"] = 16\n", "\n", "good = np.array([[90, 91], [93, 94], [92, 98]]) #优等学生\n", "medium = np.array([[81, 82], [80, 79], [82, 86]])  #中等学生\n", "bad = np.array([[62, 65], [64, 66], [69, 64]]) #差等学生\n", "sample = np.array([[80, 70]])  #预测样本\n", "\n", "plt.scatter(good[:, 0], good[:, 1], color=\"r\", label=\"优等学生\")\n", "plt.scatter(medium[:, 0], medium[:, 1], color=\"g\", label=\"中等学生\")\n", "plt.scatter(bad[:, 0], bad[:, 1], color=\"b\", label=\"差等学生\")\n", "plt.scatter(sample[:, 0], sample[:, 1], color=\"purple\", label=\"预测样本\")\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "9e50af25", "metadata": {}, "source": ["# 1.2.6 KNN算法优化"]}, {"cell_type": "code", "execution_count": 19, "id": "ffce1697", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中心点： 4.4 3.8\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "# 寻找数据中心\n", "# 点：A,B,C,D,E\n", "x = [1, 2, 4, 6, 9]\n", "y = [1, 2, 6, 7, 3]\n", "# 中心位置:mean求平均数\n", "x_ = np.mean(x)\n", "y_ = np.mean(y)\n", "print('中心点：', x_, y_)\n", "# 可视化\n", "plt.scatter(x, y)\n", "plt.scatter(x_, y_)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 22, "id": "ed781f7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a中心： 2.8 1.8\n", "b中心： 5.4 6.6\n", "ac距离： 3.417601498127013\n", "bc距离： 2.1260291625469296\n", "是b类\n"]}], "source": ["import math\n", "import numpy as np\n", "# a类数据\n", "a = np.array([[1, 1], [2, 1], [3, 2], [3, 4], [5, 1]])\n", "# b类数据\n", "b = np.array([[6, 7], [5, 6], [7, 6], [3, 5], [6, 9]])\n", "# 要分类数据\n", "c = [4, 5]\n", "# 求a,b中心\n", "ax, ay = np.mean(a[:, 0]), np.mean(a[:, 1])\n", "bx, by = np.mean(b[:, 0]), np.mean(b[:, 1])\n", "print('a中心：', ax, ay)\n", "print('b中心：', bx, by)\n", "# 求c到a,b中心的距离\n", "ac = distance(c,np.array([ax,ay]))\n", "bc = distance(c,np.array([bx,by]))\n", "print('ac距离：', ac)\n", "print('bc距离：', bc)\n", "# 比较ac,bc那个小，就分那个类\n", "if ac < bc:\n", "    print('是a类')\n", "else:\n", "    print('是b类')"]}, {"cell_type": "markdown", "id": "32b8daed", "metadata": {}, "source": ["# 2.KNN算法实践"]}, {"cell_type": "code", "execution_count": 23, "id": "a37435bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.7\n"]}, {"data": {"text/plain": ["array([0, 1, 1])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.datasets import load_iris\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report\n", "iris = load_iris()\n", "X = iris.data[:,:2]\n", "y = iris.target\n", "X_train , X_test , y_train , y_test = train_test_split(X,y,test_size=0.2,random_state=0)\n", "knn = KNeighborsClassifier(n_neighbors=3,weights=\"uniform\")\n", "knn.fit(X_train , y_train)\n", "print(knn.score(X_test,y_test))\n", "knn.predict([[1,2],[5.8,2.8],[6,2.2]])"]}, {"cell_type": "markdown", "id": "a94fd5a7", "metadata": {}, "source": ["# 3.2 网格搜索"]}, {"cell_type": "code", "execution_count": 2, "id": "5888e154", "metadata": {}, "outputs": [{"ename": "UnicodeEncodeError", "evalue": "'ascii' codec can't encode character '\\u90ed' in position 23: ordinal not in range(128)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mUnicodeEncodeError\u001b[0m                        <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 14\u001b[0m\n\u001b[0;32m     12\u001b[0m grid \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mn_neighbors\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m11\u001b[39m, \u001b[38;5;241m1\u001b[39m)} \n\u001b[0;32m     13\u001b[0m gs \u001b[38;5;241m=\u001b[39m GridSearchCV(estimator\u001b[38;5;241m=\u001b[39mknn, param_grid\u001b[38;5;241m=\u001b[39mgrid, scoring\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maccuracy\u001b[39m\u001b[38;5;124m\"\u001b[39m, n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, cv\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m5\u001b[39m, verbose\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m) \n\u001b[1;32m---> 14\u001b[0m gs\u001b[38;5;241m.\u001b[39mfit(X_train, y_train)\n\u001b[0;32m     16\u001b[0m \u001b[38;5;66;03m# 最好的得分\u001b[39;00m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;28mprint\u001b[39m(gs\u001b[38;5;241m.\u001b[39mbest_score_)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\sklearn\\base.py:1363\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1356\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1358\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1359\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1360\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1361\u001b[0m     )\n\u001b[0;32m   1362\u001b[0m ):\n\u001b[1;32m-> 1363\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fit_method(estimator, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\sklearn\\model_selection\\_search.py:979\u001b[0m, in \u001b[0;36mBaseSearchCV.fit\u001b[1;34m(self, X, y, **params)\u001b[0m\n\u001b[0;32m    967\u001b[0m fit_and_score_kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdict\u001b[39m(\n\u001b[0;32m    968\u001b[0m     scorer\u001b[38;5;241m=\u001b[39mscorers,\n\u001b[0;32m    969\u001b[0m     fit_params\u001b[38;5;241m=\u001b[39mrouted_params\u001b[38;5;241m.\u001b[39mestimator\u001b[38;5;241m.\u001b[39mfit,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    976\u001b[0m     verbose\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mverbose,\n\u001b[0;32m    977\u001b[0m )\n\u001b[0;32m    978\u001b[0m results \u001b[38;5;241m=\u001b[39m {}\n\u001b[1;32m--> 979\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m parallel:\n\u001b[0;32m    980\u001b[0m     all_candidate_params \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m    981\u001b[0m     all_out \u001b[38;5;241m=\u001b[39m []\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\parallel.py:765\u001b[0m, in \u001b[0;36mParallel.__enter__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    763\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__enter__\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m    764\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_managed_backend \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m--> 765\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initialize_backend()\n\u001b[0;32m    766\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\parallel.py:775\u001b[0m, in \u001b[0;36mParallel._initialize_backend\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    773\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Build a process or thread pool and return the number of workers\"\"\"\u001b[39;00m\n\u001b[0;32m    774\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 775\u001b[0m     n_jobs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39mconfigure(n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_jobs, parallel\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    776\u001b[0m                                      \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend_args)\n\u001b[0;32m    777\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39msupports_timeout:\n\u001b[0;32m    778\u001b[0m         warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[0;32m    779\u001b[0m             \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mThe backend class \u001b[39m\u001b[38;5;132;01m{!r}\u001b[39;00m\u001b[38;5;124m does not support timeout. \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m    780\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mYou have set \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtimeout=\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m in Parallel but \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    781\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mthe \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m parameter will not be used.\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[0;32m    782\u001b[0m                 \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m,\n\u001b[0;32m    783\u001b[0m                 \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout))\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\_parallel_backends.py:506\u001b[0m, in \u001b[0;36mLokyBackend.configure\u001b[1;34m(self, n_jobs, parallel, prefer, require, idle_worker_timeout, **memmappingexecutor_args)\u001b[0m\n\u001b[0;32m    502\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m n_jobs \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m    503\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m FallbackToBackend(\n\u001b[0;32m    504\u001b[0m         SequentialBackend(nesting_level\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnesting_level))\n\u001b[1;32m--> 506\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_workers \u001b[38;5;241m=\u001b[39m get_memmapping_executor(\n\u001b[0;32m    507\u001b[0m     n_jobs, timeout\u001b[38;5;241m=\u001b[39midle_worker_timeout,\n\u001b[0;32m    508\u001b[0m     env\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_worker_env(n_jobs\u001b[38;5;241m=\u001b[39mn_jobs),\n\u001b[0;32m    509\u001b[0m     context_id\u001b[38;5;241m=\u001b[39mparallel\u001b[38;5;241m.\u001b[39m_id, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mmemmappingexecutor_args)\n\u001b[0;32m    510\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mparallel \u001b[38;5;241m=\u001b[39m parallel\n\u001b[0;32m    511\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m n_jobs\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\executor.py:20\u001b[0m, in \u001b[0;36mget_memmapping_executor\u001b[1;34m(n_jobs, **kwargs)\u001b[0m\n\u001b[0;32m     19\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget_memmapping_executor\u001b[39m(n_jobs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m---> 20\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m MemmappingExecutor\u001b[38;5;241m.\u001b[39mget_memmapping_executor(n_jobs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\executor.py:42\u001b[0m, in \u001b[0;36mMemmappingExecutor.get_memmapping_executor\u001b[1;34m(cls, n_jobs, timeout, initializer, initargs, env, temp_folder, context_id, **backend_args)\u001b[0m\n\u001b[0;32m     39\u001b[0m reuse \u001b[38;5;241m=\u001b[39m _executor_args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mor\u001b[39;00m _executor_args \u001b[38;5;241m==\u001b[39m executor_args\n\u001b[0;32m     40\u001b[0m _executor_args \u001b[38;5;241m=\u001b[39m executor_args\n\u001b[1;32m---> 42\u001b[0m manager \u001b[38;5;241m=\u001b[39m TemporaryResourcesManager(temp_folder)\n\u001b[0;32m     44\u001b[0m \u001b[38;5;66;03m# reducers access the temporary folder in which to store temporary\u001b[39;00m\n\u001b[0;32m     45\u001b[0m \u001b[38;5;66;03m# pickles through a call to manager.resolve_temp_folder_name. resolving\u001b[39;00m\n\u001b[0;32m     46\u001b[0m \u001b[38;5;66;03m# the folder name dynamically is useful to use different folders across\u001b[39;00m\n\u001b[0;32m     47\u001b[0m \u001b[38;5;66;03m# calls of a same reusable executor\u001b[39;00m\n\u001b[0;32m     48\u001b[0m job_reducers, result_reducers \u001b[38;5;241m=\u001b[39m get_memmapping_reducers(\n\u001b[0;32m     49\u001b[0m     unlink_on_gc_collect\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[0;32m     50\u001b[0m     temp_folder_resolver\u001b[38;5;241m=\u001b[39mmanager\u001b[38;5;241m.\u001b[39mresolve_temp_folder_name,\n\u001b[0;32m     51\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mbackend_args)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\_memmapping_reducer.py:535\u001b[0m, in \u001b[0;36mTemporaryResourcesManager.__init__\u001b[1;34m(self, temp_folder_root, context_id)\u001b[0m\n\u001b[0;32m    529\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m context_id \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    530\u001b[0m     \u001b[38;5;66;03m# It would be safer to not assign a default context id (less silent\u001b[39;00m\n\u001b[0;32m    531\u001b[0m     \u001b[38;5;66;03m# bugs), but doing this while maintaining backward compatibility\u001b[39;00m\n\u001b[0;32m    532\u001b[0m     \u001b[38;5;66;03m# with the previous, context-unaware version get_memmaping_executor\u001b[39;00m\n\u001b[0;32m    533\u001b[0m     \u001b[38;5;66;03m# exposes exposes too many low-level details.\u001b[39;00m\n\u001b[0;32m    534\u001b[0m     context_id \u001b[38;5;241m=\u001b[39m uuid4()\u001b[38;5;241m.\u001b[39mhex\n\u001b[1;32m--> 535\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mset_current_context(context_id)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\_memmapping_reducer.py:539\u001b[0m, in \u001b[0;36mTemporaryResourcesManager.set_current_context\u001b[1;34m(self, context_id)\u001b[0m\n\u001b[0;32m    537\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mset_current_context\u001b[39m(\u001b[38;5;28mself\u001b[39m, context_id):\n\u001b[0;32m    538\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_current_context_id \u001b[38;5;241m=\u001b[39m context_id\n\u001b[1;32m--> 539\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mregister_new_context(context_id)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\_memmapping_reducer.py:564\u001b[0m, in \u001b[0;36mTemporaryResourcesManager.register_new_context\u001b[1;34m(self, context_id)\u001b[0m\n\u001b[0;32m    557\u001b[0m new_folder_name \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m    558\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mjoblib_memmapping_folder_\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m_\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m_\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[0;32m    559\u001b[0m         os\u001b[38;5;241m.\u001b[39mgetpid(), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_id, context_id)\n\u001b[0;32m    560\u001b[0m )\n\u001b[0;32m    561\u001b[0m new_folder_path, _ \u001b[38;5;241m=\u001b[39m _get_temp_dir(\n\u001b[0;32m    562\u001b[0m     new_folder_name, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_temp_folder_root\n\u001b[0;32m    563\u001b[0m )\n\u001b[1;32m--> 564\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mregister_folder_finalizer(new_folder_path, context_id)\n\u001b[0;32m    565\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_cached_temp_folders[context_id] \u001b[38;5;241m=\u001b[39m new_folder_path\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\_memmapping_reducer.py:594\u001b[0m, in \u001b[0;36mTemporaryResourcesManager.register_folder_finalizer\u001b[1;34m(self, pool_subfolder, context_id)\u001b[0m\n\u001b[0;32m    587\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mregister_folder_finalizer\u001b[39m(\u001b[38;5;28mself\u001b[39m, pool_subfolder, context_id):\n\u001b[0;32m    588\u001b[0m     \u001b[38;5;66;03m# Register the garbage collector at program exit in case caller forgets\u001b[39;00m\n\u001b[0;32m    589\u001b[0m     \u001b[38;5;66;03m# to call terminate explicitly: note we do not pass any reference to\u001b[39;00m\n\u001b[0;32m    590\u001b[0m     \u001b[38;5;66;03m# ensure that this callback won't prevent garbage collection of\u001b[39;00m\n\u001b[0;32m    591\u001b[0m     \u001b[38;5;66;03m# parallel instance and related file handler resources such as POSIX\u001b[39;00m\n\u001b[0;32m    592\u001b[0m     \u001b[38;5;66;03m# semaphores and pipes\u001b[39;00m\n\u001b[0;32m    593\u001b[0m     pool_module_name \u001b[38;5;241m=\u001b[39m whichmodule(delete_folder, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdelete_folder\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m--> 594\u001b[0m     resource_tracker\u001b[38;5;241m.\u001b[39mregister(pool_subfolder, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfolder\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    596\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_cleanup\u001b[39m():\n\u001b[0;32m    597\u001b[0m         \u001b[38;5;66;03m# In some cases the Python runtime seems to set delete_folder to\u001b[39;00m\n\u001b[0;32m    598\u001b[0m         \u001b[38;5;66;03m# None just before exiting when accessing the delete_folder\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    603\u001b[0m         \u001b[38;5;66;03m# because joblib should only use relative imports to allow\u001b[39;00m\n\u001b[0;32m    604\u001b[0m         \u001b[38;5;66;03m# easy vendoring.\u001b[39;00m\n\u001b[0;32m    605\u001b[0m         delete_folder \u001b[38;5;241m=\u001b[39m \u001b[38;5;28m__import__\u001b[39m(\n\u001b[0;32m    606\u001b[0m             pool_module_name, fromlist\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdelete_folder\u001b[39m\u001b[38;5;124m'\u001b[39m])\u001b[38;5;241m.\u001b[39mdelete_folder\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\resource_tracker.py:179\u001b[0m, in \u001b[0;36mResourceTracker.register\u001b[1;34m(self, name, rtype)\u001b[0m\n\u001b[0;32m    177\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m'''Register a named resource, and increment its refcount.'''\u001b[39;00m\n\u001b[0;32m    178\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mensure_running()\n\u001b[1;32m--> 179\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mREGISTER\u001b[39m\u001b[38;5;124m'\u001b[39m, name, rtype)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\resource_tracker.py:196\u001b[0m, in \u001b[0;36mResourceTracker._send\u001b[1;34m(self, cmd, name, rtype)\u001b[0m\n\u001b[0;32m    192\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(name) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m512\u001b[39m:\n\u001b[0;32m    193\u001b[0m     \u001b[38;5;66;03m# posix guarantees that writes to a pipe of less than PIPE_BUF\u001b[39;00m\n\u001b[0;32m    194\u001b[0m     \u001b[38;5;66;03m# bytes are atomic, and that PIPE_BUF >= 512\u001b[39;00m\n\u001b[0;32m    195\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mname too long\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m--> 196\u001b[0m msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mcmd\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrtype\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mencode(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mascii\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m    197\u001b[0m nbytes \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fd, msg)\n\u001b[0;32m    198\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m nbytes \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mlen\u001b[39m(msg)\n", "\u001b[1;31mUnicodeEncodeError\u001b[0m: 'ascii' codec can't encode character '\\u90ed' in position 23: ordinal not in range(128)"]}], "source": ["from sklearn.model_selection import GridSearchCV \n", "from sklearn.model_selection import train_test_split\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.datasets import load_iris\n", "from sklearn.metrics import classification_report\n", "\n", "iris = load_iris()\n", "X = iris.data[:, :2]\n", "y = iris.target\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=0)\n", "knn = KNeighborsClassifier()\n", "grid = {\"n_neighbors\": range(1, 11, 1)} \n", "gs = GridSearchCV(estimator=knn, param_grid=grid, scoring=\"accuracy\", n_jobs=-1, cv=5, verbose=1) \n", "gs.fit(X_train, y_train)\n", "\n", "# 最好的得分\n", "print(gs.best_score_)\n", "# 最好的超参数\n", "print(gs.best_params_)\n", "# 使用最好的超参数训练模型。\n", "print(gs.best_estimator_)\n", "# 测试集上的表现\n", "print(gs.score(X_test,y_test))\n", "# 查看测试集的结果\n", "print(gs.predict(X_test))\n", "# 测试集上的表现\n", "sum((gs.predict(X_test) == y_test) / len(y_test))"]}, {"cell_type": "markdown", "id": "7f0c5306", "metadata": {}, "source": ["# 3.3 学习曲线"]}, {"cell_type": "code", "execution_count": 37, "id": "6b43d800", "metadata": {}, "outputs": [{"data": {"text/plain": ["([<matplotlib.axis.XTick at 0x2b072b02d00>,\n", "  <matplotlib.axis.XTick at 0x2b072b02520>,\n", "  <matplotlib.axis.XTick at 0x2b072d65cd0>,\n", "  <matplotlib.axis.XTick at 0x2b072c24af0>,\n", "  <matplotlib.axis.XTick at 0x2b072c2e280>,\n", "  <matplotlib.axis.XTick at 0x2b072c24d60>,\n", "  <matplotlib.axis.XTick at 0x2b072c2e2b0>,\n", "  <matplotlib.axis.XTick at 0x2b072c35220>,\n", "  <matplotlib.axis.XTick at 0x2b072c35970>,\n", "  <matplotlib.axis.XTick at 0x2b072c39100>],\n", " [Text(0, 0, ''),\n", "  Text(0, 0, ''),\n", "  Text(0, 0, ''),\n", "  Text(0, 0, ''),\n", "  Text(0, 0, ''),\n", "  Text(0, 0, ''),\n", "  Text(0, 0, ''),\n", "  Text(0, 0, ''),\n", "  Text(0, 0, ''),\n", "  Text(0, 0, '')])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "plt.rcParams[\"font.family\"] = \"SimHei\"\n", "plt.rcParams[\"font.size\"] = 16\n", "list1=[]\n", "for i in range(10):\n", "    knn = KNeighborsClassifier(n_neighbors=i+1)\n", "    knn = knn.fit(X_train,y_train)\n", "    list1.append(knn.score(X_test,y_test))\n", "plt.plot(range(1,11), list1)\n", "plt.xticks(range(1, 11))"]}, {"cell_type": "markdown", "id": "f7e2773a", "metadata": {}, "source": ["# 4 手写数字识别"]}, {"cell_type": "code", "execution_count": 6, "id": "30002ecb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.neighbors import KNeighborsClassifier\n", "import joblib\n", "from collections import Counter\n", "\n", "def show_img(idx):\n", "    data = pd.read_csv('C:\\\\Users\\\\<USER>\\\\Desktop\\\\海洋大学授课\\\\手写数字识别数据集\\\\data1.csv')\n", "    if idx<0 or idx>len(data)-1:\n", "        return\n", "    X = data.iloc[:,1:]\n", "    y = data.iloc[:,0]\n", "    print(\"真实数据标签为：\",y.iloc[idx])\n", "    data_ = X.iloc[idx].values\n", "    data_ = data_.reshape(28,28)\n", "    plt.axis(\"off\")\n", "    plt.imshow(data_)\n"]}, {"cell_type": "code", "execution_count": null, "id": "5a8311be", "metadata": {}, "outputs": [], "source": ["show_img(1)"]}, {"cell_type": "code", "execution_count": 10, "id": "d46c8801", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9697619047619047\n"]}], "source": ["def train_model():\n", "    data = pd.read_csv('data1.csv')\n", "    X = data.iloc[:,1:]\n", "    y = data.iloc[:,0]\n", "    X_train , X_test , y_train , y_test  = train_test_split(X,y,test_size=0.2,random_state=0)\n", "    knn = KNeighborsClassifier(n_neighbors=3)\n", "    knn.fit(X_train,y_train)\n", "    print(knn.score(X_test,y_test))\n", "    joblib.dump(knn,'knn_model.pth')\n", "\n", "train_model()"]}, {"cell_type": "code", "execution_count": 14, "id": "77c3a780", "metadata": {}, "outputs": [], "source": ["def predict_model(idx=0):\n", "    import pandas as pd\n", "    import joblib\n", "    # 读取数据\n", "    data = pd.read_csv('data1.csv')\n", "    X = data.iloc[:,1:]\n", "    y = data.iloc[:,0]\n", "    # 选择一行数据进行预测，保持DataFrame格式\n", "    sample = X.iloc[[idx]]  # 使用[[idx]]保持DataFrame格式，而不是转换为numpy数组\n", "    # 加载模型\n", "    knn = joblib.load('knn_model.pth')\n", "    # 预测\n", "    y_pred = knn.predict(sample)\n", "    print(f\"真实标签: {y.iloc[idx]}\")\n", "    print(f\"预测结果: {y_pred[0]}\")"]}, {"cell_type": "code", "execution_count": 13, "id": "5d80f513", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["真实标签: 1\n", "预测结果: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\anaconda\\Lib\\site-packages\\sklearn\\utils\\validation.py:2749: UserWarning: X does not have valid feature names, but KNeighborsClassifier was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["predict_model(0)"]}, {"cell_type": "code", "execution_count": null, "id": "fbcff901", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}