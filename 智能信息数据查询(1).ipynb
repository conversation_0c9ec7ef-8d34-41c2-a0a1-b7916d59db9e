{"cells": [{"cell_type": "markdown", "id": "9ca2d36f", "metadata": {}, "source": ["智能信息查询目标\n", "1. 了解什么是 Function Calling 技术\n", "2. 掌握如何通过基于 OpenAI、Function Calling 等技术让大模型能够回复基于私有化知识的问题\n"]}, {"cell_type": "markdown", "id": "a9777dbb", "metadata": {}, "source": ["# ===================================================================\n", "# 🎮 模块4: 控制台交互界面系统 - 完全重构版 v3.0\n", "# ===================================================================\n", "\n", "import sys\n", "import os\n", "import time\n", "import json\n", "import traceback\n", "from datetime import datetime, timedelta\n", "from typing import Dict, List, Any, Optional\n", "from pathlib import Path\n", "\n", "class PowerBankConsoleInterface:\n", "    \"\"\"控制台交互界面 - 企业级重构版\"\"\"\n", "    \n", "    def __init__(self, assistant):\n", "        \"\"\"初始化控制台界面\"\"\"\n", "        self.assistant = assistant\n", "        self.running = True\n", "        self.session_history = []\n", "        \n", "        # 会话统计\n", "        self.session_stats = {\n", "            'start_time': datetime.now(),\n", "            'total_queries': 0,\n", "            'successful_queries': 0,\n", "            'failed_queries': 0,\n", "            'commands_executed': 0,\n", "            'avg_response_time': 0\n", "        }\n", "        \n", "        # 颜色主题\n", "        self.colors = {\n", "            'cyan': '\\033[96m', 'green': '\\033[92m', 'yellow': '\\033[93m',\n", "            'red': '\\033[91m', 'blue': '\\033[94m', 'magenta': '\\033[95m',\n", "            'white': '\\033[97m', 'bold': '\\033[1m', 'end': '\\033[0m'\n", "        }\n", "        \n", "        # 验证助手状态\n", "        self._validate_assistant()\n", "        custom_print(\"🎮 控制台交互界面初始化完成\")\n", "    \n", "    def _validate_assistant(self):\n", "        \"\"\"验证助手状态\"\"\"\n", "        if not self.assistant:\n", "            custom_print(\"❌ 助手实例为空\")\n", "            return False\n", "        \n", "        # 确保助手有必要的方法\n", "        if not hasattr(self.assistant, 'analyze'):\n", "            def analyze_method(question: str):\n", "                if hasattr(self.assistant, 'intelligent_chat'):\n", "                    response = self.assistant.intelligent_chat(question)\n", "                    return {\"success\": True, \"response\": response, \"data\": []}\n", "                return {\"success\": False, \"response\": \"助手功能不可用\", \"data\": []}\n", "            self.assistant.analyze = analyze_method\n", "        \n", "        if not hasattr(self.assistant, 'get_system_status'):\n", "            def get_status_method():\n", "                return {\n", "                    \"initialization_complete\": True,\n", "                    \"modules_loaded\": [\"基础模式\"],\n", "                    \"database_status\": \"未知\",\n", "                    \"module_count\": 1\n", "                }\n", "            self.assistant.get_system_status = get_status_method\n", "        \n", "        return True\n", "    \n", "    def print_banner(self):\n", "        \"\"\"显示启动横幅\"\"\"\n", "        try:\n", "            status = self.assistant.get_system_status()\n", "            table_status = \"已连接\" if status.get('database_status') == \"已连接\" else \"未连接\"\n", "            module_count = status.get('module_count', 0)\n", "            health_score = \"优秀\" if status.get('initialization_complete') else \"一般\"\n", "        except:\n", "            table_status, module_count, health_score = \"未知\", 0, \"异常\"\n", "        \n", "        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "        \n", "        banner = f\"\"\"\n", "{self.colors['cyan']}╔══════════════════════════════════════════════════════════════════════╗\n", "║                🚀 共享充电宝智能数据分析系统 v3.0                      ║\n", "║                     PowerBank AI Assistant - Enterprise               ║\n", "╠══════════════════════════════════════════════════════════════════════╣\n", "║  🎯 功能: 智能问答 | 数据分析 | 业务洞察 | 批量处理 | 系统诊断         ║\n", "║  ⚡ 快捷: /help /demo /status /health /clear /quit                   ║\n", "║  📊 状态: 数据库({table_status}) | 模块({module_count}个) | 健康度({health_score})  ║\n", "║  🕐 时间: {current_time}                              ║\n", "╚══════════════════════════════════════════════════════════════════════╝{self.colors['end']}\n", "\n", "{self.colors['green']}🚀 系统已就绪，开始您的数据分析之旅！{self.colors['end']}\n", "{self.colors['yellow']}💡 提示: 输入 /help 查看所有可用命令{self.colors['end']}\n", "        \"\"\"\n", "        print(banner)\n", "    \n", "    def print_colored(self, text, color='white'):\n", "        \"\"\"打印彩色文本\"\"\"\n", "        color_code = self.colors.get(color, self.colors['white'])\n", "        print(f\"{color_code}{text}{self.colors['end']}\")\n", "    \n", "    def safe_input(self, prompt):\n", "        \"\"\"安全的输入函数\"\"\"\n", "        try:\n", "            return input(prompt)\n", "        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KeyboardInterrupt):\n", "            self.print_colored(\"\\n🔥 检测到中断信号\", 'yellow')\n", "            return '/quit'\n", "        except Exception as e:\n", "            self.print_colored(f\"\\n❌ 输入异常: {e}\", 'red')\n", "            return '/quit'\n", "    \n", "    def handle_command(self, command: str) -> bool:\n", "        \"\"\"处理快捷命令\"\"\"\n", "        command = command.lower().strip()\n", "        \n", "        try:\n", "            self.session_stats['commands_executed'] += 1\n", "            \n", "            if command in ['/quit', '/exit', 'quit', 'exit', '退出']:\n", "                self._show_session_summary()\n", "                self.running = False\n", "                return True\n", "            \n", "            elif command in ['/help', 'help', '帮助']:\n", "                self._show_help()\n", "                return True\n", "            \n", "            elif command in ['/clear', 'clear', '清屏']:\n", "                os.system('cls' if os.name == 'nt' else 'clear')\n", "                self.print_banner()\n", "                return True\n", "            \n", "            elif command in ['/status', 'status', '状态']:\n", "                self._show_system_status()\n", "                return True\n", "            \n", "            elif command in ['/health', 'health', '健康']:\n", "                self._show_health_check()\n", "                return True\n", "            \n", "            elif command in ['/demo', 'demo', '演示']:\n", "                self._run_demo()\n", "                return True\n", "            \n", "            elif command in ['/stats', 'stats', '统计']:\n", "                self._show_session_stats()\n", "                return True\n", "            \n", "            else:\n", "                self.print_colored(f\"❌ 未知命令: {command}\", 'red')\n", "                self.print_colored(\"💡 输入 /help 查看可用命令\", 'yellow')\n", "                return True\n", "                \n", "        except Exception as e:\n", "            self.print_colored(f\"❌ 命令执行异常: {e}\", 'red')\n", "            return True\n", "    \n", "    def _show_help(self):\n", "        \"\"\"显示帮助信息\"\"\"\n", "        help_text = f\"\"\"\n", "{self.colors['blue']}📖 系统帮助文档{self.colors['end']}\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "║  ⚡ 快捷命令:                                                        ║\n", "║    /help    - 显示此帮助信息                                         ║\n", "║    /status  - 查看系统状态                                           ║\n", "║    /health  - 系统健康检查                                           ║\n", "║    /demo    - 运行演示查询                                           ║\n", "║    /stats   - 查看会话统计                                           ║\n", "║    /clear   - 清屏并重新显示横幅                                     ║\n", "║    /quit    - 退出系统                                               ║\n", "║                                                                      ║\n", "║  📊 查询示例:                                                        ║\n", "║    • \"各品牌市场份额如何？\"                                          ║\n", "║    • \"用户年龄分布情况\"                                              ║\n", "║    • \"哪个地区收入最高？\"                                            ║\n", "║    • \"最近一个月的收入趋势\"                                          ║\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "        \"\"\"\n", "        print(help_text)\n", "    \n", "    def _show_system_status(self):\n", "        \"\"\"显示系统状态\"\"\"\n", "        try:\n", "            status = self.assistant.get_system_status()\n", "            \n", "            status_text = f\"\"\"\n", "{self.colors['blue']}📊 系统状态报告{self.colors['end']}\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "🕐 会话开始: {self.session_stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}\n", "💬 对话次数: {self.session_stats['total_queries']}\n", "🔧 加载模块: {status.get('module_count', 0)} 个\n", "📚 模块列表: {', '.join(status.get('modules_loaded', []))}\n", "🗄️ 数据库: {status.get('database_status', '未知')}\n", "✅ 初始化: {'完成' if status.get('initialization_complete') else '未完成'}\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "            \"\"\"\n", "            print(status_text)\n", "            \n", "        except Exception as e:\n", "            self.print_colored(f\"❌ 状态获取失败: {e}\", 'red')\n", "    \n", "    def _show_health_check(self):\n", "        \"\"\"显示健康检查\"\"\"\n", "        self.print_colored(\"🔍 执行系统健康检查...\", 'cyan')\n", "        \n", "        checks = [\n", "            (\"助手实例\", self.assistant is not None),\n", "            (\"分析功能\", hasattr(self.assistant, 'analyze')),\n", "            (\"状态查询\", hasattr(self.assistant, 'get_system_status')),\n", "            (\"会话统计\", len(self.session_history) >= 0)\n", "        ]\n", "        \n", "        for check_name, result in checks:\n", "            status = \"✅ 正常\" if result else \"❌ 异常\"\n", "            self.print_colored(f\"  {check_name}: {status}\", 'green' if result else 'red')\n", "    \n", "    def _run_demo(self):\n", "        \"\"\"运行演示查询\"\"\"\n", "        demo_questions = [\n", "            \"系统状态如何？\",\n", "            \"你好，请介绍一下系统功能\",\n", "            \"帮我分析一下数据\"\n", "        ]\n", "        \n", "        self.print_colored(\"🎬 开始演示查询...\", 'cyan')\n", "        \n", "        for i, question in enumerate(demo_questions, 1):\n", "            self.print_colored(f\"\\n📝 演示 {i}: {question}\", 'yellow')\n", "            \n", "            try:\n", "                start_time = time.time()\n", "                result = self.assistant.analyze(question)\n", "                processing_time = time.time() - start_time\n", "                \n", "                if result.get('success', False):\n", "                    self.print_colored(f\"✅ 回答: {result.get('response', '无响应')[:100]}...\", 'green')\n", "                    self.print_colored(f\"⏱️ 处理时间: {processing_time:.2f}秒\", 'blue')\n", "                else:\n", "                    self.print_colored(f\"❌ 演示失败: {result.get('response', '未知错误')}\", 'red')\n", "                    \n", "            except Exception as e:\n", "                self.print_colored(f\"❌ 演示异常: {e}\", 'red')\n", "            \n", "            time.sleep(1)  # 演示间隔\n", "    \n", "    def _show_session_stats(self):\n", "        \"\"\"显示会话统计\"\"\"\n", "        duration = datetime.now() - self.session_stats['start_time']\n", "        \n", "        stats_text = f\"\"\"\n", "{self.colors['magenta']}📈 会话统计信息{self.colors['end']}\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "⏱️ 会话时长: {str(duration).split('.')[0]}\n", "💬 总查询数: {self.session_stats['total_queries']}\n", "✅ 成功查询: {self.session_stats['successful_queries']}\n", "❌ 失败查询: {self.session_stats['failed_queries']}\n", "🔧 命令执行: {self.session_stats['commands_executed']}\n", "⚡ 平均响应: {self.session_stats['avg_response_time']:.2f}秒\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "        \"\"\"\n", "        print(stats_text)\n", "    \n", "    def process_query(self, question: str) -> bool:\n", "        \"\"\"处理用户查询\"\"\"\n", "        try:\n", "            self.session_stats['total_queries'] += 1\n", "            start_time = time.time()\n", "            \n", "            # 调用助手分析\n", "            result = self.assistant.analyze(question)\n", "            processing_time = time.time() - start_time\n", "            \n", "            # 更新统计\n", "            self.session_stats['avg_response_time'] = (\n", "                (self.session_stats['avg_response_time'] * (self.session_stats['total_queries'] - 1) + processing_time) \n", "                / self.session_stats['total_queries']\n", "            )\n", "            \n", "            if result.get('success', False):\n", "                self.session_stats['successful_queries'] += 1\n", "                self._display_success_result(question, result, processing_time)\n", "                return True\n", "            else:\n", "                self.session_stats['failed_queries'] += 1\n", "                self.print_colored(f\"❌ 查询失败: {result.get('response', '未知错误')}\", 'red')\n", "                return False\n", "                \n", "        except Exception as e:\n", "            self.session_stats['failed_queries'] += 1\n", "            self.print_colored(f\"❌ 查询异常: {e}\", 'red')\n", "            return False\n", "    \n", "    def _display_success_result(self, question: str, result: Dict, processing_time: float):\n", "        \"\"\"显示成功结果\"\"\"\n", "        print(f\"\\n{self.colors['cyan']}{'='*80}{self.colors['end']}\")\n", "        print(f\"{self.colors['green']}✅ 查询分析完成{self.colors['end']}\")\n", "        print(f\"{self.colors['cyan']}{'='*80}{self.colors['end']}\")\n", "        \n", "        print(f\"{self.colors['yellow']}📝 问题: {question}{self.colors['end']}\")\n", "        print(f\"{self.colors['blue']}⏱️ 处理时间: {processing_time:.2f}秒{self.colors['end']}\")\n", "        \n", "        # 显示回答\n", "        response = result.get('response', '无响应')\n", "        print(f\"\\n{self.colors['white']}💡 智能回答:{self.colors['end']}\")\n", "        print(f\"{response}\")\n", "        \n", "        # 显示数据（如果有）\n", "        data = result.get('data', [])\n", "        if data:\n", "            print(f\"\\n{self.colors['magenta']}📊 查询数据: {len(data)}条记录{self.colors['end']}\")\n", "        \n", "        print(f\"{self.colors['cyan']}{'='*80}{self.colors['end']}\")\n", "    \n", "    def _show_session_summary(self):\n", "        \"\"\"显示会话总结\"\"\"\n", "        duration = datetime.now() - self.session_stats['start_time']\n", "        success_rate = (self.session_stats['successful_queries'] / max(self.session_stats['total_queries'], 1)) * 100\n", "        \n", "        summary = f\"\"\"\n", "{self.colors['cyan']}╔══════════════════════════════════════════════════════════════════════╗\n", "║                        📊 会话总结报告                                ║\n", "╠══════════════════════════════════════════════════════════════════════╣\n", "║  ⏱️ 会话时长: {str(duration).split('.')[0]:<52} ║\n", "║  💬 总查询数: {self.session_stats['total_queries']:<52} ║\n", "║  ✅ 成功率: {success_rate:.1f}%{' ':<56} ║\n", "║  ⚡ 平均响应: {self.session_stats['avg_response_time']:.2f}秒{' ':<44} ║\n", "║                                                                      ║\n", "║  感谢使用共享充电宝智能数据分析系统！                                ║\n", "║  🎯 您的数据分析之旅表现优秀！                                       ║\n", "╚══════════════════════════════════════════════════════════════════════╝{self.colors['end']}\n", "        \"\"\"\n", "        print(summary)\n", "    \n", "    def run(self):\n", "        \"\"\"运行控制台界面\"\"\"\n", "        try:\n", "            # 显示启动横幅\n", "            self.print_banner()\n", "            \n", "            # 主循环\n", "            while self.running:\n", "                try:\n", "                    # 获取用户输入\n", "                    user_input = self.safe_input(f\"\\n{self.colors['green']}🤔 请输入您的问题 (或输入命令): {self.colors['end']}\").strip()\n", "                    \n", "                    if not user_input:\n", "                        continue\n", "                    \n", "                    # 处理命令\n", "                    if user_input.startswith('/') or user_input.lower() in ['quit', 'exit', 'help', '退出', '帮助']:\n", "                        if self.handle_command(user_input):\n", "                            continue\n", "                    \n", "                    # 处理普通查询\n", "                    self.process_query(user_input)\n", "                    \n", "                except KeyboardInterrupt:\n", "                    self.print_colored(\"\\n🔥 检测到中断信号，正在退出...\", 'yellow')\n", "                    break\n", "                except Exception as e:\n", "                    self.print_colored(f\"❌ 运行异常: {e}\", 'red')\n", "                    \n", "        except Exception as e:\n", "            self.print_colored(f\"❌ 界面运行失败: {e}\", 'red')\n", "        finally:\n", "            self.print_colored(\"👋 感谢使用智能数据分析系统！\", 'cyan')\n", "\n", "custom_print(\"✅ 模块4: 控制台交互界面系统 - 完全重构版 v3.0 加载完成\")\n", "custom_print(\"🎯 新特性: 智能分析增强 • 完善错误处理 • 性能统计 • 用户体验优化\")"]}, {"cell_type": "code", "execution_count": null, "id": "6ec731c8", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'AutoGen' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[16], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m AutoGen\n", "\u001b[1;31mNameError\u001b[0m: name 'AutoGen' is not defined"]}], "source": ["# AutoGen\n", "# Langchain + LangGraph\n", "# Coze"]}, {"cell_type": "markdown", "id": "2c48d2b1", "metadata": {}, "source": ["# 1.私有知识库问题提问"]}, {"cell_type": "markdown", "id": "0bd7aa2f", "metadata": {}, "source": ["导入相关模块"]}, {"cell_type": "code", "execution_count": 6, "id": "db9ea0a1", "metadata": {}, "outputs": [], "source": ["import os\n", "import io\n", "import json\n", "import inspect\n", "import numpy as np\n", "import pandas as pd\n", "import pymysql\n", "import openai\n", "from openai import OpenAI"]}, {"cell_type": "markdown", "id": "1a35b2ca", "metadata": {}, "source": ["创建客户端"]}, {"cell_type": "code", "execution_count": 7, "id": "4de1ed36", "metadata": {}, "outputs": [], "source": ["from rich.console import Console\n", "\n", "console = Console()\n", "\n", "def custom_print(info):\n", "    console.print(info)"]}, {"cell_type": "code", "execution_count": 9, "id": "6ce06f93", "metadata": {}, "outputs": [], "source": ["# 创建HTTP客户端时不使用proxies参数\n", "import httpx\n", "http_client = httpx.Client(follow_redirects=True)"]}, {"cell_type": "code", "execution_count": 10, "id": "c3bb069f", "metadata": {}, "outputs": [], "source": ["api_key = \"sk-5617b89342844050a48268b477327b44\"\n", "base_url = \"https://api.deepseek.com/v1\"\n", "\n", "# 使用自定义HTTP客户端创建OpenAI实例\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")"]}, {"cell_type": "markdown", "id": "1437a51b", "metadata": {}, "source": ["创建消息"]}, {"cell_type": "code", "execution_count": 11, "id": "300e88d2", "metadata": {}, "outputs": [], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "] "]}, {"cell_type": "code", "execution_count": 12, "id": "184dcb4a", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletion</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'5e078b93-2ef7-4f31-9ab5-a9391ad331f8'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">choices</span>=<span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Choice</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">finish_reason</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'stop'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">logprobs</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">message</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">COUNT(*) AS total_records \\nFROM </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">user_base_info;\\n```\\n\\n这条SQL会返回表中的总行数，列名为`total_records`。\\n\\n注意事项：\\n1. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">如果表非常大，COUNT(*)可能需要较长时间执行\\n2. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">某些数据库系统(如MySQL的InnoDB)在大表上执行COUNT(*)可能不是最优化的方式\\n3. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">如果只需要估算值，某些数据库系统提供近似计数函数(如PostgreSQL的pg_class.reltuples)\\n\\n您需要在实际数据库环境中执行</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">此查询才能得到准确结果。'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">]</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">created</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1752715554</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">model</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'deepseek-chat'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">object</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'chat.completion'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">service_tier</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">system_fingerprint</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'fp_8802369eaa_prod0623_fp8_kvcache'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">usage</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">CompletionUsage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">completion_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">134</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">total_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">161</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">completion_tokens_details</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_tokens_details</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">PromptTokensDetails</span><span style=\"font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">audio_tokens</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>, <span style=\"color: #808000; text-decoration-color: #808000\">cached_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"font-weight: bold\">)</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_cache_hit_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_cache_miss_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27</span>\n", "    <span style=\"font-weight: bold\">)</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletion\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mid\u001b[0m=\u001b[32m'5e078b93-2ef7-4f31-9ab5-a9391ad331f8'\u001b[0m,\n", "    \u001b[33mchoices\u001b[0m=\u001b[1m[\u001b[0m\n", "        \u001b[1;35mChoice\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mfinish_reason\u001b[0m=\u001b[32m'stop'\u001b[0m,\n", "            \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "            \u001b[33mlogprobs\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mmessage\u001b[0m=\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mcontent\u001b[0m=\u001b[32m'要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT \u001b[0m\n", "\u001b[32mCOUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records \\nFROM \u001b[0m\n", "\u001b[32muser_base_info;\\n```\\n\\n这条SQL会返回表中的总行数，列名为`total_records`。\\n\\n注意事项：\\n1. \u001b[0m\n", "\u001b[32m如果表非常大，COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m可能需要较长时间执行\\n2. \u001b[0m\n", "\u001b[32m某些数据库系统\u001b[0m\u001b[32m(\u001b[0m\u001b[32m如MySQL的InnoDB\u001b[0m\u001b[32m)\u001b[0m\u001b[32m在大表上执行COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m可能不是最优化的方式\\n3. \u001b[0m\n", "\u001b[32m如果只需要估算值，某些数据库系统提供近似计数函数\u001b[0m\u001b[32m(\u001b[0m\u001b[32m如PostgreSQL的pg_class.reltuples\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\\n\\n您需要在实际数据库环境中执行\u001b[0m\n", "\u001b[32m此查询才能得到准确结果。'\u001b[0m,\n", "                \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "                \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "                \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "                \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "                \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m]\u001b[0m,\n", "    \u001b[33mcreated\u001b[0m=\u001b[1;36m1752715554\u001b[0m,\n", "    \u001b[33mmodel\u001b[0m=\u001b[32m'deepseek-chat'\u001b[0m,\n", "    \u001b[33mobject\u001b[0m=\u001b[32m'chat.completion'\u001b[0m,\n", "    \u001b[33mservice_tier\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33msystem_fingerprint\u001b[0m=\u001b[32m'fp_8802369eaa_prod0623_fp8_kvcache'\u001b[0m,\n", "    \u001b[33musage\u001b[0m=\u001b[1;35mCompletionUsage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcompletion_tokens\u001b[0m=\u001b[1;36m134\u001b[0m,\n", "        \u001b[33mprompt_tokens\u001b[0m=\u001b[1;36m27\u001b[0m,\n", "        \u001b[33mtotal_tokens\u001b[0m=\u001b[1;36m161\u001b[0m,\n", "        \u001b[33mcompletion_tokens_details\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mprompt_tokens_details\u001b[0m=\u001b[1;35mPromptTokensDetails\u001b[0m\u001b[1m(\u001b[0m\u001b[33maudio_tokens\u001b[0m=\u001b[3;35mNone\u001b[0m, \u001b[33mcached_tokens\u001b[0m=\u001b[1;36m0\u001b[0m\u001b[1m)\u001b[0m,\n", "        \u001b[33mprompt_cache_hit_tokens\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "        \u001b[33mprompt_cache_miss_tokens\u001b[0m=\u001b[1;36m27\u001b[0m\n", "    \u001b[1m)\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=messages,\n", "    )\n", "custom_print(response)"]}, {"cell_type": "markdown", "id": "b090b8c7", "metadata": {}, "source": ["# 2.定义数据查询函数"]}, {"cell_type": "markdown", "id": "ef261cea", "metadata": {}, "source": ["## 2.1 定义函数"]}, {"cell_type": "markdown", "id": "b5b8f6e8", "metadata": {}, "source": ["定义函数。输入SQL，返回从MySQL查询的结果（以JSON格式返回）"]}, {"cell_type": "markdown", "id": "a75e4298", "metadata": {}, "source": ["预留一个问题：为什么我要编写函数字典，以及为什么参数有的是必选、有的是可选"]}, {"cell_type": "markdown", "id": "5fc7fd43", "metadata": {}, "source": ["补充说明：当 orient='records' 时，生成的 JSON 是一个数组，数组中的每个元素是 DataFrame 的一行，格式为键值对（列名: 值）。这种格式适合直接传递给前端或 API 接口"]}, {"cell_type": "code", "execution_count": 13, "id": "8b0dfa88", "metadata": {}, "outputs": [], "source": ["def get_user_base_info(sql,username='root',password='123456',db='user_info'):\n", "    \"\"\"\n", "    当前函数，用于查询指定数据库下的相关表中的数据\n", "    ：param sql：参数为必要参数,字符串类型的SQL语句,\n", "    ：param username：参数为可选参数,代表:用户名,字符串类型,\n", "    ：param password：参数为可选参数,代表:用户密码,字符串类型,\n", "    ：param db：参数为可选参数,代表:需要连接的数据库名字,字符串类型,\n", "    ：return:当前SQL语句执行完的查询结果\n", "    \"\"\"\n", "    conn = pymysql.connect(\n", "        host = 'localhost',\n", "        user = username,\n", "        password = password,\n", "        db = db,\n", "        charset = 'utf8'\n", "    )\n", "    try: \n", "        with conn.cursor() as cursor:\n", "            cursor.execute(sql)\n", "            results = cursor.fetchall()\n", "    finally:\n", "        cursor.close()\n", "        conn.close()\n", "    column_names = [desc[0] for desc in cursor.description]\n", "    print(type(column_names))\n", "    print(column_names)\n", "    print(\"===============================================================================\")\n", "    print(results)\n", "    df = pd.DataFrame(results,columns=column_names)\n", "    print(\"===============================================================================\")\n", "    print(df)\n", "    return df.to_json(orient = 'records')"]}, {"cell_type": "markdown", "id": "885c1b11", "metadata": {}, "source": ["## 2.2 测试函数"]}, {"cell_type": "markdown", "id": "42b829fc", "metadata": {}, "source": ["get_user_base_info函数准确性验证：对于函数准确性验证是开发过程中十分重要的环节，可以通过调用函数，传递一个SQL进行测试，如果函数编写正确，则会返回预期的结果。"]}, {"cell_type": "code", "execution_count": 14, "id": "3e433139", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n", "['Tables_in_user_info']\n", "===============================================================================\n", "(('is_survived',), ('user_base_info',), ('user_extend_info',))\n", "===============================================================================\n", "  Tables_in_user_info\n", "0         is_survived\n", "1      user_base_info\n", "2    user_extend_info\n"]}, {"data": {"text/plain": ["'[{\"Tables_in_user_info\":\"is_survived\"},{\"Tables_in_user_info\":\"user_base_info\"},{\"Tables_in_user_info\":\"user_extend_info\"}]'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_base_info('SHOW TABLES')"]}, {"cell_type": "markdown", "id": "23540a0c", "metadata": {}, "source": ["```\n", "column_names = [desc[0] for desc in cursor.description]\n", "    print(type(column_names))\n", "    print(column_names)\n", "    print(\"===============================================================================\")\n", "    print(results)\n", "    df = pd.DataFrame(results,columns=column_names)\n", "    print(\"===============================================================================\")\n", "    print(df)\n", "    return df.to_json(orient = 'records')\n", "```"]}, {"cell_type": "markdown", "id": "507cb1c6", "metadata": {}, "source": ["```\n", "'passenger_id', 'name', 'sex', 'age', 'sib_sp', 'parch'\n", "((1, '<PERSON><PERSON>, Mr. <PERSON>', 'male', 22, '1', '0'),\n", " (2, '<PERSON><PERSON><PERSON>s, Mrs. <PERSON> (<PERSON>)', 'female', 38, '1', '0'), \n", " (3, '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>', 'female', 26, '0', '0'))\n", "```"]}, {"cell_type": "code", "execution_count": 15, "id": "29c348ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n", "['passenger_id', 'name', 'sex', 'age', 'sib_sp', 'parch']\n", "===============================================================================\n", "((1, '<PERSON><PERSON>, Mr. <PERSON>', 'male', 22, '1', '0'), (2, '<PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (Florence <PERSON>)', 'female', 38, '1', '0'), (3, '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>', 'female', 26, '0', '0'))\n", "===============================================================================\n", "   passenger_id                                               name     sex  \\\n", "0             1                            <PERSON><PERSON>, Mr. <PERSON>    male   \n", "1             2  <PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...  female   \n", "2             3                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female   \n", "\n", "   age sib_sp parch  \n", "0   22      1     0  \n", "1   38      1     0  \n", "2   26      0     0  \n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON><PERSON>, Mr. <PERSON> </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">Harris\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"male\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">22</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"1\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Cumings, Mrs. John Bradley </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">(<PERSON>)\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"female\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"1\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Heikkinen, </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">Miss. Laina\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"female\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m1\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON>, Mr. <PERSON> \u001b[0m\n", "\u001b[32m<PERSON><PERSON><PERSON>\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"male\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m22\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"1\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m2\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> \u001b[0m\n", "\u001b[32m(\u001b[0m\u001b[32mFlorence <PERSON>\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"female\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m38\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"1\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m3\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON><PERSON><PERSON>, \u001b[0m\n", "\u001b[32mMiss. <PERSON><PERSON>\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"female\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m26\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"0\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(get_user_base_info('SELECT * FROM user_base_info LIMIT 3'))"]}, {"cell_type": "markdown", "id": "c833326f", "metadata": {}, "source": ["# 3.基于大模型的私有化知识查询"]}, {"cell_type": "markdown", "id": "255c3c1b", "metadata": {}, "source": ["## 3.1 定义 tools "]}, {"cell_type": "markdown", "id": "63839ef6", "metadata": {}, "source": ["def get_user_base_info(sql,username='root',password='123456',db='user_info'):"]}, {"cell_type": "code", "execution_count": 18, "id": "711bc484", "metadata": {}, "outputs": [], "source": ["tools = [{'type': 'function',\n", "         'function': {\n", "            'name': 'get_user_base_info',\n", "            'description': '当前函数，用于查询指定数据库下的相关表中的数据。输入参数为字符串类型的SQL语句，输出为当前SQL语句执行完的查询结果',\n", "            'parameters': {'type': 'object',\n", "                           'properties': {'sql': {'description': '必要参数，字符串类型的SQL语句','type': 'string'},\n", "                                          'username': {'description': '用户名,字符串类型','type': 'string'},\n", "                                          'password': {'description': '用户密码,字符串类型','type': 'string'},\n", "                                          'db': {'description': '需要连接的数据库名字,字符串类型','type': 'string'}\n", "                                         },\n", "         'required': ['sql']},\n", "        }}]"]}, {"cell_type": "markdown", "id": "a128e35d", "metadata": {}, "source": ["## 3.2 通过工具获取答案"]}, {"cell_type": "markdown", "id": "6d682b2a", "metadata": {}, "source": ["```\n", "messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "] \n", "```"]}, {"cell_type": "code", "execution_count": 12, "id": "969bea1b", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT COUNT(*) AS </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">total_records \\nFROM user_base_info;\\n```\\n\\n这个查询会返回表中的总行数，列名为 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">`total_records`。\\n\\n如果您需要更具体的统计信息，比如：\\n1. 按某个字段分组统计\\n2. 统计去重后的用户数\\n3. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">统计满足某些条件的记录数\\n\\n请告诉我您的具体需求，我可以提供更精确的查询语句。\\n\\n注意：实际执行此查询需要您有该表</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">的查询权限，且表名大小写需与实际数据库中的表名一致。'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m'要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS \u001b[0m\n", "\u001b[32mtotal_records \\nFROM user_base_info;\\n```\\n\\n这个查询会返回表中的总行数，列名为 \u001b[0m\n", "\u001b[32m`total_records`。\\n\\n如果您需要更具体的统计信息，比如：\\n1. 按某个字段分组统计\\n2. 统计去重后的用户数\\n3. \u001b[0m\n", "\u001b[32m统计满足某些条件的记录数\\n\\n请告诉我您的具体需求，我可以提供更精确的查询语句。\\n\\n注意：实际执行此查询需要您有该表\u001b[0m\n", "\u001b[32m的查询权限，且表名大小写需与实际数据库中的表名一致。'\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=messages,\n", "    )\n", "custom_print(response.choices[0].message)"]}, {"cell_type": "markdown", "id": "35d32a98", "metadata": {}, "source": ["def get_user_base_info(sql,username='root',password='123456',db='user_info'):"]}, {"cell_type": "code", "execution_count": 19, "id": "970ee49e", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_73169670-9e59-4bb8-b45a-55c004a9073c'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_base_info'</span>\n", "            <span style=\"font-weight: bold\">)</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">]</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "        \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mid\u001b[0m=\u001b[32m'call_0_73169670-9e59-4bb8-b45a-55c004a9073c'\u001b[0m,\n", "            \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                \u001b[33mname\u001b[0m=\u001b[32m'get_user_base_info'\u001b[0m\n", "            \u001b[1m)\u001b[0m,\n", "            \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "            \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m]\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=messages,\n", "        tools = tools,\n", "        tool_choice=\"auto\",\n", "    )\n", "custom_print(response.choices[0].message)"]}, {"cell_type": "code", "execution_count": 20, "id": "0ce37874", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n", "['total_records']\n", "===============================================================================\n", "((891,),)\n", "===============================================================================\n", "   total_records\n", "0            891\n"]}, {"data": {"text/plain": ["'[{\"total_records\":891}]'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_base_info(\"SELECT COUNT(*) AS total_records FROM user_base_info\")"]}, {"cell_type": "markdown", "id": "3718ebdf", "metadata": {}, "source": ["创建函数指针"]}, {"cell_type": "code", "execution_count": 21, "id": "74fef4c3", "metadata": {}, "outputs": [], "source": ["tools_list = {\"get_user_base_info\": get_user_base_info} "]}, {"cell_type": "markdown", "id": "6cfbdf8c", "metadata": {}, "source": ["获取函数名"]}, {"cell_type": "code", "execution_count": 23, "id": "36b286d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["get_user_base_info\n"]}], "source": ["func_name = response.choices[0].message.tool_calls[0].function.name\n", "print(func_name)"]}, {"cell_type": "markdown", "id": "5f38382f", "metadata": {}, "source": ["获取函数参数"]}, {"cell_type": "code", "execution_count": 24, "id": "94cb6a5f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT COUNT(*) AS total_records FROM user_base_info\n"]}], "source": ["func_args = json.loads(response.choices[0].message.tool_calls[0].function.arguments)['sql']\n", "print(func_args) "]}, {"cell_type": "markdown", "id": "788432c8", "metadata": {}, "source": ["获取函数执行结果"]}, {"cell_type": "markdown", "id": "fdb39781", "metadata": {}, "source": ["```\n", "tools_list['get_user_base_info'](\"SELECT COUNT(*) AS total_records FROM user_base_info\")\n", "get_user_base_info = tools_list['get_user_base_info']\n", "get_user_base_info(\"SELECT COUNT(*) AS total_records FROM user_base_info\")\n", "```"]}, {"cell_type": "code", "execution_count": 25, "id": "61d2d799", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n", "['total_records']\n", "===============================================================================\n", "((891,),)\n", "===============================================================================\n", "   total_records\n", "0            891\n", "[{\"total_records\":891}]\n"]}], "source": ["func_result = tools_list[func_name](func_args)\n", "print(func_result)"]}, {"cell_type": "markdown", "id": "7cf56cce", "metadata": {}, "source": ["## 3.3 消息二次封装"]}, {"cell_type": "code", "execution_count": 19, "id": "c4f71879", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_32b03601-484c-41aa-acdf-d6043a1aaae6'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_base_info'</span>\n", "            <span style=\"font-weight: bold\">)</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">]</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "        \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mid\u001b[0m=\u001b[32m'call_0_32b03601-484c-41aa-acdf-d6043a1aaae6'\u001b[0m,\n", "            \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                \u001b[33mname\u001b[0m=\u001b[32m'get_user_base_info'\u001b[0m\n", "            \u001b[1m)\u001b[0m,\n", "            \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "            \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m]\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["message_call = response.choices[0].message\n", "custom_print(message_call)"]}, {"cell_type": "code", "execution_count": 20, "id": "5844e4ed", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '你是大数据领域的数据专家,精通各种简单和复杂的数据分析'},\n", " {'role': 'user', 'content': '请问user_base_info数据表中一共有多少条数据？'}]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["messages"]}, {"cell_type": "code", "execution_count": 26, "id": "3ee7fdfe", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'请问user_base_info数据表中一共有多少条数据？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_73169670-9e59-4bb8-b45a-55c004a9073c'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_base_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_73169670-9e59-4bb8-b45a-55c004a9073c'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_base_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"total_records\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'请问user_base_info数据表中一共有多少条数据？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_73169670-9e59-4bb8-b45a-55c004a9073c'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_base_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_73169670-9e59-4bb8-b45a-55c004a9073c'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_base_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"total_records\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["message_call = response.choices[0].message\n", "tool_calls = message_call.tool_calls\n", "tool_call_id = tool_calls[0].id\n", "\n", "messages.append(message_call)\n", "\n", "messages.append(\n", "    {\n", "        \"role\":\"tool\",\n", "        \"tool_call_id\":tool_call_id,\n", "        \"name\":func_name,\n", "        \"content\":func_result\n", "    }\n", ")\n", "custom_print(messages)"]}, {"cell_type": "markdown", "id": "d175c5a0", "metadata": {}, "source": ["通过新封装的消息向大模型提问"]}, {"cell_type": "code", "execution_count": 27, "id": "535c7e35", "metadata": {}, "outputs": [{"data": {"text/plain": ["'user_base_info数据表中一共有891条数据记录。'"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["second_response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", ")\n", "second_response.choices[0].message.content"]}, {"cell_type": "markdown", "id": "90f28fab", "metadata": {}, "source": ["# 4. 问题"]}, {"cell_type": "markdown", "id": "97863f89", "metadata": {}, "source": ["4.1 为什么大模型要进行二次调用"]}, {"cell_type": "markdown", "id": "e15b0f15", "metadata": {}, "source": ["4.2 进行二次调用为什么要传递ChatCompletionMessage对象信息以及'role': 'tool'"]}, {"cell_type": "markdown", "id": "32be2ce8", "metadata": {}, "source": ["```\n", "作业: 自己声明一个函数 , auto_call(...)\n", "要求：用户发起一个请求(消息)，auto_call ---》 直接返回结果\n", "\n", "示例：\n", "用户：user_base_info表中存在多少条数据\n", "函数的返回值：user_base_info数据表中一共有891条记录。\n", "```"]}, {"cell_type": "markdown", "id": "04dcffbd", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "c124d723", "metadata": {}, "source": ["# 5.实践"]}, {"cell_type": "markdown", "id": "1fbcea0f", "metadata": {}, "source": ["```\n", "client:客户端\n", "messages:用户提问的问题\n", "tools:本地工具说明\n", "model:\"deepseek-chat\"\n", "需求：\n", "    tools不传递具体参数的时候，需要让大模型基于自己的知识自动的回复用户的问题\n", "    tools传递了具体参数的时候，需要让大模型自动的决定，要不要调用本地的工具\n", "最终效果：\n", "    用户输入问题\n", "    大模型返回最终的答案\n", "\n", "def auto_call(client,messages,tools=None,model='deepseek-chat')\n", "```\n"]}, {"cell_type": "markdown", "id": "5b60109d", "metadata": {}, "source": ["```\n", "用例：\n", "m1 = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "]\n", "custom_print(auto_call(client,m1,model='deepseek-chat'))\n", "答案:你可以执行XXXSQL进行查询;\n", "\n", "# 设计模式\n", "m1 = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "]\n", "custom_print(auto_call(client,m1,tools=tools,model='deepseek-chat'))\n", "\n", "答案: user_base_info数据表中存在891条数据\n", "\n", "\n", "```"]}, {"cell_type": "markdown", "id": "fixed_auto_call_section", "metadata": {}, "source": ["# 4. 修复后的完整auto_call函数\n", "\n", "## 问题分析\n", "原始代码的主要问题：\n", "1. 没有函数映射字典\n", "2. 没有真正执行SQL查询函数\n", "3. 缺少完整的Function Calling流程\n", "4. tools定义格式错误\n", "\n", "## 解决方案\n", "修复后的版本能够：\n", "- 真正执行数据库查询\n", "- 返回格式化的结果\n", "- 完整的错误处理"]}, {"cell_type": "code", "execution_count": null, "id": "fixed_auto_call_function", "metadata": {}, "outputs": [], "source": ["def auto_call(client, message):\n", "    \"\"\"\n", "    修复后的auto_call函数，能够真正执行SQL查询并返回结果\n", "    \n", "    主要修复内容：\n", "    1. 添加了函数映射字典 tools_list\n", "    2. 实现了完整的Function Calling流程\n", "    3. 真正执行数据库查询函数\n", "    4. 处理函数执行结果并返回给模型\n", "    5. 添加了错误处理机制\n", "    \"\"\"\n", "    # 函数映射字典 - 这是关键！\n", "    tools_list = {\"get_user_base_info\": get_user_base_info}\n", "    \n", "    tools = [\n", "        {\n", "            'type': 'function',\n", "            'function': {\n", "                'name': 'get_user_base_info',\n", "                'description': '查询指定数据库下的相关表中的数据',\n", "                'parameters': {\n", "                    'type': 'object',\n", "                    'properties': {\n", "                        'sql': {\n", "                            'description': '必要参数，字符串类型的SQL语句',\n", "                            'type': 'string'\n", "                        },\n", "                        'username': {\n", "                            'description': '用户名,字符串类型',\n", "                            'type': 'string'\n", "                        },\n", "                        'password': {\n", "                            'description': '用户密码,字符串类型',\n", "                            'type': 'string'\n", "                        },\n", "                        'db': {\n", "                            'description': '需要连接的数据库名字,字符串类型',\n", "                            'type': 'string'\n", "                        }\n", "                    },\n", "                    'required': ['sql']\n", "                }\n", "            }\n", "        }\n", "    ]\n", "\n", "    # 构建消息\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家, 精通各种简单和复杂的数据分析\"},\n", "        {\"role\": \"user\", \"content\": f\"请问{message}\"}\n", "    ]\n", "\n", "    print(\"🔄 第一次调用模型...\")\n", "    # 第一次调用模型\n", "    response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=messages,\n", "        tools=tools,\n", "        tool_choice=\"auto\"\n", "    )\n", "    \n", "    response_message = response.choices[0].message\n", "    \n", "    # 检查模型是否要调用函数\n", "    if response_message.tool_calls:\n", "        print(\"🔧 模型请求调用函数...\")\n", "        # 将模型的回复添加到消息历史中\n", "        messages.append(response_message)\n", "        \n", "        # 处理每个函数调用\n", "        for tool_call in response_message.tool_calls:\n", "            # 获取函数名和参数\n", "            func_name = tool_call.function.name\n", "            func_args = json.loads(tool_call.function.arguments)\n", "            \n", "            print(f\"📞 调用函数: {func_name}\")\n", "            print(f\"📋 函数参数: {func_args}\")\n", "            \n", "            # 执行函数\n", "            if func_name in tools_list:\n", "                try:\n", "                    print(\"⚡ 执行数据库查询...\")\n", "                    # 调用实际的数据库查询函数\n", "                    func_result = tools_list[func_name](**func_args)\n", "                    \n", "                    print(f\"✅ 查询成功\")\n", "                    \n", "                    # 将函数执行结果添加到消息历史中\n", "                    messages.append({\n", "                        \"role\": \"tool\",\n", "                        \"tool_call_id\": tool_call.id,\n", "                        \"name\": func_name,\n", "                        \"content\": func_result\n", "                    })\n", "                except Exception as e:\n", "                    print(f\"❌ 查询失败: {str(e)}\")\n", "                    # 如果函数执行出错，返回错误信息\n", "                    messages.append({\n", "                        \"role\": \"tool\",\n", "                        \"tool_call_id\": tool_call.id,\n", "                        \"name\": func_name,\n", "                        \"content\": f\"执行SQL查询时出错: {str(e)}\"\n", "                    })\n", "        \n", "        print(\"🔄 第二次调用模型生成最终回复...\")\n", "        # 第二次调用模型，让它基于函数结果生成最终回复\n", "        final_response = client.chat.completions.create(\n", "            model=\"deepseek-chat\",\n", "            messages=messages\n", "        )\n", "        \n", "        return final_response.choices[0].message.content\n", "    \n", "    # 如果模型没有调用函数，直接返回回复\n", "    print(\"💬 模型直接回复，未调用函数\")\n", "    return response_message.content"]}, {"cell_type": "markdown", "id": "test_section", "metadata": {}, "source": ["## 测试修复后的函数"]}, {"cell_type": "code", "execution_count": null, "id": "test_fixed_auto_call", "metadata": {}, "outputs": [], "source": ["# 测试修复后的auto_call函数\n", "print(\"🚀 开始测试修复后的auto_call函数...\")\n", "print(\"=\" * 80)\n", "\n", "# 确保所有必要的变量和函数都已定义\n", "try:\n", "    # 导入必要的模块\n", "    import os\n", "    import json\n", "    import httpx\n", "    import pandas as pd\n", "    import pymysql\n", "    from openai import OpenAI\n", "    from rich.console import Console\n", "    \n", "    # 确保custom_print函数存在\n", "    if 'custom_print' not in globals():\n", "        console = Console()\n", "        def custom_print(info):\n", "            console.print(info)\n", "    \n", "    # 确保get_user_base_info函数存在\n", "    if 'get_user_base_info' not in globals():\n", "        def get_user_base_info(sql,username='root',password='123456',db='user_info'):\n", "            conn = pymysql.connect(\n", "                host = 'localhost',\n", "                user = username,\n", "                password = password,\n", "                db = db,\n", "                charset = 'utf8'\n", "            )\n", "            try: \n", "                with conn.cursor() as cursor:\n", "                    cursor.execute(sql)\n", "                    results = cursor.fetchall()\n", "            finally:\n", "                cursor.close()\n", "                conn.close()\n", "            column_names = [desc[0] for desc in cursor.description]\n", "            df = pd.DataFrame(results,columns=column_names)\n", "            return df.to_json(orient = 'records')\n", "    \n", "    # 确保client变量已定义\n", "    if 'client' not in globals():\n", "        api_key = \"sk-5617b89342844050a48268b477327b44\"\n", "        base_url = \"https://api.deepseek.com/v1\"\n", "        http_client = httpx.Client(follow_redirects=True)\n", "        \n", "        client = OpenAI(\n", "            api_key=api_key,\n", "            base_url=base_url,\n", "            http_client=http_client\n", "        )\n", "        print(\"✅ Client已重新创建\")\n", "    else:\n", "        print(\"✅ Client已存在\")\n", "    \n", "    # 测试auto_call函数\n", "    result = auto_call(client, \"user_base_info数据表中一共有多少条数据\")\n", "    print(\"=\" * 80)\n", "    print(\"🎉 最终结果:\")\n", "    custom_print(result)\n", "    \n", "except Exception as e:\n", "    print(f\"❌ 调用出错: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "id": "39c262f6", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}