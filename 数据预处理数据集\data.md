# 表数据说明

当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，需要多表关联时，可以作为主键进行关联。

**user_base_info表存在6个字段，分别为 ：**

​	整数类型的 passenger_id 字段；

​	字符串类型的 name 字段；

​	字符串类型的 sex 字段；

​	整数类型的 age 字段；

​	字符串类型的 sib_sp 字段；

​	字符串类型的 parch 字段；

**user_extend_info表存在6个字段，分别为 ：**

​	整数类型的 passenger_id 字段；

​	字符串类型的 p_class 字段；

​	字符串类型的 ticket 字段；

​	字符串类型的 fare 字段；

​	字符串类型的 cabin 字段；

​	字符串类型的 embarked 字段；

**is_survived表存在2字段，分别为 ：**

​	整数类型的 passenger_id 字段；

​	字符串类型的 survived 字段；

**每个字段的含义如下：**

**PassengerId（乘客 ID）**

```
唯一标识编号。它主要用区分不同的乘客个体。
```

**Survived（是否幸存）**

```
它是一个二元变量，0 表示乘客没有在泰坦尼克号事故中幸存，1 表示乘客幸存下来。
```

**Pclass（客舱等级）**

```
这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。
1 代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；
2 代表二等舱；
3 代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存概率的潜在影响。
```

**Name（姓名）**

```
乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” 等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。
```

**Sex（性别）**

```
乘客的生理性别，只有 “male”（男性）和 “female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” 的救援原则，所以女性乘客的生存概率相对较高。
```

**Age（年龄）**

```
乘客的年龄。年龄是一个重要的连续变量，不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。
```

**SibSp（直系亲属数量）**

```
这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。
```

**Parch（父母 / 子女数量）**

```
表示乘客在船上的父母或子女的数量。和 SibSp 类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩子的安全而放弃自己的逃生机会。
```

**Ticket（船票号码）**

```
乘客的船票号码。船票号码本身可能没有直接的预测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响
```

**Fare（票价）**

```
乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经济实力的一个间接指标。
```

**Cabin（客舱号码）**

```
乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。
```

**Embarked（登船港口）**

```
乘客登船的港口，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。
```

