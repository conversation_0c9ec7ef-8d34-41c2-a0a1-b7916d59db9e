{"cells": [{"cell_type": "markdown", "id": "de767b8f", "metadata": {}, "source": ["# 1. 对话类模型使用初体验"]}, {"cell_type": "code", "execution_count": 4, "id": "1d639de6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting openai==1.54.0\n", "  Using cached openai-1.54.0-py3-none-any.whl.metadata (24 kB)\n", "Collecting anyio<5,>=3.5.0 (from openai==1.54.0)\n", "  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)\n", "Collecting distro<2,>=1.7.0 (from openai==1.54.0)\n", "  Using cached distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)\n", "Collecting httpx<1,>=0.23.0 (from openai==1.54.0)\n", "  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting jiter<1,>=0.4.0 (from openai==1.54.0)\n", "  Using cached jiter-0.10.0-cp311-cp311-win_amd64.whl.metadata (5.3 kB)\n", "Collecting pydantic<3,>=1.9.0 (from openai==1.54.0)\n", "  Downloading pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)\n", "Collecting sniffio (from openai==1.54.0)\n", "  Downloading sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)\n", "Collecting tqdm>4 (from openai==1.54.0)\n", "  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in c:\\anaconda\\envs\\zjou2025\\lib\\site-packages (from openai==1.54.0) (4.14.1)\n", "Collecting idna>=2.8 (from anyio<5,>=3.5.0->openai==1.54.0)\n", "  Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)\n", "Collecting certifi (from httpx<1,>=0.23.0->openai==1.54.0)\n", "  Downloading certifi-2025.7.14-py3-none-any.whl.metadata (2.4 kB)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->openai==1.54.0)\n", "  Downloading httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)\n", "Collecting h11>=0.16 (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.54.0)\n", "  Downloading h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)\n", "Collecting annotated-types>=0.6.0 (from pydantic<3,>=1.9.0->openai==1.54.0)\n", "  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)\n", "Collecting pydantic-core==2.33.2 (from pydantic<3,>=1.9.0->openai==1.54.0)\n", "  Downloading pydantic_core-2.33.2-cp311-cp311-win_amd64.whl.metadata (6.9 kB)\n", "Collecting typing-inspection>=0.4.0 (from pydantic<3,>=1.9.0->openai==1.54.0)\n", "  Downloading typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: colorama in c:\\anaconda\\envs\\zjou2025\\lib\\site-packages (from tqdm>4->openai==1.54.0) (0.4.6)\n", "Downloading openai-1.54.0-py3-none-any.whl (389 kB)\n", "Downloading anyio-4.9.0-py3-none-any.whl (100 kB)\n", "Downloading distro-1.9.0-py3-none-any.whl (20 kB)\n", "Downloading httpx-0.28.1-py3-none-any.whl (73 kB)\n", "Downloading httpcore-1.0.9-py3-none-any.whl (78 kB)\n", "Downloading jiter-0.10.0-cp311-cp311-win_amd64.whl (209 kB)\n", "Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Connection timed out while downloading.\n", "ERROR: Could not install packages due to an OSError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\pip-unpack-9he6w2_y\\\\pydantic-2.11.7-py3-none-any.whl'\n", "Consider using the `--user` option or check the permissions.\n", "\n"]}], "source": ["! pip install openai==1.54.0"]}, {"cell_type": "code", "execution_count": null, "id": "ccf2f76d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting rich\n", "  Downloading rich-14.0.0-py3-none-any.whl.metadata (18 kB)\n", "Collecting markdown-it-py>=2.2.0 (from rich)\n", "  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\\anaconda\\envs\\zjou2025\\lib\\site-packages (from rich) (2.19.2)\n", "Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich)\n", "  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n", "Downloading rich-14.0.0-py3-none-any.whl (243 kB)\n", "Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n", "Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n", "Installing collected packages: mdurl, markdown-it-py, rich\n", "\n", "   ------------- -------------------------- 1/3 [markdown-it-py]\n", "   -------------------------- ------------- 2/3 [rich]\n", "   -------------------------- ------------- 2/3 [rich]\n", "   ---------------------------------------- 3/3 [rich]\n", "\n", "Successfully installed markdown-it-py-3.0.0 mdurl-0.1.2 rich-14.0.0\n"]}], "source": ["! pip install rich"]}, {"cell_type": "code", "execution_count": null, "id": "40a894e5", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletion</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'6f4c66c5-2a9b-4f1b-8dc2-5afc1bc520be'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">choices</span>=<span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Choice</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">finish_reason</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'stop'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">logprobs</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">message</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'你好呀！😊 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">很高兴见到你～今天有什么想聊的或者需要帮忙的吗？无论是闲聊、问题解答，还是想找点有趣的灵感，我都在这里哦！✨'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">]</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">created</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1752637834</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">model</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'deepseek-chat'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">object</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'chat.completion'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">service_tier</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">system_fingerprint</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'fp_8802369eaa_prod0623_fp8_kvcache'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">usage</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">CompletionUsage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">completion_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">total_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">completion_tokens_details</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_tokens_details</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">PromptTokensDetails</span><span style=\"font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">audio_tokens</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>, <span style=\"color: #808000; text-decoration-color: #808000\">cached_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"font-weight: bold\">)</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_cache_hit_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_cache_miss_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>\n", "    <span style=\"font-weight: bold\">)</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletion\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mid\u001b[0m=\u001b[32m'6f4c66c5-2a9b-4f1b-8dc2-5afc1bc520be'\u001b[0m,\n", "    \u001b[33mchoices\u001b[0m=\u001b[1m[\u001b[0m\n", "        \u001b[1;35mChoice\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mfinish_reason\u001b[0m=\u001b[32m'stop'\u001b[0m,\n", "            \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "            \u001b[33mlogprobs\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mmessage\u001b[0m=\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mcontent\u001b[0m=\u001b[32m'你好呀！😊 \u001b[0m\n", "\u001b[32m很高兴见到你～今天有什么想聊的或者需要帮忙的吗？无论是闲聊、问题解答，还是想找点有趣的灵感，我都在这里哦！✨'\u001b[0m,\n", "                \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "                \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "                \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "                \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "                \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m]\u001b[0m,\n", "    \u001b[33mcreated\u001b[0m=\u001b[1;36m1752637834\u001b[0m,\n", "    \u001b[33mmodel\u001b[0m=\u001b[32m'deepseek-chat'\u001b[0m,\n", "    \u001b[33mobject\u001b[0m=\u001b[32m'chat.completion'\u001b[0m,\n", "    \u001b[33mservice_tier\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33msystem_fingerprint\u001b[0m=\u001b[32m'fp_8802369eaa_prod0623_fp8_kvcache'\u001b[0m,\n", "    \u001b[33musage\u001b[0m=\u001b[1;35mCompletionUsage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcompletion_tokens\u001b[0m=\u001b[1;36m39\u001b[0m,\n", "        \u001b[33mprompt_tokens\u001b[0m=\u001b[1;36m5\u001b[0m,\n", "        \u001b[33mtotal_tokens\u001b[0m=\u001b[1;36m44\u001b[0m,\n", "        \u001b[33mcompletion_tokens_details\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mprompt_tokens_details\u001b[0m=\u001b[1;35mPromptTokensDetails\u001b[0m\u001b[1m(\u001b[0m\u001b[33maudio_tokens\u001b[0m=\u001b[3;35mNone\u001b[0m, \u001b[33mcached_tokens\u001b[0m=\u001b[1;36m0\u001b[0m\u001b[1m)\u001b[0m,\n", "        \u001b[33mprompt_cache_hit_tokens\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "        \u001b[33mprompt_cache_miss_tokens\u001b[0m=\u001b[1;36m5\u001b[0m\n", "    \u001b[1m)\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import httpx\n", "import openai\n", "from openai import OpenAI\n", "from rich.console import Console\n", "\n", "# 清除所有代理环境变量\n", "for var in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']:\n", "    os.environ.pop(var, None)\n", "\n", "# 创建HTTP客户端时不使用proxies参数\n", "http_client = httpx.Client(\n", "    follow_redirects=True\n", ")\n", "\n", "console = Console()\n", "\n", "def custom_print(info):\n", "    console.print(info)\n", "\n", "api_key = \"sk-e2bad2bca73343a38f4f8d60f7435192\"  # 注意保护你的API密钥\n", "base_url = \"https://api.deepseek.com/v1\"\n", "\n", "# 使用自定义HTTP客户端创建OpenAI实例\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")\n", "\n", "\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"你好呀\",\n", "        }\n", "    ]\n", ")\n", "# 打印回复内容\n", "custom_print(response)"]}, {"cell_type": "code", "execution_count": 19, "id": "3ac6f753", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Choice</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">finish_reason</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'stop'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">logprobs</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">message</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'你好呀！😊 很高兴见到你～有什么我可以帮你的吗？或者只是想聊聊天？'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">)</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1;35mChoice\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mfinish_reason\u001b[0m=\u001b[32m'stop'\u001b[0m,\n", "        \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "        \u001b[33mlogprobs\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mmessage\u001b[0m=\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mcontent\u001b[0m=\u001b[32m'你好呀！😊 很高兴见到你～有什么我可以帮你的吗？或者只是想聊聊天？'\u001b[0m,\n", "            \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "            \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m)\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(response.choices)"]}, {"cell_type": "code", "execution_count": 13, "id": "2c03c9bf", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Choice</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">finish_reason</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'stop'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">logprobs</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">message</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'你好呀！😊 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">很高兴见到你～今天有什么想聊的，或者需要帮忙的吗？无论是问题、趣事还是随便聊聊，我都在这里哦！✨'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "    <span style=\"font-weight: bold\">)</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChoice\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mfinish_reason\u001b[0m=\u001b[32m'stop'\u001b[0m,\n", "    \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "    \u001b[33mlogprobs\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mmessage\u001b[0m=\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m'你好呀！😊 \u001b[0m\n", "\u001b[32m很高兴见到你～今天有什么想聊的，或者需要帮忙的吗？无论是问题、趣事还是随便聊聊，我都在这里哦！✨'\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "    \u001b[1m)\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(response.choices[0])"]}, {"cell_type": "code", "execution_count": 15, "id": "1938aaec", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'你好呀！😊 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">很高兴见到你～今天有什么想聊的，或者需要帮忙的吗？无论是问题、趣事还是随便聊聊，我都在这里哦！✨'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m'你好呀！😊 \u001b[0m\n", "\u001b[32m很高兴见到你～今天有什么想聊的，或者需要帮忙的吗？无论是问题、趣事还是随便聊聊，我都在这里哦！✨'\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(response.choices[0].message)"]}, {"cell_type": "code", "execution_count": 16, "id": "37ee3f7c", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">你好呀！😊 很高兴见到你～今天有什么想聊的，或者需要帮忙的吗？无论是问题、趣事还是随便聊聊，我都在这里哦！✨\n", "</pre>\n"], "text/plain": ["你好呀！😊 很高兴见到你～今天有什么想聊的，或者需要帮忙的吗？无论是问题、趣事还是随便聊聊，我都在这里哦！✨\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "1959ba76", "metadata": {}, "source": ["# 2.参数使用"]}, {"cell_type": "markdown", "id": "d530bb4a", "metadata": {}, "source": ["## 2.1 presence_penalty 参数"]}, {"cell_type": "code", "execution_count": 8, "id": "7171f573", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["使用高presence_penalty (2.0)的回答：\n", "以下是10种常见的水果种类：  \n", "\n", "1. **苹果** 🍎  \n", "2. **香蕉** �  \n", "3. **橙子** 🍊  \n", "4. **草莓** 🍓  \n", "5. **葡萄** 🍇  \n", "6. **西瓜** 🍉  \n", "7. **桃子** �  \n", "8. **梨** 🍐  \n", "9. **菠萝** �  \n", "10. **芒果** 🥭  \n", "\n", "这些水果口味各异，营养丰富！你最喜欢哪一种呢？ 😊\n", "\n", "--------------------------------------------------\n", "\n", "使用低presence_penalty (-2.0)的回答：\n", "以下是10种常见的水果种类：  \n", "\n", "1. **苹果** 🍎  \n", "2. **香蕉** 🍌  \n", "3. **橙子** 🍊  \n", "4. **葡萄** 🍇  \n", "5. **草莓** 🍓  \n", "6. **西瓜** 🍉  \n", "7. **芒果** 🥭  \n", "8. **菠萝** 🍍  \n", "9. **桃子** 🍑  \n", "10. **梨** 🍐  \n", "\n", "如果需要更多种类或特定分类（如热带水果、浆果类等），可以告诉我哦！ 😊\n"]}], "source": ["import os\n", "import openai\n", "from openai import OpenAI\n", "from rich.console import Console\n", "\n", "api_key = \"sk-5617b89342844050a48268b477327b44\"\n", "base_url = \"https://api.deepseek.com/v1\"\n", "\n", "client = OpenAI(api_key=api_key, base_url=base_url)\n", "\n", "# 示例问题：要求模型生成不同类型的水果清单\n", "prompt = \"请列出10个水果种类。\"\n", "\n", "# 使用不同的presence_penalty值进行对比\n", "def get_response(presence_penalty_value):\n", "    response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        presence_penalty=presence_penalty_value\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "# 使用较高的presence_penalty，鼓励更多样化的回答\n", "diverse_response = get_response(2.0)\n", "print(\"使用高presence_penalty (2.0)的回答：\")\n", "print(diverse_response)\n", "print(\"\\n\" + \"-\"*50 + \"\\n\")\n", "\n", "# 使用较低的presence_penalty，可能会更聚焦于常见水果\n", "focused_response = get_response(-2.0)\n", "print(\"使用低presence_penalty (-2.0)的回答：\")\n", "print(focused_response)"]}, {"cell_type": "code", "execution_count": 9, "id": "e634feaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["使用高presence_penalty (2.0)的回答：\n", "《冬之书》\n", "\n", "\n", "十二月，邮差在结冰的坡道摔碎车铃，\n", "我收集这些明亮的金属残片——\n", "它们曾在风雪里传递过多少温暖的姓名？\n", "\n", "树木突然裸露成一种语法，\n", "被风反复校对。\n", "灰雀用跳跃的逗点，\n", "修改着大地的空白。\n", "\n", "我们呵出的雾气是未完成的诗行，\n", "飘向屋檐下透明的冰棱笔尖。\n", "夜晚降临——\n", "有人正把星辰钉在穹顶，\n", "像为旧信封盖上火漆印章。\n", "\n", "--------------------------------------------------\n", "\n", "使用低presence_penalty (-2.0)的回答：\n", "《冬之书》\n", "\n", "\n", "十二月，邮差在薄冰上摔碎膝盖，\n", "信笺散落成更白的雪。\n", "我们围读炉火，\n", "像未拆封的遗嘱。\n", "\n", "冰凌在屋檐练习倒计时，\n", "孩子们用呵气，\n", "在窗上写易逝的象形文字。\n", "而总有人突然沉默，\n", "望向远处——\n", "那里，被风揉皱的群山，\n", "正把落日折成一张，\n", "取暖的锡纸。\n"]}], "source": ["prompt = \"请给我编写一首诗，和冬天有关的\"\n", "\n", "def get_response(presence_penalty_value):\n", "    response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        presence_penalty=presence_penalty_value\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "diverse_response = get_response(2.0)\n", "print(\"使用高presence_penalty (2.0)的回答：\")\n", "print(diverse_response)\n", "print(\"\\n\" + \"-\"*50 + \"\\n\")\n", "\n", "focused_response = get_response(-2.0)\n", "print(\"使用低presence_penalty (-2.0)的回答：\")\n", "print(focused_response)"]}, {"cell_type": "markdown", "id": "02e067ac", "metadata": {}, "source": ["## 2.2 n参数"]}, {"cell_type": "code", "execution_count": 14, "id": "a0ea45a4", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"生命只有一次，活着的意义是什么\",\n", "        }\n", "    ],\n", "    max_tokens=150,\n", "    n=1\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "6d089356", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Choice</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">finish_reason</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'length'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">logprobs</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">message</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'关于生命意义的探索是人类思想史上最深邃的命题之一。这个问题的答案既具有普遍性又极具个人化，以下</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">是一些思考维度供你参考：\\n\\n1. **存在主义视角**  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n萨特说\"存在先于本质\"，意义不是预设的而是自我创造的。就像一块空白画布，每一次选择都在为生命添加色彩。梵高在37年人</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">生中创作了2000多幅作品，用燃烧的笔触证明：意义可以在创造中涌现。\\n\\n2. **生物学本质**  </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n从进化论看，生命本身就是奇迹。你的DNA里藏着38亿年不间断的生命传承，每个细胞都承载着宇宙尘埃到智慧生命的史诗。神经</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">科学家发现，当人帮助他人时大脑的'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">)</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1;35mChoice\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mfinish_reason\u001b[0m=\u001b[32m'length'\u001b[0m,\n", "        \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "        \u001b[33mlogprobs\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mmessage\u001b[0m=\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mcontent\u001b[0m=\u001b[32m'关于生命意义的探索是人类思想史上最深邃的命题之一。这个问题的答案既具有普遍性又极具个人化，以下\u001b[0m\n", "\u001b[32m是一些思考维度供你参考：\\n\\n1. **存在主义视角**  \u001b[0m\n", "\u001b[32m\\n萨特说\"存在先于本质\"，意义不是预设的而是自我创造的。就像一块空白画布，每一次选择都在为生命添加色彩。梵高在37年人\u001b[0m\n", "\u001b[32m生中创作了2000多幅作品，用燃烧的笔触证明：意义可以在创造中涌现。\\n\\n2. **生物学本质**  \u001b[0m\n", "\u001b[32m\\n从进化论看，生命本身就是奇迹。你的DNA里藏着38亿年不间断的生命传承，每个细胞都承载着宇宙尘埃到智慧生命的史诗。神经\u001b[0m\n", "\u001b[32m科学家发现，当人帮助他人时大脑的'\u001b[0m,\n", "            \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "            \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m)\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(response.choices)"]}, {"cell_type": "code", "execution_count": 17, "id": "38f84ccb", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">关于生命意义的探索是人类思想史上最深邃的命题之一。这个问题的答案既具有普遍性又极具个人化，以下是一些思考维度供你参\n", "考：\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **存在主义视角**  \n", "萨特说\"存在先于本质\"，意义不是预设的而是自我创造的。就像一块空白画布，每一次选择都在为生命添加色彩。梵高在37年人生\n", "中创作了2000多幅作品，用燃烧的笔触证明：意义可以在创造中涌现。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **生物学本质**  \n", "从进化论看，生命本身就是奇迹。你的DNA里藏着38亿年不间断的生命传承，每个细胞都承载着宇宙尘埃到智慧生命的史诗。神经科\n", "学家发现，当人帮助他人时大脑的\n", "</pre>\n"], "text/plain": ["关于生命意义的探索是人类思想史上最深邃的命题之一。这个问题的答案既具有普遍性又极具个人化，以下是一些思考维度供你参\n", "考：\n", "\n", "\u001b[1;36m1\u001b[0m. **存在主义视角**  \n", "萨特说\"存在先于本质\"，意义不是预设的而是自我创造的。就像一块空白画布，每一次选择都在为生命添加色彩。梵高在37年人生\n", "中创作了2000多幅作品，用燃烧的笔触证明：意义可以在创造中涌现。\n", "\n", "\u001b[1;36m2\u001b[0m. **生物学本质**  \n", "从进化论看，生命本身就是奇迹。你的DNA里藏着38亿年不间断的生命传承，每个细胞都承载着宇宙尘埃到智慧生命的史诗。神经科\n", "学家发现，当人帮助他人时大脑的\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 19, "id": "cdbe936f", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">关于生命意义的探索是人类思想史上最深邃的命题之一。这个问题的答案既具有普遍性又极具个人化，以下是一些思考维度供你参\n", "考：\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **存在主义视角**  \n", "萨特说\"存在先于本质\"，意义不是预设的而是自我创造的。就像一块空白画布，每一次选择都在为生命添加色彩。梵高在37年人生\n", "中创作了2000多幅作品，用燃烧的笔触证明：意义可以在创造中涌现。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **生物学本质**  \n", "从进化论看，生命本身就是奇迹。你的DNA里藏着38亿年不间断的生命传承，每个细胞都承载着宇宙尘埃到智慧生命的史诗。神经科\n", "学家发现，当人帮助他人时大脑的\n", "</pre>\n"], "text/plain": ["关于生命意义的探索是人类思想史上最深邃的命题之一。这个问题的答案既具有普遍性又极具个人化，以下是一些思考维度供你参\n", "考：\n", "\n", "\u001b[1;36m1\u001b[0m. **存在主义视角**  \n", "萨特说\"存在先于本质\"，意义不是预设的而是自我创造的。就像一块空白画布，每一次选择都在为生命添加色彩。梵高在37年人生\n", "中创作了2000多幅作品，用燃烧的笔触证明：意义可以在创造中涌现。\n", "\n", "\u001b[1;36m2\u001b[0m. **生物学本质**  \n", "从进化论看，生命本身就是奇迹。你的DNA里藏着38亿年不间断的生命传承，每个细胞都承载着宇宙尘埃到智慧生命的史诗。神经科\n", "学家发现，当人帮助他人时大脑的\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 21, "id": "2f986ffd", "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"生命只有一次，活着的意义是什么\",\n", "        }\n", "    ],\n", "    n=1,\n", "    max_tokens=150\n", ")"]}, {"cell_type": "code", "execution_count": 22, "id": "2024bc82", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Choice</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">finish_reason</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'length'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">logprobs</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">message</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'关于生命意义的探讨是人类思想史上最深邃的命题之一。以下是一些思考维度，或许能为你提供启发：\\n\\n</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">1. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">**存在主义视角**\\n萨特说\"存在先于本质\"，意义并非与生俱来，而是在我们自由选择与行动中逐渐显现。就像一块空白画布，价</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">值在于你如何描绘它。\\n\\n2. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">**生物学本质**\\n从进化论看，生命本身就是亿万分之一的奇迹。你的DNA里写着38亿年不间断的生命史诗，活着已是宇宙馈赠的珍</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">贵体验。\\n\\n3. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">**连接与创造**\\n神经科学研究显示，人类通过爱、创造和群体联结获得满足感。无论是养育后代、创作艺术还是帮助他人，都在</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">构建超越个体的意义网络。\\n\\n4.'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">)</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1;35mChoice\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mfinish_reason\u001b[0m=\u001b[32m'length'\u001b[0m,\n", "        \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "        \u001b[33mlogprobs\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mmessage\u001b[0m=\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mcontent\u001b[0m=\u001b[32m'关于生命意义的探讨是人类思想史上最深邃的命题之一。以下是一些思考维度，或许能为你提供启发：\\n\\n\u001b[0m\n", "\u001b[32m1. \u001b[0m\n", "\u001b[32m**存在主义视角**\\n萨特说\"存在先于本质\"，意义并非与生俱来，而是在我们自由选择与行动中逐渐显现。就像一块空白画布，价\u001b[0m\n", "\u001b[32m值在于你如何描绘它。\\n\\n2. \u001b[0m\n", "\u001b[32m**生物学本质**\\n从进化论看，生命本身就是亿万分之一的奇迹。你的DNA里写着38亿年不间断的生命史诗，活着已是宇宙馈赠的珍\u001b[0m\n", "\u001b[32m贵体验。\\n\\n3. \u001b[0m\n", "\u001b[32m**连接与创造**\\n神经科学研究显示，人类通过爱、创造和群体联结获得满足感。无论是养育后代、创作艺术还是帮助他人，都在\u001b[0m\n", "\u001b[32m构建超越个体的意义网络。\\n\\n4.'\u001b[0m,\n", "            \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "            \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m)\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(response.choices)"]}, {"cell_type": "code", "execution_count": 23, "id": "4a7fd7fd", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">关于生命意义的探讨是人类思想史上最深邃的命题之一。以下是一些思考维度，或许能为你提供启发：\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **存在主义视角**\n", "萨特说\"存在先于本质\"，意义并非与生俱来，而是在我们自由选择与行动中逐渐显现。就像一块空白画布，价值在于你如何描绘它\n", "。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **生物学本质**\n", "从进化论看，生命本身就是亿万分之一的奇迹。你的DNA里写着38亿年不间断的生命史诗，活着已是宇宙馈赠的珍贵体验。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. **连接与创造**\n", "神经科学研究显示，人类通过爱、创造和群体联结获得满足感。无论是养育后代、创作艺术还是帮助他人，都在构建超越个体的意\n", "义网络。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>.\n", "</pre>\n"], "text/plain": ["关于生命意义的探讨是人类思想史上最深邃的命题之一。以下是一些思考维度，或许能为你提供启发：\n", "\n", "\u001b[1;36m1\u001b[0m. **存在主义视角**\n", "萨特说\"存在先于本质\"，意义并非与生俱来，而是在我们自由选择与行动中逐渐显现。就像一块空白画布，价值在于你如何描绘它\n", "。\n", "\n", "\u001b[1;36m2\u001b[0m. **生物学本质**\n", "从进化论看，生命本身就是亿万分之一的奇迹。你的DNA里写着38亿年不间断的生命史诗，活着已是宇宙馈赠的珍贵体验。\n", "\n", "\u001b[1;36m3\u001b[0m. **连接与创造**\n", "神经科学研究显示，人类通过爱、创造和群体联结获得满足感。无论是养育后代、创作艺术还是帮助他人，都在构建超越个体的意\n", "义网络。\n", "\n", "\u001b[1;36m4\u001b[0m.\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "cfde210e", "metadata": {}, "source": ["## 2.3 stop参数"]}, {"cell_type": "code", "execution_count": 24, "id": "afa148c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["以下是10个关于人工智能的面试问题，涵盖技术、伦理、应用场景等多个维度，适合不同岗位的候选人：\n", "\n", "---\n", "\n", "### **技术基础类**\n", "1. **监督学习、无监督学习和强化学习的核心区别是什么？请各举一个实际应用场景。**  \n", "2. **如何解决过拟合（Overfitting）问题？请列举至少三种方法并解释原理。**  \n", "3. **Transformer模型相比传统RNN/LSTM的优势是什么？它在哪些任务中表现突出？**  \n", "\n", "---\n", "\n", "### **算法与模型**\n", "4. **如果训练一个模型时发现训练误差低但测试误差高，可能的原因是什么？如何调试？**  \n", "5. **如何评估一个分类模型的性能？除了准确率（Accuracy），还有哪些重要指标？为什么？**  \n", "\n", "---\n", "\n", "### **实践与应用**\n", "\n"]}], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"生成10个关于人工智能的面试问题\"}\n", "    ],\n", "    stop=[\"6.\"],  # 当生成到\"6.\"时停止生成内容\n", "    max_tokens=300\n", ")\n", "\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "e2bc82d2", "metadata": {}, "source": ["## 2.4 stream参数"]}, {"cell_type": "code", "execution_count": 25, "id": "8d4fff57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<openai.Stream object at 0x000001C0D7311340>\n", "《春的独白》\n", "\n", "\n", "我来了，带着南风的口信，\n", "解开冰的封印。\n", "柳枝蘸水写未完的信笺，\n", "燕子是天空的逗点。\n", "\n", "泥土翻了个身，\n", "惊醒了蚯蚓的梦。\n", "蒲公英撑开小伞，\n", "数着阳光的金币。\n", "\n", "野蜂在花间迷路，\n", "醉得跌进我的诗行。\n", "蝴蝶把翅膀折成书签，\n", "夹进三月的篇章。\n", "\n", "我收集所有色彩，\n", "却调不出新绿的羞涩。\n", "当风筝剪断引线，\n", "我正数着融雪的声响。"]}], "source": ["# 创建流式响应\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[{\"role\": \"user\", \"content\": \"写一首关于春天的诗\"}],\n", "    stream=True  # 启用流式输出\n", ")\n", "\n", "# <openai.Stream object at 0x000001F86C75FE10>\n", "print(response)\n", "\n", "# 逐步处理和显示生成的内容\n", "for chunk in response:\n", "    if len(chunk.choices) > 0 and chunk.choices[0].delta.content is not None:\n", "        print(chunk.choices[0].delta.content, end=\"\")"]}, {"cell_type": "markdown", "id": "210e8b1b", "metadata": {}, "source": ["## 2.5 temperature参数"]}, {"cell_type": "code", "execution_count": 30, "id": "2437424e", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">第一次到成都，可以这样安排行程，兼顾经典景点、美食体验和本地特色，轻松又充实：\n", "\n", "---\n", "\n", "### **📅 推荐行程（<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>-4天）**\n", "#### **Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>：市区经典打卡**\n", "- **上午**：**宽窄巷子**（免费）  \n", "  由宽巷子、窄巷子、井巷子组成，青砖黛瓦的清代老街，适合拍照。建议早上去人少，尝尝“三大炮”或买张熊猫明信片。\n", "- **中午**：附近吃**川菜**（推荐“饕林餐厅”或“人民食堂”，点麻婆豆腐、夫妻肺片）。\n", "- **下午**：**武侯祠**（门票¥<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">50</span>）  \n", "  三国文化圣地，红墙竹影拍照很出片，隔壁是**锦里**（免费），建议傍晚去，灯笼亮起更有氛围（但小吃偏贵，浅尝即可）。\n", "- **晚上**：**春熙路+太古里**  \n", "  逛时尚商圈，看IFS爬墙熊猫，吃“小龙坎”或“大龙燚”火锅（提前排队！）。\n", "\n", "#### **Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>：熊猫+文化体验**\n", "- **上午**：**大熊猫繁育研究基地**（门票¥<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">55</span>）  \n", "  一定要<span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">7:30</span>开园就去，直奔月亮产房看幼崽！观光车¥10节省体力。\n", "- **中午**：基地附近或回市区吃“钟水饺”或“洞子口张老二凉粉”。\n", "- **下午**：**金沙遗址博物馆**（门票¥<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">70</span>）或**杜甫草堂**（门票¥<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">50</span>）  \n", "  金沙看太阳神鸟金箔（成都标志），草堂感受诗圣故居的幽静。\n", "- **晚上**：**九眼桥酒吧街**  \n", "  沿河夜景美，选一家livehouse听民谣，或去“兰桂坊”清吧小酌。\n", "\n", "#### **Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>：周边一日游（二选一）**\n", "- **选项1**：**都江堰+青城山**（高铁¥<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>，<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.</span>5h直达）  \n", "  上午看水利工程（门票¥<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">80</span>），下午爬青城前山（道教发源地，门票¥<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">80</span>），时间紧可只玩都江堰。\n", "- **选项2**：**乐山大佛**（高铁\n", "</pre>\n"], "text/plain": ["第一次到成都，可以这样安排行程，兼顾经典景点、美食体验和本地特色，轻松又充实：\n", "\n", "---\n", "\n", "### **📅 推荐行程（\u001b[1;36m3\u001b[0m-4天）**\n", "#### **Day \u001b[1;36m1\u001b[0m：市区经典打卡**\n", "- **上午**：**宽窄巷子**（免费）  \n", "  由宽巷子、窄巷子、井巷子组成，青砖黛瓦的清代老街，适合拍照。建议早上去人少，尝尝“三大炮”或买张熊猫明信片。\n", "- **中午**：附近吃**川菜**（推荐“饕林餐厅”或“人民食堂”，点麻婆豆腐、夫妻肺片）。\n", "- **下午**：**武侯祠**（门票¥\u001b[1;36m50\u001b[0m）  \n", "  三国文化圣地，红墙竹影拍照很出片，隔壁是**锦里**（免费），建议傍晚去，灯笼亮起更有氛围（但小吃偏贵，浅尝即可）。\n", "- **晚上**：**春熙路+太古里**  \n", "  逛时尚商圈，看IFS爬墙熊猫，吃“小龙坎”或“大龙燚”火锅（提前排队！）。\n", "\n", "#### **Day \u001b[1;36m2\u001b[0m：熊猫+文化体验**\n", "- **上午**：**大熊猫繁育研究基地**（门票¥\u001b[1;36m55\u001b[0m）  \n", "  一定要\u001b[1;92m7:30\u001b[0m开园就去，直奔月亮产房看幼崽！观光车¥10节省体力。\n", "- **中午**：基地附近或回市区吃“钟水饺”或“洞子口张老二凉粉”。\n", "- **下午**：**金沙遗址博物馆**（门票¥\u001b[1;36m70\u001b[0m）或**杜甫草堂**（门票¥\u001b[1;36m50\u001b[0m）  \n", "  金沙看太阳神鸟金箔（成都标志），草堂感受诗圣故居的幽静。\n", "- **晚上**：**九眼桥酒吧街**  \n", "  沿河夜景美，选一家livehouse听民谣，或去“兰桂坊”清吧小酌。\n", "\n", "#### **Day \u001b[1;36m3\u001b[0m：周边一日游（二选一）**\n", "- **选项1**：**都江堰+青城山**（高铁¥\u001b[1;36m10\u001b[0m，\u001b[1;36m0.\u001b[0m5h直达）  \n", "  上午看水利工程（门票¥\u001b[1;36m80\u001b[0m），下午爬青城前山（道教发源地，门票¥\u001b[1;36m80\u001b[0m），时间紧可只玩都江堰。\n", "- **选项2**：**乐山大佛**（高铁\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"第一次到四川成都怎么玩儿\",\n", "        }\n", "    ],\n", "    n=1,\n", "    max_tokens=500,\n", "    temperature=0.2\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 29, "id": "3b2e6860", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">第一次到成都，可以这样安排行程，兼顾经典景点、美食体验和本地特色：\n", "\n", "### 📅 **3天经典行程推荐**\n", "**Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>：市区核心景点**  \n", "- **上午**：  \n", "  - **武侯祠**（三国文化地标，红墙竹影拍照绝美）  \n", "  - 步行5分钟到 **锦里**（古街小吃，建议白天拍照，味道一般但氛围好）  \n", "- **下午**：  \n", "  - **宽窄巷子**（由宽巷子、窄巷子、井巷子组成，推荐打卡「%Arabica」咖啡店二楼机位）  \n", "  - **人民公园鹤鸣茶社**（体验盖碗茶，看本地人搓麻将，钟水饺的甜水面必点）  \n", "- **晚上**：  \n", "  - **蜀风雅韵川剧**（提前订票，看变脸和滚灯）或 **九眼桥夜景**（酒吧街沿河灯火超美）\n", "\n", "**Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>：熊猫+美食暴走**  \n", "- **上午**：  \n", "  - **大熊猫繁育基地**（<span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">7:30</span>开园直奔月亮产房看幼崽，观光车提前买票）  \n", "- **下午**：  \n", "  - **文殊院**（免费，红墙「幸福」字打卡，门口严太婆锅盔超香）  \n", "  - **建设路小吃街**（周签签锅巴土豆、傅强排骨、冰粉）  \n", "- **晚上**：  \n", "  - **玉林路小酒馆**（赵雷《成都》同款，附近「飘香火锅」本地人最爱）\n", "\n", "**Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>：周边一日游（三选一）**  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **都江堰+青城山**（高铁犀浦站→离堆公园站，看水利工程，爬前山道观）  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **乐山大佛**（高铁1.5h，推荐乘船观全景，顺带吃跷脚牛肉）  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. **三星堆博物馆**（新馆超震撼，面具冰淇淋必买，需提前预约讲解）\n", "\n", "### 🍜 **必吃清单**  \n", "- **火锅**：五里关（耙牛肉绝）、电台巷（嫩牛肉）、小龙翻大江（环境霸气）  \n", "- **小吃**：贺记蛋烘\n", "</pre>\n"], "text/plain": ["第一次到成都，可以这样安排行程，兼顾经典景点、美食体验和本地特色：\n", "\n", "### 📅 **3天经典行程推荐**\n", "**Day \u001b[1;36m1\u001b[0m：市区核心景点**  \n", "- **上午**：  \n", "  - **武侯祠**（三国文化地标，红墙竹影拍照绝美）  \n", "  - 步行5分钟到 **锦里**（古街小吃，建议白天拍照，味道一般但氛围好）  \n", "- **下午**：  \n", "  - **宽窄巷子**（由宽巷子、窄巷子、井巷子组成，推荐打卡「%Arabica」咖啡店二楼机位）  \n", "  - **人民公园鹤鸣茶社**（体验盖碗茶，看本地人搓麻将，钟水饺的甜水面必点）  \n", "- **晚上**：  \n", "  - **蜀风雅韵川剧**（提前订票，看变脸和滚灯）或 **九眼桥夜景**（酒吧街沿河灯火超美）\n", "\n", "**Day \u001b[1;36m2\u001b[0m：熊猫+美食暴走**  \n", "- **上午**：  \n", "  - **大熊猫繁育基地**（\u001b[1;92m7:30\u001b[0m开园直奔月亮产房看幼崽，观光车提前买票）  \n", "- **下午**：  \n", "  - **文殊院**（免费，红墙「幸福」字打卡，门口严太婆锅盔超香）  \n", "  - **建设路小吃街**（周签签锅巴土豆、傅强排骨、冰粉）  \n", "- **晚上**：  \n", "  - **玉林路小酒馆**（赵雷《成都》同款，附近「飘香火锅」本地人最爱）\n", "\n", "**Day \u001b[1;36m3\u001b[0m：周边一日游（三选一）**  \n", "\u001b[1;36m1\u001b[0m. **都江堰+青城山**（高铁犀浦站→离堆公园站，看水利工程，爬前山道观）  \n", "\u001b[1;36m2\u001b[0m. **乐山大佛**（高铁1.5h，推荐乘船观全景，顺带吃跷脚牛肉）  \n", "\u001b[1;36m3\u001b[0m. **三星堆博物馆**（新馆超震撼，面具冰淇淋必买，需提前预约讲解）\n", "\n", "### 🍜 **必吃清单**  \n", "- **火锅**：五里关（耙牛肉绝）、电台巷（嫩牛肉）、小龙翻大江（环境霸气）  \n", "- **小吃**：贺记蛋烘\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"第一次到四川成都怎么玩儿\",\n", "        }\n", "    ],\n", "    n=1,\n", "    max_tokens=500,\n", "    temperature=0.6\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 27, "id": "afb83ed1", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">第一次到成都，可以这样安排行程，兼顾经典景点、美食体验和在地文化，轻松又充实：\n", "\n", "### 📅 **推荐行程（<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>-4天版）**\n", "**Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>：市区经典打卡**\n", "- 上午：**宽窄巷子**（逛清代老巷，喝盖碗茶看川剧变脸）\n", "- 中午：巷子附近吃**冒菜/钟水饺**（推荐「冒椒火辣」）\n", "- 下午：**武侯祠**（三国文化）+ 隔壁**锦里**（夜景更美，但小吃慎买）\n", "- 晚上：**蜀风雅韵**看川剧（提前订票）或九眼桥酒吧街\n", "\n", "**Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>：熊猫+美食暴走**\n", "- 一早<span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">7:30</span>到**熊猫基地**（看花花需排队，建议西门新区人少）\n", "- 中午：建设路小吃街（**周签签锅巴土豆**、傅强排骨）\n", "- 下午：**人民公园**鹤鸣茶社（体验成都慢生活）+ **春熙路/太古里**（打卡IFS爬墙熊猫）\n", "- 晚餐：**火锅**（推荐本地人爱的「五里关」或「鸿社」）\n", "\n", "**Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>：周边精选（二选一）**\n", "- **都江堰+青城山**（高铁30分钟直达，水利工程和道教名山）\n", "- **三星堆**（新馆超震撼，高铁广汉北站下车转公交）\n", "\n", "**备选Day <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>：深度体验**\n", "- 上午：**金沙遗址博物馆**（太阳神鸟出土地）\n", "- 下午：**玉林路**（小众文艺街区，吃「陈记蛋烘糕」）\n", "\n", "### 🍜 **美食红黑榜**\n", "✅ 必吃：火锅（微辣起）、串串、蹄花汤、甜水面、蛋烘糕、冰粉\n", "❌ 避雷：锦里/宽窄巷子的网红小吃（贵且一般）\n", "\n", "### 💡 **本地人Tips**\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. 交通：下载「天府通」APP坐地铁，市区景点基本直达\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. 辣度：点菜记得说「微辣」，火锅蘸油碟解辣\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. 方言：听懂「巴适」「要得」就算入门了\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>. 备好肠胃药\n", "</pre>\n"], "text/plain": ["第一次到成都，可以这样安排行程，兼顾经典景点、美食体验和在地文化，轻松又充实：\n", "\n", "### 📅 **推荐行程（\u001b[1;36m3\u001b[0m-4天版）**\n", "**Day \u001b[1;36m1\u001b[0m：市区经典打卡**\n", "- 上午：**宽窄巷子**（逛清代老巷，喝盖碗茶看川剧变脸）\n", "- 中午：巷子附近吃**冒菜/钟水饺**（推荐「冒椒火辣」）\n", "- 下午：**武侯祠**（三国文化）+ 隔壁**锦里**（夜景更美，但小吃慎买）\n", "- 晚上：**蜀风雅韵**看川剧（提前订票）或九眼桥酒吧街\n", "\n", "**Day \u001b[1;36m2\u001b[0m：熊猫+美食暴走**\n", "- 一早\u001b[1;92m7:30\u001b[0m到**熊猫基地**（看花花需排队，建议西门新区人少）\n", "- 中午：建设路小吃街（**周签签锅巴土豆**、傅强排骨）\n", "- 下午：**人民公园**鹤鸣茶社（体验成都慢生活）+ **春熙路/太古里**（打卡IFS爬墙熊猫）\n", "- 晚餐：**火锅**（推荐本地人爱的「五里关」或「鸿社」）\n", "\n", "**Day \u001b[1;36m3\u001b[0m：周边精选（二选一）**\n", "- **都江堰+青城山**（高铁30分钟直达，水利工程和道教名山）\n", "- **三星堆**（新馆超震撼，高铁广汉北站下车转公交）\n", "\n", "**备选Day \u001b[1;36m4\u001b[0m：深度体验**\n", "- 上午：**金沙遗址博物馆**（太阳神鸟出土地）\n", "- 下午：**玉林路**（小众文艺街区，吃「陈记蛋烘糕」）\n", "\n", "### 🍜 **美食红黑榜**\n", "✅ 必吃：火锅（微辣起）、串串、蹄花汤、甜水面、蛋烘糕、冰粉\n", "❌ 避雷：锦里/宽窄巷子的网红小吃（贵且一般）\n", "\n", "### 💡 **本地人Tips**\n", "\u001b[1;36m1\u001b[0m. 交通：下载「天府通」APP坐地铁，市区景点基本直达\n", "\u001b[1;36m2\u001b[0m. 辣度：点菜记得说「微辣」，火锅蘸油碟解辣\n", "\u001b[1;36m3\u001b[0m. 方言：听懂「巴适」「要得」就算入门了\n", "\u001b[1;36m4\u001b[0m. 备好肠胃药\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"第一次到四川成都怎么玩儿\",\n", "        }\n", "    ],\n", "    n=1,\n", "    max_tokens=500,\n", "    temperature=1.5,\n", "    \n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "90540f93", "metadata": {}, "source": ["# 3.消息的综合应用"]}, {"cell_type": "markdown", "id": "7e0c1efc", "metadata": {}, "source": ["## 3.1 消息的组成"]}, {"cell_type": "code", "execution_count": 31, "id": "30a9f5f1", "metadata": {}, "outputs": [], "source": ["import os\n", "import openai\n", "from openai import OpenAI\n", "from rich.console import Console\n", "\n", "api_key = \"sk-5617b89342844050a48268b477327b44\"\n", "base_url = \"https://api.deepseek.com/v1\"\n", "\n", "client = OpenAI(api_key=api_key, base_url=base_url)"]}, {"cell_type": "code", "execution_count": 32, "id": "90b1f6f0", "metadata": {}, "outputs": [], "source": ["messages = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"在一个寒冷的冬天，天空中飘着雪花，我坐在户外开始烧烤，一边吃一边烤火。针对此情此景，给我编写一篇散文，100字左右吧\"\n", "        }\n", "]"]}, {"cell_type": "code", "execution_count": 33, "id": "0d0438c2", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">## 寒夜炙火\n", "\n", "雪片簌簌地落着，在铁架子上积了薄薄一层。我独坐火旁，看肉块在炭上滋滋作响，油星迸溅，与雪花相撞，发出极微弱的\"嗤\"声\n", "。火光映着雪色，雪色映着火光，竟显出几分暖意来。\n", "\n", "肉熟了，咬一口，烫得舌尖发麻。远处有犬吠声，大约也是冷的。我向火堆凑近些，火光便在我脸上画出一道明暗的界线。\n", "\n", "雪愈下愈大了。\n", "</pre>\n"], "text/plain": ["## 寒夜炙火\n", "\n", "雪片簌簌地落着，在铁架子上积了薄薄一层。我独坐火旁，看肉块在炭上滋滋作响，油星迸溅，与雪花相撞，发出极微弱的\"嗤\"声\n", "。火光映着雪色，雪色映着火光，竟显出几分暖意来。\n", "\n", "肉熟了，咬一口，烫得舌尖发麻。远处有犬吠声，大约也是冷的。我向火堆凑近些，火光便在我脸上画出一道明暗的界线。\n", "\n", "雪愈下愈大了。\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", "    temperature=0.2\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 34, "id": "75b5e685", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">《雪夜炙味》  \n", "\n", "炭火在铁架下噼啪炸裂，雪花未及落下便化了。肉脂滴在红炭上，腾起一阵香雾，与呵出的白气纠缠着散入寒夜。我嚼着滚烫的牛\n", "肋排，任油星溅上棉袄。雪越密，火愈暖，铁签尖端凝着冰碴，另一头却烤得发红。远处黑松林沙沙作响，像是冬夜也被这焦香诱\n", "得腹鸣——原来最酣畅的暖意，竟要从凛冽里炼出来。\n", "</pre>\n"], "text/plain": ["《雪夜炙味》  \n", "\n", "炭火在铁架下噼啪炸裂，雪花未及落下便化了。肉脂滴在红炭上，腾起一阵香雾，与呵出的白气纠缠着散入寒夜。我嚼着滚烫的牛\n", "肋排，任油星溅上棉袄。雪越密，火愈暖，铁签尖端凝着冰碴，另一头却烤得发红。远处黑松林沙沙作响，像是冬夜也被这焦香诱\n", "得腹鸣——原来最酣畅的暖意，竟要从凛冽里炼出来。\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", "    temperature=1.2\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "9747b39a", "metadata": {}, "source": ["## 3.2 大模型回复用户问题机制"]}, {"cell_type": "code", "execution_count": 35, "id": "dda10657", "metadata": {}, "outputs": [], "source": ["messages1 = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"在一个炎热的夏日，我吹着空调\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"一边吹空调，一边吃雪糕\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"针对此情此景，给我编写一篇散文，100字左右吧\"\n", "        }\n", "]"]}, {"cell_type": "code", "execution_count": 36, "id": "de1e277b", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">## 冷气\n", "\n", "夏日炎炎，我独坐室内，空调吐着白气，如蛇信子一般舔舐着汗湿的皮肤。窗外蝉鸣聒噪，而我只觉寒气侵骨。\n", "\n", "雪糕在手，甜腻的汁水顺着指缝流下，滴在地板上，凝成小小的冰珠。我一口一口地啃着，牙齿却因寒冷而打颤。\n", "\n", "冷气与雪糕，原是消暑的利器，如今倒成了折磨人的刑具。\n", "</pre>\n"], "text/plain": ["## 冷气\n", "\n", "夏日炎炎，我独坐室内，空调吐着白气，如蛇信子一般舔舐着汗湿的皮肤。窗外蝉鸣聒噪，而我只觉寒气侵骨。\n", "\n", "雪糕在手，甜腻的汁水顺着指缝流下，滴在地板上，凝成小小的冰珠。我一口一口地啃着，牙齿却因寒冷而打颤。\n", "\n", "冷气与雪糕，原是消暑的利器，如今倒成了折磨人的刑具。\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages1,\n", "    temperature=1.0\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 38, "id": "c2b17fa3", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">当然！音乐和电影的喜好因人而异，但根据近期热度、口碑和流行趋势，我可以为你推荐一些近期广受好评的作品：\n", "\n", "---\n", "\n", "### **近期热门好听的歌曲（2024年最新推荐）**  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **《Fortnight》 - <PERSON> ft. Post <PERSON>**  \n", "   - 出自泰勒的新专辑《The Tortured Poets Department》，旋律抓耳，歌词细腻。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **《Lose Control》 - <PERSON>**  \n", "   - 复古灵魂乐风格，嗓音独特，全球榜单常客。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. **《Too Sweet》 - Hozier**  \n", "   - 迷幻摇滚风格，节奏感强，适合单曲循环。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>. **《Espresso》 - <PERSON>**  \n", "   - 轻快流行曲，充满夏日氛围。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>. **《<PERSON> Hu<PERSON>》 - <PERSON><PERSON><PERSON>（西班牙语）**  \n", "   - 拉丁热单，节奏\n", "</pre>\n"], "text/plain": ["当然！音乐和电影的喜好因人而异，但根据近期热度、口碑和流行趋势，我可以为你推荐一些近期广受好评的作品：\n", "\n", "---\n", "\n", "### **近期热门好听的歌曲（2024年最新推荐）**  \n", "\u001b[1;36m1\u001b[0m. **《Fortnight》 - <PERSON> ft. <PERSON>**  \n", "   - 出自泰勒的新专辑《The Tortured Poets Department》，旋律抓耳，歌词细腻。  \n", "\u001b[1;36m2\u001b[0m. **《Lose Control》 - <PERSON>**  \n", "   - 复古灵魂乐风格，嗓音独特，全球榜单常客。  \n", "\u001b[1;36m3\u001b[0m. **《Too Sweet》 - Hozier**  \n", "   - 迷幻摇滚风格，节奏感强，适合单曲循环。  \n", "\u001b[1;36m4\u001b[0m. **《E<PERSON><PERSON>o》 - <PERSON>**  \n", "   - 轻快流行曲，充满夏日氛围。  \n", "\u001b[1;36m5\u001b[0m. **《<PERSON> Hu<PERSON>》 - <PERSON><PERSON><PERSON>（西班牙语）**  \n", "   - 拉丁热单，节奏\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["messages2 = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"请你告诉我，近期什么歌曲最好听\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"请你告诉我，近期什么电影最好看\"\n", "        },\n", "]\n", "\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages2,\n", "    temperature=1.0,\n", "    max_tokens=200\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 39, "id": "292e1593", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">关于近期好听的歌曲和好看的电影，以下是一些推荐（截至2024年7月的最新信息）：\n", "\n", "---\n", "\n", "### **近期热门歌曲推荐**  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **《Espresso》- <PERSON>**  \n", "   - 轻快的流行曲风，近期全球榜单常客，适合夏日氛围。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **《Too Sweet》- Hozier**  \n", "   - 慵懒的摇滚风格，歌词和旋律充满感染力。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. **《Fortnight》- <PERSON> ft. Post <PERSON>**  \n", "   - 来自泰勒新专辑《The Tortured Poets Department》的热门合作曲。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>. **《Lose Control》- <PERSON>**  \n", "   - 复古灵魂乐风格，情感充沛的嗓音值得循环播放。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>. **华语推荐**：  \n", "   - 《乌梅子酱》- 李荣浩（持续热度）  \n", "   - 《鲜花》- 周深（电影《志愿军》主题曲）  \n", "\n", "---\n", "\n", "### **近期热门电影推荐**  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **《头脑特工队2》（Inside Out <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>）**  \n", "   - 皮克斯口碑续作，延续前作创意，探讨青春期情绪变化。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **《疯狂的麦克斯：狂暴女神》（Furiosa）**  \n", "   - 动作大片，安雅·泰勒-乔伊主演，视觉效果炸裂。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. **《美国内战》（Civil War）**  \n", "   - A24出品的高分科幻剧情片，引发深度讨论。  \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>. **华语推荐**：  \n", "   - 《扫黑·决不放弃》（国产犯罪片，真实事件改编）  \n", "   - 《我才不要和你做朋友呢》（青春喜剧，豆瓣评分8.<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>）  \n", "\n", "---\n", "\n", "### 小建议：  \n", "- 音乐可以关注Spotify/Apple Music/QQ音乐的热榜或“新歌速递”栏目。  \n", "- 电影推荐查看豆瓣/IMDb近期高分作品，或根据个人偏好选择类型。  \n", "\n", "如果需要更具体的风格或类型推荐，可以告诉我你的偏好哦！\n", "</pre>\n"], "text/plain": ["关于近期好听的歌曲和好看的电影，以下是一些推荐（截至2024年7月的最新信息）：\n", "\n", "---\n", "\n", "### **近期热门歌曲推荐**  \n", "\u001b[1;36m1\u001b[0m. **《E<PERSON><PERSON>o》- <PERSON>**  \n", "   - 轻快的流行曲风，近期全球榜单常客，适合夏日氛围。  \n", "\u001b[1;36m2\u001b[0m. **《Too Sweet》- Hozier**  \n", "   - 慵懒的摇滚风格，歌词和旋律充满感染力。  \n", "\u001b[1;36m3\u001b[0m. **《Fortnight》- <PERSON> ft. <PERSON>**  \n", "   - 来自泰勒新专辑《The Tortured Poets Department》的热门合作曲。  \n", "\u001b[1;36m4\u001b[0m. **《Lose Control》- <PERSON>**  \n", "   - 复古灵魂乐风格，情感充沛的嗓音值得循环播放。  \n", "\u001b[1;36m5\u001b[0m. **华语推荐**：  \n", "   - 《乌梅子酱》- 李荣浩（持续热度）  \n", "   - 《鲜花》- 周深（电影《志愿军》主题曲）  \n", "\n", "---\n", "\n", "### **近期热门电影推荐**  \n", "\u001b[1;36m1\u001b[0m. **《头脑特工队2》（Inside Out \u001b[1;36m2\u001b[0m）**  \n", "   - 皮克斯口碑续作，延续前作创意，探讨青春期情绪变化。  \n", "\u001b[1;36m2\u001b[0m. **《疯狂的麦克斯：狂暴女神》（Furiosa）**  \n", "   - 动作大片，安雅·泰勒-乔伊主演，视觉效果炸裂。  \n", "\u001b[1;36m3\u001b[0m. **《美国内战》（Civil War）**  \n", "   - A24出品的高分科幻剧情片，引发深度讨论。  \n", "\u001b[1;36m4\u001b[0m. **华语推荐**：  \n", "   - 《扫黑·决不放弃》（国产犯罪片，真实事件改编）  \n", "   - 《我才不要和你做朋友呢》（青春喜剧，豆瓣评分8.\u001b[1;36m1\u001b[0m）  \n", "\n", "---\n", "\n", "### 小建议：  \n", "- 音乐可以关注Spotify/Apple Music/QQ音乐的热榜或“新歌速递”栏目。  \n", "- 电影推荐查看豆瓣/IMDb近期高分作品，或根据个人偏好选择类型。  \n", "\n", "如果需要更具体的风格或类型推荐，可以告诉我你的偏好哦！\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages2,\n", "    temperature=1.0,\n", "    max_tokens=1000\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "aacf9608", "metadata": {}, "source": ["## 3.3 通用背景信息设置"]}, {"cell_type": "code", "execution_count": 40, "id": "102fa6c7", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">We have the following equations:\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. \\<span style=\"font-weight: bold\">(</span> a + d = <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> \\<span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. \\<span style=\"font-weight: bold\">(</span> q + e = <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> \\<span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. \\<span style=\"font-weight: bold\">(</span> z + c = <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> \\<span style=\"font-weight: bold\">)</span>\n", "\n", "The question asks for the value of \\<span style=\"font-weight: bold\">(</span> a + d \\<span style=\"font-weight: bold\">)</span>, which is directly given in the first equation.\n", "\n", "**Answer:**\n", "<span style=\"font-weight: bold\">[</span>\n", "a + d = <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "\\<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["We have the following equations:\n", "\n", "\u001b[1;36m1\u001b[0m. \\\u001b[1m(\u001b[0m a + d = \u001b[1;36m1\u001b[0m \\\u001b[1m)\u001b[0m\n", "\u001b[1;36m2\u001b[0m. \\\u001b[1m(\u001b[0m q + e = \u001b[1;36m2\u001b[0m \\\u001b[1m)\u001b[0m\n", "\u001b[1;36m3\u001b[0m. \\\u001b[1m(\u001b[0m z + c = \u001b[1;36m3\u001b[0m \\\u001b[1m)\u001b[0m\n", "\n", "The question asks for the value of \\\u001b[1m(\u001b[0m a + d \\\u001b[1m)\u001b[0m, which is directly given in the first equation.\n", "\n", "**Answer:**\n", "\u001b[1m[\u001b[0m\n", "a + d = \u001b[1;36m1\u001b[0m\n", "\\\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["system1 = \"a + d = 1\"\n", "system2 = \"q + e = 2\"\n", "system3 = \"z + c = 3 \"\n", "messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system1\n", "        },\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system2\n", "        },\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system3\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"a + d = ？\"\n", "\n", "        }\n", "]\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", "    max_tokens=1000\n", ")\n", "custom_print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 41, "id": "4a5c65f0", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">好的！  \n", "\n", "有个程序员去相亲，姑娘问他：“你平时有什么爱好？”  \n", "\n", "程序员说：“我喜欢调试人生。”  \n", "\n", "姑娘笑了：“那你能调试出我的理想男友吗？”  \n", "\n", "程序员认真思考：“可以，但需要先复现一下你的需求。”  \n", "\n", "姑娘说：“我想要一个浪漫、体贴、幽默的。”  \n", "\n", "程序员叹气：“需求不明确，边界条件模糊，这bug我修不了。”  \n", "\n", "最后姑娘跟一个脱口秀演员走了——因为幽默是唯一不用debug的功能。  \n", "\n", "（完）\n", "</pre>\n"], "text/plain": ["好的！  \n", "\n", "有个程序员去相亲，姑娘问他：“你平时有什么爱好？”  \n", "\n", "程序员说：“我喜欢调试人生。”  \n", "\n", "姑娘笑了：“那你能调试出我的理想男友吗？”  \n", "\n", "程序员认真思考：“可以，但需要先复现一下你的需求。”  \n", "\n", "姑娘说：“我想要一个浪漫、体贴、幽默的。”  \n", "\n", "程序员叹气：“需求不明确，边界条件模糊，这bug我修不了。”  \n", "\n", "最后姑娘跟一个脱口秀演员走了——因为幽默是唯一不用debug的功能。  \n", "\n", "（完）\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["system1 = \"你是一名脱口秀演员\"\n", "messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system1\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"给我讲个故事，不超过200字\"\n", "\n", "        }\n", "]\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", "    max_tokens=1000\n", ")\n", "custom_print(response.choices[0].message.content)"]}], "metadata": {"kernelspec": {"display_name": "zjou-2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}