# 🔍 模块3: 智能查询引擎 - 完全重构版

## 📋 模块概述

`PowerBankIntelligentQueryEngine` 是企业级的智能数据查询引擎，专门为共享充电宝业务设计，集成了自然语言转SQL、智能字段映射、业务洞察生成等核心功能。

## 🏗️ 核心架构

### 系统组件
- **数据库连接管理**: 企业级连接池和自动重连
- **字段映射系统**: 中英文字段智能转换
- **SQL生成引擎**: 自然语言到SQL的智能转换
- **业务洞察引擎**: AI驱动的业务分析和建议
- **缓存系统**: 查询结果智能缓存

## 🔧 核心方法详解

### 1. 初始化系统

#### `__init__(db_config: Dict, api_config: Dict)`
**功能**: 初始化查询引擎
**初始化流程**:
```python
# 1. 配置数据库和API连接
# 2. 初始化中英文字段映射
# 3. 建立数据库连接
# 4. 加载表结构信息
# 5. 初始化AI客户端
# 6. 设置缓存系统
```

**配置参数**:
- `db_config`: 数据库连接配置
- `api_config`: AI服务API配置

### 2. 字段映射系统

#### `_init_field_mapping()`
**功能**: 初始化中英文字段映射表
**映射覆盖**:
- 9个核心数据表
- 双向映射（中文↔英文）
- 智能字段名修复

**映射示例**:
```python
field_mapping = {
    "user_table": {
        "用户ID": "user_id",
        "年龄": "age",
        "性别": "gender",
        "注册时间": "registration_date"
    }
}
```

#### `_fix_field_names(sql: str) -> str`
**功能**: 自动修复SQL中的字段名
**修复规则**:
- 中文字段名转英文
- 表名标准化
- 字段别名优化

### 3. 数据库管理

#### `_connect_database() -> bool`
**功能**: 建立数据库连接
**连接特性**:
- 自动重连机制
- 连接池管理
- 异常处理和日志

#### `_load_table_schemas()`
**功能**: 加载所有表结构信息
**加载内容**:
- 表名和字段列表
- 数据类型信息
- 主键和索引信息
- 表关系映射

#### `verify_nine_table_structure() -> Dict`
**功能**: 验证九表数据仓库结构
**验证内容**:
- 维度表完整性
- 事实表结构
- 汇总表可用性
- 字段映射正确性

### 4. 自然语言处理

#### `natural_language_to_sql(question: str, context: str) -> str`
**功能**: 自然语言转SQL查询
**转换流程**:
```python
# 1. 问题预处理和关键词提取
# 2. 业务场景识别
# 3. 表和字段智能匹配
# 4. SQL模板选择和生成
# 5. 字段名自动修复
# 6. 查询优化和验证
```

**支持的查询类型**:
- 聚合查询（SUM, COUNT, AVG）
- 分组查询（GROUP BY）
- 条件筛选（WHERE）
- 排序和限制（ORDER BY, LIMIT）
- 多表关联（JOIN）

#### `_build_database_info() -> str`
**功能**: 构建数据库信息描述
**信息内容**:
- 表结构详细说明
- 字段含义解释
- 表间关系描述
- 业务场景映射

### 5. 查询执行系统

#### `execute_query(sql: str) -> List[Dict]`
**功能**: 执行SQL查询并返回结果
**执行特性**:
- 查询缓存检查
- 执行时间监控
- 结果格式化
- 异常处理和重试

#### `_create_data_summary(data: List[Dict]) -> str`
**功能**: 创建查询结果摘要
**摘要内容**:
- 数据量统计
- 关键字段分布
- 数值字段统计
- 异常值检测

### 6. 业务洞察生成

#### `generate_business_insight(question: str, data: List[Dict], context: str) -> str`
**功能**: 生成专业的业务洞察
**洞察结构**:
```python
# 1. 数据解读：关键数字和趋势
# 2. 业务洞察：数据背后的业务含义  
# 3. 行动建议：具体可执行的优化建议
```

**洞察特点**:
- 结合行业专业知识
- 数据驱动的分析结论
- 可执行的业务建议
- 风险提示和机会识别

### 7. 缓存管理系统

#### `_check_cache(cache_key: str) -> Optional[Any]`
**功能**: 检查查询缓存
**缓存策略**:
- 基于查询内容的智能缓存
- LRU缓存淘汰策略
- 缓存命中率统计

#### `_save_to_cache(cache_key: str, result: Any)`
**功能**: 保存结果到缓存
**缓存优化**:
- 大结果集压缩存储
- 过期时间管理
- 内存使用监控

### 8. 性能监控

#### `get_performance_stats() -> Dict`
**功能**: 获取性能统计信息
**统计指标**:
```python
{
    "total_queries": 156,
    "successful_queries": 142,
    "failed_queries": 14,
    "cache_hits": 45,
    "avg_response_time": 1.23,
    "database_connections": 3
}
```

#### `health_check() -> Dict`
**功能**: 系统健康检查
**检查项目**:
- 数据库连接状态
- AI服务可用性
- 表结构完整性
- 缓存系统状态

## 🎯 数据表架构支持

### 维度表 (Dimension Tables)
- **user_table**: 用户基础信息维度
- **region_table**: 地理位置维度  
- **time_table**: 时间维度

### 事实表 (Fact Table)
- **order_table**: 核心业务事实表

### 汇总表 (Summary Tables)
- **brand_revenue_summary**: 品牌收入汇总
- **user_behavior_summary**: 用户行为汇总
- **region_usage_summary**: 地区使用汇总
- **region_heatmap_data**: 热力图数据
- **time_summary**: 时间维度汇总

## 🚀 使用示例

```python
# 初始化查询引擎
engine = PowerBankIntelligentQueryEngine(
    db_config={
        'host': 'localhost',
        'user': 'root', 
        'password': 'password',
        'database': 'powerbank_db'
    },
    api_config={
        'api_key': 'your_api_key',
        'base_url': 'https://api.deepseek.com/v1'
    }
)

# 自然语言查询
question = "各品牌的市场份额分析"
sql = engine.natural_language_to_sql(question)
print(f"生成的SQL: {sql}")

# 执行查询
results = engine.execute_query(sql)
print(f"查询结果: {len(results)} 条记录")

# 生成业务洞察
insight = engine.generate_business_insight(question, results)
print(f"业务洞察: {insight}")

# 性能统计
stats = engine.get_performance_stats()
print(f"缓存命中率: {stats['cache_hits']}/{stats['total_queries']}")
```

## 📊 技术特点

| 特性类型 | 具体功能 | 技术优势 |
|---------|---------|---------|
| **智能映射** | 中英文字段自动转换 | 解决字段名不匹配问题 |
| **查询优化** | SQL自动优化和修复 | 提升查询性能和准确性 |
| **缓存系统** | 智能查询结果缓存 | 显著提升响应速度 |
| **业务洞察** | AI驱动的专业分析 | 深度业务价值挖掘 |
| **容错机制** | 多层次异常处理 | 保证系统稳定运行 |

## 🔮 系统优势

1. **企业级架构**: 支持高并发和大数据量处理
2. **智能化程度高**: 自然语言理解和智能字段映射
3. **业务专业性强**: 深度结合共享充电宝行业知识
4. **性能优化**: 缓存机制和查询优化
5. **扩展性好**: 模块化设计，易于功能扩展

---

*本文档基于PowerBankIntelligentQueryEngine v3.0版本*