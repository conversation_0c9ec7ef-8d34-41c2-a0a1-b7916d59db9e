{"cells": [{"cell_type": "markdown", "id": "3419e8f6", "metadata": {}, "source": ["# 1.前置代码"]}, {"cell_type": "code", "execution_count": 13, "id": "4e2d645f", "metadata": {}, "outputs": [], "source": ["from rich.console import Console\n", "console = Console()\n", "def custom_print(info):\n", "    console.print(info)"]}, {"cell_type": "code", "execution_count": 14, "id": "399f2dc6", "metadata": {}, "outputs": [], "source": ["import os\n", "import io\n", "import json\n", "import httpx\n", "import inspect\n", "import numpy as np\n", "import pandas as pd\n", "import pymysql\n", "import openai\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 21, "id": "9c0e2de9", "metadata": {}, "outputs": [], "source": ["api_key = \"sk-e2bad2bca73343a38f4f8d60f7435192\"  \n", "base_url = \"https://api.deepseek.com/v1\"\n", "http_client = httpx.Client(follow_redirects=True)\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")"]}, {"cell_type": "code", "execution_count": 22, "id": "9fdea827", "metadata": {}, "outputs": [], "source": ["def get_user_info(sql,username='root',password='',db='user_info'):\n", "    \"\"\"\n", "    当前函数，用于查询指定数据库下的相关表中的数据\n", "    ：param sql：参数为必要参数,字符串类型的SQL语句,\n", "    ：param username：参数为可选参数,代表:用户名,字符串类型,\n", "    ：param password：参数为可选参数,代表:用户密码,字符串类型,\n", "    ：param db：参数为可选参数,代表:需要连接的数据库名字,字符串类型,\n", "    ：return:当前SQL语句执行完的查询结果\n", "    \"\"\"\n", "    conn = pymysql.connect(\n", "        host = 'localhost',\n", "        user = username,\n", "        password = password,\n", "        db = db,\n", "        charset = 'utf8'\n", "    )\n", "    try: \n", "        with conn.cursor() as cursor:\n", "            cursor.execute(sql)\n", "            results = cursor.fetchall()\n", "    finally:\n", "        cursor.close()\n", "        conn.close()\n", "    column_names = [desc[0] for desc in cursor.description]\n", "    df = pd.DataFrame(results, columns = column_names)\n", "    return df.to_json(orient = 'records') "]}, {"cell_type": "code", "execution_count": 23, "id": "378cb691", "metadata": {}, "outputs": [], "source": ["#构建函数的指针(引用)\n", "tools_list = {\"get_user_info\": get_user_info}\n", "\n", "#函数的说明(工具的说明)\n", "tools = [{'type': 'function',\n", "         'function': {\n", "            'name': 'get_user_info',\n", "            'description': '当前函数，用于查询指定数据库下的相关表中的数据。输入参数为字符串类型的SQL语句，输出为当前SQL语句执行完的查询结果',\n", "            'parameters': {'type': 'object',\n", "                           'properties': {'sql': {'description': '必要参数，字符串类型的SQL语句','type': 'string'},\n", "                                          'username': {'description': '用户名,字符串类型','type': 'string'},\n", "                                          'password': {'description': '用户密码,字符串类型','type': 'string'},\n", "                                          'db': {'description': '需要连接的数据库名字,字符串类型','type': 'string'}\n", "                                         },\n", "         'required': ['sql']},\n", "        }}]"]}, {"cell_type": "markdown", "id": "731e128d", "metadata": {}, "source": ["功能：用户输入问题，大模型决定是否要自己回复还是调用工具回复"]}, {"cell_type": "code", "execution_count": 24, "id": "7c2bc6bb", "metadata": {}, "outputs": [], "source": ["def auto_call(client,messages,tools=None,model='deepseek-chat'):\n", "    if tools == None :\n", "        response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages\n", "        )\n", "        return response.choices[0].message.content\n", "    else :\n", "        #函数第一次调用\n", "        first_response = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages,\n", "            tools = tools,\n", "            tool_choice=\"auto\",\n", "        )\n", "        message_call = first_response.choices[0].message\n", "        tools_calls = message_call.tool_calls\n", "        if tools_calls != None:\n", "            print(\"进行二次调用\")\n", "        else:\n", "            print(\"没有进行二次调用\")\n", "        if tools_calls != None:\n", "            tool_call_id = tools_calls[0].id\n", "            func_name = first_response.choices[0].message.tool_calls[0].function.name\n", "            func_args = json.loads(tools_calls[0].function.arguments)['sql']\n", "            custom_print(func_args)\n", "            func_result = tools_list[func_name](func_args)\n", "            messages.append(message_call)\n", "            messages.append(\n", "                {\n", "                    \"role\":\"tool\",\n", "                    \"tool_call_id\":tool_call_id,\n", "                    \"name\":func_name,\n", "                    \"content\":func_result\n", "                }\n", "            )\n", "            #二次调用\n", "            second_response = client.chat.completions.create(\n", "                model=model,\n", "                messages=messages\n", "            )\n", "            return second_response.choices[0].message.content\n", "        else:\n", "            return first_response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 26, "id": "894ae67a", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">要查询 `user_base_info` 数据表中的记录总数，您可以使用以下SQL语句：\n", "\n", "```sql\n", "SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> AS total_records \n", "FROM user_base_info;\n", "```\n", "\n", "这条SQL会返回表中的总行数，列名为\"total_records\"。\n", "\n", "注意事项：\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. 如果表很大，<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span>可能需要一些时间执行\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. 在某些数据库系统中，<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">您也可以使用表的元数据获取近似行数</span><span style=\"font-weight: bold\">(</span>如MySQL的`SHOW TABLE STATUS`<span style=\"font-weight: bold\">)</span>\n", "\n", "如果您需要更具体的查询或有其他分析需求，请提供更多信息，我可以进一步协助您。\n", "</pre>\n"], "text/plain": ["要查询 `user_base_info` 数据表中的记录总数，您可以使用以下SQL语句：\n", "\n", "```sql\n", "SELECT \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m AS total_records \n", "FROM user_base_info;\n", "```\n", "\n", "这条SQL会返回表中的总行数，列名为\"total_records\"。\n", "\n", "注意事项：\n", "\u001b[1;36m1\u001b[0m. 如果表很大，\u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m可能需要一些时间执行\n", "\u001b[1;36m2\u001b[0m. 在某些数据库系统中，\u001b[1;35m您也可以使用表的元数据获取近似行数\u001b[0m\u001b[1m(\u001b[0m如MySQL的`SHOW TABLE STATUS`\u001b[1m)\u001b[0m\n", "\n", "如果您需要更具体的查询或有其他分析需求，请提供更多信息，我可以进一步协助您。\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["m1 = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "]\n", "custom_print(auto_call(client,m1))"]}, {"cell_type": "code", "execution_count": 29, "id": "5be0b6c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">今天下课吃什么可以根据你的口味、预算和时间来决定。以下是一些常见的建议：\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **快速方便**：\n", "   - 食堂或快餐店：汉堡、三明治、沙拉等。\n", "   - 便利店：饭团、便当、关东煮。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **健康选择**：\n", "   - 轻食：沙拉、鸡胸肉、蔬菜卷。\n", "   - 汤类：牛肉汤、蔬菜汤。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. **满足口腹之欲**：\n", "   - 火锅或麻辣烫。\n", "   - 披萨或炸鸡。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>. **经济实惠**：\n", "   - 面条或炒饭。\n", "   - 包子或煎饼。\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>. **特别推荐**：\n", "   - 尝试一家新开的餐厅。\n", "   - 和朋友一起点外卖分享。\n", "\n", "如果你有具体的偏好或限制（比如素食、预算等），可以告诉我，我可以帮你更精准地推荐！\n", "</pre>\n"], "text/plain": ["今天下课吃什么可以根据你的口味、预算和时间来决定。以下是一些常见的建议：\n", "\n", "\u001b[1;36m1\u001b[0m. **快速方便**：\n", "   - 食堂或快餐店：汉堡、三明治、沙拉等。\n", "   - 便利店：饭团、便当、关东煮。\n", "\n", "\u001b[1;36m2\u001b[0m. **健康选择**：\n", "   - 轻食：沙拉、鸡胸肉、蔬菜卷。\n", "   - 汤类：牛肉汤、蔬菜汤。\n", "\n", "\u001b[1;36m3\u001b[0m. **满足口腹之欲**：\n", "   - 火锅或麻辣烫。\n", "   - 披萨或炸鸡。\n", "\n", "\u001b[1;36m4\u001b[0m. **经济实惠**：\n", "   - 面条或炒饭。\n", "   - 包子或煎饼。\n", "\n", "\u001b[1;36m5\u001b[0m. **特别推荐**：\n", "   - 尝试一家新开的餐厅。\n", "   - 和朋友一起点外卖分享。\n", "\n", "如果你有具体的偏好或限制（比如素食、预算等），可以告诉我，我可以帮你更精准地推荐！\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["m2 = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问今天下课吃啥？\"}\n", "]\n", "custom_print(auto_call(client,m2,tools))"]}, {"cell_type": "code", "execution_count": 30, "id": "4f7cda61", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> AS total_records FROM user_base_info\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m AS total_records FROM user_base_info\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'user_base_info数据表中一共有891条数据记录。'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["m3 = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "]\n", "auto_call(client,m3,tools)"]}, {"cell_type": "markdown", "id": "afc26b97", "metadata": {}, "source": ["# 2.多轮对话核心流程"]}, {"cell_type": "markdown", "id": "fefdbee9", "metadata": {}, "source": ["## 2.1 能力边界测试"]}, {"cell_type": "markdown", "id": "93432c48", "metadata": {}, "source": ["测试大语言模型回复涉及私有化知识问题的能力边界"]}, {"cell_type": "code", "execution_count": 31, "id": "75d88db4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">FLOOR</span><span style=\"font-weight: bold\">(</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">AVG</span><span style=\"font-weight: bold\">(</span>age<span style=\"font-weight: bold\">))</span> AS average_age FROM user_base_info;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mFLOOR\u001b[0m\u001b[1m(\u001b[0m\u001b[1;35mAVG\u001b[0m\u001b[1m(\u001b[0mage\u001b[1m)\u001b[0m\u001b[1m)\u001b[0m AS average_age FROM user_base_info;\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'根据user_base_info数据表的分析结果，所有人员的平均年龄为29岁（已取整）。'"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": 32, "id": "7f8991c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'[{\"average_age\":29.7542}]'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_info(\"SELECT AVG(age) AS average_age FROM user_base_info ;\")"]}, {"cell_type": "markdown", "id": "a27da459", "metadata": {}, "source": ["报错的原因：大模型不知道本地的私有化知识"]}, {"cell_type": "code", "execution_count": 33, "id": "a7738c5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> AS total_siblings_spouses FROM user_base_info GROUP BY siblings_spouses ORDER BY \n", "total_siblings_spouses DESC LIMIT <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m AS total_siblings_spouses FROM user_base_info GROUP BY siblings_spouses ORDER BY \n", "total_siblings_spouses DESC LIMIT \u001b[1;36m1\u001b[0m;\n"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "OperationalError", "evalue": "(1054, \"Unknown column 'siblings_spouses' in 'group statement'\")", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mOperationalError\u001b[39m                          <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[33]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      1\u001b[39m messages = [    \n\u001b[32m      2\u001b[39m     {\u001b[33m\"\u001b[39m\u001b[33mrole\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33msystem\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mcontent\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33m你是大数据领域的数据专家,精通各种简单和复杂的数据分析\u001b[39m\u001b[33m\"\u001b[39m},\n\u001b[32m      3\u001b[39m     {\u001b[33m\"\u001b[39m\u001b[33mrole\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33muser\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mcontent\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33m请问user_base_info数据表兄弟姐妹以及配偶总人数，最多的为多少？\u001b[39m\u001b[33m\"\u001b[39m}\n\u001b[32m      4\u001b[39m ]\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m \u001b[43mauto_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43mclient\u001b[49m\u001b[43m,\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43mtools\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[24]\u001b[39m\u001b[32m, line 27\u001b[39m, in \u001b[36mauto_call\u001b[39m\u001b[34m(client, messages, tools, model)\u001b[39m\n\u001b[32m     25\u001b[39m func_args = json.loads(tools_calls[\u001b[32m0\u001b[39m].function.arguments)[\u001b[33m'\u001b[39m\u001b[33msql\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m     26\u001b[39m custom_print(func_args)\n\u001b[32m---> \u001b[39m\u001b[32m27\u001b[39m func_result = \u001b[43mtools_list\u001b[49m\u001b[43m[\u001b[49m\u001b[43mfunc_name\u001b[49m\u001b[43m]\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfunc_args\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     28\u001b[39m messages.append(message_call)\n\u001b[32m     29\u001b[39m messages.append(\n\u001b[32m     30\u001b[39m     {\n\u001b[32m     31\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mrole\u001b[39m\u001b[33m\"\u001b[39m:\u001b[33m\"\u001b[39m\u001b[33mtool\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     35\u001b[39m     }\n\u001b[32m     36\u001b[39m )\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[22]\u001b[39m\u001b[32m, line 19\u001b[39m, in \u001b[36mget_user_info\u001b[39m\u001b[34m(sql, username, password, db)\u001b[39m\n\u001b[32m     17\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m: \n\u001b[32m     18\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m conn.cursor() \u001b[38;5;28;01mas\u001b[39;00m cursor:\n\u001b[32m---> \u001b[39m\u001b[32m19\u001b[39m         \u001b[43mcursor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     20\u001b[39m         results = cursor.fetchall()\n\u001b[32m     21\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\cursors.py:153\u001b[39m, in \u001b[36mCursor.execute\u001b[39m\u001b[34m(self, query, args)\u001b[39m\n\u001b[32m    149\u001b[39m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[32m    151\u001b[39m query = \u001b[38;5;28mself\u001b[39m.mogrify(query, args)\n\u001b[32m--> \u001b[39m\u001b[32m153\u001b[39m result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_query\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    154\u001b[39m \u001b[38;5;28mself\u001b[39m._executed = query\n\u001b[32m    155\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\cursors.py:322\u001b[39m, in \u001b[36mCursor._query\u001b[39m\u001b[34m(self, q)\u001b[39m\n\u001b[32m    320\u001b[39m conn = \u001b[38;5;28mself\u001b[39m._get_db()\n\u001b[32m    321\u001b[39m \u001b[38;5;28mself\u001b[39m._clear_result()\n\u001b[32m--> \u001b[39m\u001b[32m322\u001b[39m \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m(\u001b[49m\u001b[43mq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    323\u001b[39m \u001b[38;5;28mself\u001b[39m._do_get_result()\n\u001b[32m    324\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.rowcount\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\connections.py:563\u001b[39m, in \u001b[36mConnection.query\u001b[39m\u001b[34m(self, sql, unbuffered)\u001b[39m\n\u001b[32m    561\u001b[39m     sql = sql.encode(\u001b[38;5;28mself\u001b[39m.encoding, \u001b[33m\"\u001b[39m\u001b[33msurrogateescape\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    562\u001b[39m \u001b[38;5;28mself\u001b[39m._execute_command(COMMAND.COM_QUERY, sql)\n\u001b[32m--> \u001b[39m\u001b[32m563\u001b[39m \u001b[38;5;28mself\u001b[39m._affected_rows = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_read_query_result\u001b[49m\u001b[43m(\u001b[49m\u001b[43munbuffered\u001b[49m\u001b[43m=\u001b[49m\u001b[43munbuffered\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    564\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._affected_rows\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\connections.py:825\u001b[39m, in \u001b[36mConnection._read_query_result\u001b[39m\u001b[34m(self, unbuffered)\u001b[39m\n\u001b[32m    823\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    824\u001b[39m     result = MySQLResult(\u001b[38;5;28mself\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m825\u001b[39m     \u001b[43mresult\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    826\u001b[39m \u001b[38;5;28mself\u001b[39m._result = result\n\u001b[32m    827\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m result.server_status \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\connections.py:1199\u001b[39m, in \u001b[36mMySQLResult.read\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1197\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mread\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m   1198\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1199\u001b[39m         first_packet = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_read_packet\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1201\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m first_packet.is_ok_packet():\n\u001b[32m   1202\u001b[39m             \u001b[38;5;28mself\u001b[39m._read_ok_packet(first_packet)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\connections.py:775\u001b[39m, in \u001b[36mConnection._read_packet\u001b[39m\u001b[34m(self, packet_type)\u001b[39m\n\u001b[32m    773\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._result \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._result.unbuffered_active \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m    774\u001b[39m         \u001b[38;5;28mself\u001b[39m._result.unbuffered_active = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m775\u001b[39m     \u001b[43mpacket\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraise_for_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    776\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m packet\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\protocol.py:219\u001b[39m, in \u001b[36mMysqlPacket.raise_for_error\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    217\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m DEBUG:\n\u001b[32m    218\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33merrno =\u001b[39m\u001b[33m\"\u001b[39m, errno)\n\u001b[32m--> \u001b[39m\u001b[32m219\u001b[39m \u001b[43merr\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraise_mysql_exception\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_data\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\anaconda\\envs\\zjou-2025\\Lib\\site-packages\\pymysql\\err.py:150\u001b[39m, in \u001b[36mraise_mysql_exception\u001b[39m\u001b[34m(data)\u001b[39m\n\u001b[32m    148\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m errorclass \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    149\u001b[39m     errorclass = InternalError \u001b[38;5;28;01mif\u001b[39;00m errno < \u001b[32m1000\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m OperationalError\n\u001b[32m--> \u001b[39m\u001b[32m150\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m errorclass(errno, errval)\n", "\u001b[31mOperationalError\u001b[39m: (1054, \"Unknown column 'siblings_spouses' in 'group statement'\")"]}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表兄弟姐妹以及配偶总人数，最多的为多少？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": 34, "id": "5867d151", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">为了回答您的问题，我需要查询相关的数据。请提供以下信息：\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. **数据库名称**：您需要查询的数据库名称。\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>. **表名**：包含出行数据的表名。\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. \n", "**字段名**：表中是否有字段记录“遇难人数”和“非遇难人数”，或者是否有其他字段可以推导出这些数据（例如“状态”字段可能包\n", "含“遇难”或“幸存”等）。\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>. **SQL查询权限**：如果需要通过SQL查询数据，请提供用户名和密码（可选，如果需要）。\n", "\n", "如果您能提供这些信息，我可以帮助您查询并统计遇难人数和非遇难人数。\n", "</pre>\n"], "text/plain": ["为了回答您的问题，我需要查询相关的数据。请提供以下信息：\n", "\n", "\u001b[1;36m1\u001b[0m. **数据库名称**：您需要查询的数据库名称。\n", "\u001b[1;36m2\u001b[0m. **表名**：包含出行数据的表名。\n", "\u001b[1;36m3\u001b[0m. \n", "**字段名**：表中是否有字段记录“遇难人数”和“非遇难人数”，或者是否有其他字段可以推导出这些数据（例如“状态”字段可能包\n", "含“遇难”或“幸存”等）。\n", "\u001b[1;36m4\u001b[0m. **SQL查询权限**：如果需要通过SQL查询数据，请提供用户名和密码（可选，如果需要）。\n", "\n", "如果您能提供这些信息，我可以帮助您查询并统计遇难人数和非遇难人数。\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问本次出行中，遇难人数和非遇难人数，分别是多少人？\"}\n", "]\n", "custom_print(auto_call(client,messages,tools))"]}, {"cell_type": "markdown", "id": "1bbadb19", "metadata": {}, "source": ["## 2.2 加载系统知识"]}, {"cell_type": "code", "execution_count": 35, "id": "9e9aaab4", "metadata": {}, "outputs": [{"data": {"text/plain": ["'# 表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch 字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare 字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked 字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量，0 表示乘客没有在泰坦尼克号事故中幸存，1 表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” 等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理性别，只有 “male”（男性）和 “female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” 的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n```\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp 类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(r'C:\\Users\\<USER>\\Desktop\\暑期实训2周\\数据预处理数据集\\data.md', 'r', encoding='utf-8') as f:\n", "    system_info = f.read()\n", "system_info"]}, {"cell_type": "code", "execution_count": 36, "id": "53b94578", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">MAX</span><span style=\"font-weight: bold\">(</span>sib_sp<span style=\"font-weight: bold\">)</span> AS max_siblings_spouses FROM user_base_info;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mMAX\u001b[0m\u001b[1m(\u001b[0msib_sp\u001b[1m)\u001b[0m AS max_siblings_spouses FROM user_base_info;\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'根据查询结果，user_base_info数据表中记录的兄弟姐妹以及配偶总人数(sib_sp字段)的最大值是8。这意味着在该数据集中，有乘客携带了8位兄弟姐妹或配偶一同登船。'"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"system\", \"content\": \"数据库中表和字段的附加说明:%s\" % system_info},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表兄弟姐妹以及配偶总人数，最多的为多少？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": 48, "id": "b4be0b5e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'[{\"max_sib_sp\":\"8\"}]'"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_info(\"SELECT MAX(sib_sp) AS max_sib_sp FROM user_base_info ;\")"]}, {"cell_type": "code", "execution_count": 47, "id": "a9e208d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT survived, <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">COUNT</span><span style=\"font-weight: bold\">(</span>*<span style=\"font-weight: bold\">)</span> as count FROM is_survived GROUP BY survived;\n", "</pre>\n"], "text/plain": ["SELECT survived, \u001b[1;35mCOUNT\u001b[0m\u001b[1m(\u001b[0m*\u001b[1m)\u001b[0m as count FROM is_survived GROUP BY survived;\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">根据泰坦尼克号乘客生存数据统计结果：\n", "\n", "- 遇难人数：549人（<span style=\"color: #808000; text-decoration-color: #808000\">survived</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>）\n", "- 生还人数：342人（<span style=\"color: #808000; text-decoration-color: #808000\">survived</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>）\n", "\n", "总计乘客数量为891人（<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">549</span>+<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">342</span>）。这个结果显示了泰坦尼克号事故中大约61.<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span>%的乘客遇难，<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38.4</span>%的乘客生还。\n", "</pre>\n"], "text/plain": ["根据泰坦尼克号乘客生存数据统计结果：\n", "\n", "- 遇难人数：549人（\u001b[33msurvived\u001b[0m=\u001b[1;36m0\u001b[0m）\n", "- 生还人数：342人（\u001b[33msurvived\u001b[0m=\u001b[1;36m1\u001b[0m）\n", "\n", "总计乘客数量为891人（\u001b[1;36m549\u001b[0m+\u001b[1;36m342\u001b[0m）。这个结果显示了泰坦尼克号事故中大约61.\u001b[1;36m6\u001b[0m%的乘客遇难，\u001b[1;36m38.4\u001b[0m%的乘客生还。\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"system\", \"content\": \"数据库中表和字段的附加说明:%s\" % system_info},\n", "    {\"role\": \"user\", \"content\": \"请问本次出行中，遇难人数和非遇难人数，分别是多少人？\"}\n", "]\n", "custom_print(auto_call(client,messages,tools))"]}, {"cell_type": "code", "execution_count": 46, "id": "3945dd95", "metadata": {}, "outputs": [{"data": {"text/plain": ["'[{\"survived\":\"0\",\"count\":549},{\"survived\":\"1\",\"count\":342}]'"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_info(\"SELECT survived, COUNT(*) AS count FROM is_survived GROUP BY survived ;\")"]}, {"cell_type": "markdown", "id": "7591d54a", "metadata": {}, "source": ["## 2.3 多轮对话"]}, {"cell_type": "code", "execution_count": 37, "id": "8c3c9885", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '你是大数据领域的数据专家,精通各种简单和复杂的数据分析'},\n", " {'role': 'system',\n", "  'content': '数据库中表和字段的附加说明:# 表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch 字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare 字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked 字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量，0 表示乘客没有在泰坦尼克号事故中幸存，1 表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” 等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理性别，只有 “male”（男性）和 “female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” 的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n```\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp 类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'},\n", " {'role': 'user', 'content': '请问user_base_info数据表兄弟姐妹以及配偶总人数，最多的为多少？'},\n", " ChatCompletionMessage(content='', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_0_34f49042-e9f5-4048-8024-6521aa57c5e1', function=Function(arguments='{\"sql\":\"SELECT MAX(sib_sp) AS max_siblings_spouses FROM user_base_info;\"}', name='get_user_info'), type='function', index=0)]),\n", " {'role': 'tool',\n", "  'tool_call_id': 'call_0_34f49042-e9f5-4048-8024-6521aa57c5e1',\n", "  'name': 'get_user_info',\n", "  'content': '[{\"max_siblings_spouses\":\"8\"}]'}]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["messages"]}, {"cell_type": "markdown", "id": "1ef9287f", "metadata": {}, "source": ["system_base_info:角色设定\n", "system_extend_info:本地私有化知识"]}, {"cell_type": "code", "execution_count": null, "id": "05fe3240", "metadata": {}, "outputs": [], "source": ["def auto_chat(client, \n", "              user_input,\n", "              system_base_info=\"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\",\n", "              system_extend_info=\"\",\n", "              tools = None,\n", "              model=\"deepseek-chat\"):\n", "    system_message = {\"role\":\"system\",\"content\":system_base_info + \"%s\" %system_extend_info}\n", "    user_message={\"role\":\"user\",\"content\":user_input}\n", "    messages = []\n", "    messages.append(system_message)\n", "    messages.append(user_message)\n", "    while True:           \n", "        result = auto_call(client,messages,tools,model=model)\n", "        print(f\"模型回复: {result}\")\n", "        user_input = input(\"您还有想要提问的问题？(输入0结束对话): \")\n", "        messages.clear()\n", "        messages.append(system_message)\n", "        messages.append({\"role\":\"user\",\"content\":user_input})\n", "        custom_print(messages)\n", "        print(\"================================================================================\")\n", "        if user_input == \"0\":\n", "            break"]}, {"cell_type": "markdown", "id": "de7e4aaa", "metadata": {}, "source": ["错误的颠倒系统和用户信息的输入顺序"]}, {"cell_type": "code", "execution_count": 50, "id": "f7b1981d", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'initialize_client' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[50]\u001b[39m\u001b[32m, line 38\u001b[39m\n\u001b[32m     35\u001b[39m \u001b[38;5;66;03m# 测试运行\u001b[39;00m\n\u001b[32m     36\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[34m__name__\u001b[39m == \u001b[33m\"\u001b[39m\u001b[33m__main__\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m     37\u001b[39m     \u001b[38;5;66;03m# 假设 client 已经初始化\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m38\u001b[39m     client = \u001b[43minitialize_client\u001b[49m()  \u001b[38;5;66;03m# 替换成你的实际初始化代码\u001b[39;00m\n\u001b[32m     39\u001b[39m     auto_chat(client, \u001b[33m\"\u001b[39m\u001b[33muser_base_info表中有多少条数据？\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'initialize_client' is not defined"]}], "source": ["def auto_chat(client, \n", "              user_input,\n", "              system_base_info=\"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\",\n", "              system_extend_info=\"\",\n", "              tools=None,\n", "              model=\"deepseek-chat\"):\n", "    \n", "    # 先构建用户消息，再构建系统消息（顺序颠倒）\n", "    user_message = {\"role\": \"user\", \"content\": user_input}\n", "    system_message = {\"role\": \"system\", \"content\": system_base_info + system_extend_info}\n", "    \n", "    messages = []\n", "    messages.append(user_message)  # 先添加用户消息\n", "    messages.append(system_message)  # 再添加系统消息（顺序错误）\n", "    \n", "    while True:\n", "        try:\n", "            result = auto_call(client, messages, tools, model=model)\n", "            print(f\"模型回复: {result}\")\n", "            \n", "            user_input = input(\"您还有想要提问的问题？(输入0结束对话): \")\n", "            if user_input == \"0\":\n", "                break\n", "                \n", "            # 继续对话时，仍然保持错误的顺序（先user后system）\n", "            messages = []\n", "            messages.append({\"role\": \"user\", \"content\": user_input})\n", "            messages.append(system_message)\n", "            \n", "            print(\"================================================================================\")\n", "        except Exception as e:\n", "            print(f\"发生错误: {e}\")\n", "            break\n", "\n", "# 测试运行\n", "if __name__ == \"__main__\":\n", "    # 假设 client 已经初始化\n", "    client = initialize_client()  # 替换成你的实际初始化代码\n", "    auto_chat(client, \"user_base_info表中有多少条数据？\")"]}, {"cell_type": "code", "execution_count": 42, "id": "1900bcb7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ROUND</span><span style=\"font-weight: bold\">(</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">AVG</span><span style=\"font-weight: bold\">(</span>age<span style=\"font-weight: bold\">))</span> AS average_age FROM user_base_info;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mROUND\u001b[0m\u001b[1m(\u001b[0m\u001b[1;35mAVG\u001b[0m\u001b[1m(\u001b[0mage\u001b[1m)\u001b[0m\u001b[1m)\u001b[0m AS average_age FROM user_base_info;\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["模型回复: user_base_info数据表中所有人员的平均年龄是30岁（保留整数）。\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，0 表示乘客没有在泰坦尼克号事故中幸存，1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">性别，只有 “male”（男性）和 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'晚上去重庆渝北区吃饭 给我一点建议 谢谢'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# \u001b[0m\n", "\u001b[32m表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，\u001b[0m\n", "\u001b[32m需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mpassenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mage 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch \u001b[0m\n", "\u001b[32m字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked \u001b[0m\n", "\u001b[32m字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 \u001b[0m\n", "\u001b[32msurvived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 \u001b[0m\n", "\u001b[32mID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量\u001b[0m\n", "\u001b[32m，0 表示乘客没有在泰坦尼克号事故中幸存，1 \u001b[0m\n", "\u001b[32m表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 \u001b[0m\n", "\u001b[32m代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 \u001b[0m\n", "\u001b[32m代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存\u001b[0m\n", "\u001b[32m概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测\u001b[0m\n", "\u001b[32m生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” \u001b[0m\n", "\u001b[32m等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理\u001b[0m\n", "\u001b[32m性别，只有 “male”（男性）和 \u001b[0m\n", "\u001b[32m“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” \u001b[0m\n", "\u001b[32m的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，\u001b[0m\n", "\u001b[32m不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在\u001b[0m\n", "\u001b[32m灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直\u001b[0m\n", "\u001b[32m系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如\u001b[0m\n", "\u001b[32m，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`\u001b[0m\n", "\u001b[32m``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp \u001b[0m\n", "\u001b[32m类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩\u001b[0m\n", "\u001b[32m子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预\u001b[0m\n", "\u001b[32m测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare\u001b[0m\n", "\u001b[32m（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经\u001b[0m\n", "\u001b[32m济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置\u001b[0m\n", "\u001b[32m，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口\u001b[0m\n", "\u001b[32m，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并\u001b[0m\n", "\u001b[32m且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'晚上去重庆渝北区吃饭 给我一点建议 谢谢'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "没有进行二次调用\n", "模型回复: 重庆渝北区是一个美食聚集地，有很多值得尝试的餐厅和特色美食。以下是一些建议供你参考：\n", "\n", "### 1. **火锅**\n", "   - **推荐餐厅**：渝宗老灶火锅、大龙燚火锅、珮姐老火锅\n", "   - **特色**：重庆火锅以麻辣鲜香著称，推荐尝试毛肚、黄喉、鸭血等经典菜品。\n", "\n", "### 2. **江湖菜**\n", "   - **推荐餐厅**：杨记隆府、徐鼎盛民间菜\n", "   - **特色**：江湖菜是重庆的特色菜系，推荐尝试辣子鸡、毛血旺、水煮鱼等。\n", "\n", "### 3. **小面**\n", "   - **推荐餐厅**：秦云老太婆摊摊面、花市豌杂面\n", "   - **特色**：重庆小面是当地的特色早餐，但晚上也能吃到，推荐豌杂面或牛肉面。\n", "\n", "### 4. **烧烤**\n", "   - **推荐餐厅**：九村烤脑花、何师烧烤\n", "   - **特色**：重庆的烧烤以麻辣为主，推荐尝试烤脑花、烤鱼、烤茄子等。\n", "\n", "### 5. **特色小吃**\n", "   - **推荐餐厅**：八一好吃街（渝北也有分店）、陈麻花\n", "   - **特色**：可以尝试酸辣粉、山城小汤圆、陈麻花等小吃。\n", "\n", "### 6. **环境优雅的餐厅**\n", "   - **推荐餐厅**：渝北区的“山城饭局”、“隐庐”等\n", "   - **特色**：适合喜欢安静环境的食客，菜品精致且融合了重庆本地风味。\n", "\n", "### 7. **夜市**\n", "   - **推荐地点**：渝北区的夜市如龙溪夜市、金港国际夜市\n", "   - **特色**：夜市里有各种小吃和排档，适合喜欢热闹氛围的食客。\n", "\n", "### 温馨提示：\n", "- 如果你对辣度敏感，可以提前告知餐厅调整辣度。\n", "- 渝北区交通便利，建议提前规划好路线，避免高峰期堵车。\n", "\n", "希望你能在渝北区享受一顿美味的晚餐！如果有其他需求，可以随时告诉我。\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">age 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">survived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">ID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，0 表示乘客没有在泰坦尼克号事故中幸存，1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">性别，只有 “male”（男性）和 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'0'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析# \u001b[0m\n", "\u001b[32m表数据说明\\n\\n当前user_base_info、user_extend_info、is_survived表是存在关联的，三张表passenger_id字段代表相同函数，\u001b[0m\n", "\u001b[32m需要多表关联时，可以作为主键进行关联。\\n\\n**user_base_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mpassenger_id 字段；\\n\\n\\u200b\\t字符串类型的 name 字段；\\n\\n\\u200b\\t字符串类型的 sex 字段；\\n\\n\\u200b\\t整数类型的 \u001b[0m\n", "\u001b[32mage 字段；\\n\\n\\u200b\\t字符串类型的 sib_sp 字段；\\n\\n\\u200b\\t字符串类型的 parch \u001b[0m\n", "\u001b[32m字段；\\n\\n**user_extend_info表存在6个字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 p_class 字段；\\n\\n\\u200b\\t字符串类型的 ticket 字段；\\n\\n\\u200b\\t字符串类型的 fare \u001b[0m\n", "\u001b[32m字段；\\n\\n\\u200b\\t字符串类型的 cabin 字段；\\n\\n\\u200b\\t字符串类型的 embarked \u001b[0m\n", "\u001b[32m字段；\\n\\n**is_survived表存在2字段，分别为 ：**\\n\\n\\u200b\\t整数类型的 passenger_id 字段；\\n\\n\\u200b\\t字符串类型的 \u001b[0m\n", "\u001b[32msurvived 字段；\\n\\n**每个字段的含义如下：**\\n\\n**PassengerId（乘客 \u001b[0m\n", "\u001b[32mID）**\\n\\n```\\n唯一标识编号。它主要用区分不同的乘客个体。\\n```\\n\\n**Survived（是否幸存）**\\n\\n```\\n它是一个二元变量\u001b[0m\n", "\u001b[32m，0 表示乘客没有在泰坦尼克号事故中幸存，1 \u001b[0m\n", "\u001b[32m表示乘客幸存下来。\\n```\\n\\n**Pclass（客舱等级）**\\n\\n```\\n这代表乘客所乘坐的客舱等级，有三个可能的值：1、2、3。\\n1 \u001b[0m\n", "\u001b[32m代表一等舱，通常是社会地位较高、比较富裕的乘客乘坐的舱位；\\n2 代表二等舱；\\n3 \u001b[0m\n", "\u001b[32m代表三等舱，三等舱的乘客数量相对较多，包括许多普通民众和移民。这个变量可以反映乘客的社会经济地位和所在位置对其生存\u001b[0m\n", "\u001b[32m概率的潜在影响。\\n```\\n\\n**Name（姓名）**\\n\\n```\\n乘客的名字，包含姓氏和名字。虽然名字本身可能不会直接用于模型预测\u001b[0m\n", "\u001b[32m生存情况，但名字中的某些部分（如头衔）可以提取出来作为新的特征。例如，从名字中可以提取出 “Mr.”、“Mrs.”、“Miss” \u001b[0m\n", "\u001b[32m等头衔，不同头衔可能与乘客的性别、年龄和社会地位有关，进而影响生存概率。\\n```\\n\\n**Sex（性别）**\\n\\n```\\n乘客的生理\u001b[0m\n", "\u001b[32m性别，只有 “male”（男性）和 \u001b[0m\n", "\u001b[32m“female”（女性）两个类别。在泰坦尼克号事故中，性别是一个对生存概率有显著影响的因素，因为当时有 “妇女和儿童优先” \u001b[0m\n", "\u001b[32m的救援原则，所以女性乘客的生存概率相对较高。\\n```\\n\\n**Age（年龄）**\\n\\n```\\n乘客的年龄。年龄是一个重要的连续变量，\u001b[0m\n", "\u001b[32m不同年龄阶段的乘客在面对灾难时的生存能力和机会可能不同。例如，儿童可能会优先被救援，而老年人可能由于身体原因较难在\u001b[0m\n", "\u001b[32m灾难中逃生。同时，年龄也可以与其他变量（如性别、客舱等级）相互作用，对生存概率产生复杂的影响。\\n```\\n\\n**SibSp（直\u001b[0m\n", "\u001b[32m系亲属数量）**\\n\\n```\\n这个变量表示乘客在船上的直系亲属的数量。它可以在一定程度上反映乘客在船上的家庭支持情况。例如\u001b[0m\n", "\u001b[32m，有较多直系亲属多的人员同行的乘客可能在逃生过程中相互帮助，增加生存概率；但也有可能为了寻找家人而错过逃生机会。\\n`\u001b[0m\n", "\u001b[32m``\\n\\n**Parch（父母 / 子女数量）**\\n\\n```\\n表示乘客在船上的父母或子女的数量。和 SibSp \u001b[0m\n", "\u001b[32m类似，它体现了乘客的家庭联系情况，对生存概率可能产生正面或负面的影响。例如，一位带着多个孩子的父母可能会优先确保孩\u001b[0m\n", "\u001b[32m子的安全而放弃自己的逃生机会。\\n```\\n\\n**Ticket（船票号码）**\\n\\n```\\n乘客的船票号码。船票号码本身可能没有直接的预\u001b[0m\n", "\u001b[32m测价值，但它可能与乘客的其他信息（如客舱位置、票价等）存在关联，这些关联信息可能会对生存概率产生影响\\n```\\n\\n**Fare\u001b[0m\n", "\u001b[32m（票价）**\\n\\n```\\n乘客购买船票的价格。票价通常与客舱等级有关，一等舱的票价较高，三等舱的票价较低。它可以作为乘客经\u001b[0m\n", "\u001b[32m济实力的一个间接指标。\\n```\\n\\n**Cabin（客舱号码）**\\n\\n```\\n乘客所居住的客舱号码。客舱号码可以反映乘客在船上的位置\u001b[0m\n", "\u001b[32m，不同位置的客舱距离救生艇的远近不同，逃生的便利性也不同。\\n```\\n\\n**Embarked（登船港口）**\\n\\n```\\n乘客登船的港口\u001b[0m\n", "\u001b[32m，有三个可能的值：“C”（瑟堡）、“Q”（皇后镇）、“S”（南安普敦）。登船港口可能与乘客的旅行路线、社会背景等因素有关，并\u001b[0m\n", "\u001b[32m且不同港口登船的乘客在船上的分布位置等情况也可能有所不同，从而对生存概率产生潜在影响。\\n```\\n\\n'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'0'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["================================================================================\n"]}], "source": ["auto_chat(client,user_input=\"请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？\",system_extend_info=system_info,tools=tools)"]}, {"cell_type": "markdown", "id": "b6bef960", "metadata": {}, "source": ["# 3.短期记忆(基于数量)"]}, {"cell_type": "markdown", "id": "3d0116d3", "metadata": {}, "source": ["```\n", "总览：创建名字为ShortMemoryBaseCount的类，当前类主要用于用户与大模型对话内容管理，通过对话内容管理，进而让大模型具有记忆\n", "```"]}, {"cell_type": "markdown", "id": "6cd6ee8e", "metadata": {}, "source": ["self.count_threshold阈值，大模型最多能"]}, {"cell_type": "code", "execution_count": 51, "id": "ae219447", "metadata": {}, "outputs": [], "source": ["class ShortMemoryBaseCount:\n", "    def __init__(self, \n", "                 messages=None,\n", "                 count_threshold=0,\n", "                 counts=0,\n", "                 model=\"deepseek-chat\"):\n", "        print(\"创建 ShortMemoryBaseCount 类对应实例对象\")\n", "        if messages is None:\n", "            self.messages = []\n", "        else:\n", "            self.messages = messages\n", "        self.count_threshold = count_threshold\n", "        self.counts = counts\n", "        self.model = model\n", "\n", "    def _based_on_count(self):\n", "        if self.counts > self.count_threshold:\n", "            diff = self.counts - self.count_threshold\n", "            self.counts -= diff\n", "\n", "            del self.messages[1:(1 + diff)]\n", "            if(\"tool_call_id\" in self.messages[1]):\n", "                del self.messages[1]\n", "                # self.counts-=1\n", "    #追加消息的方法\n", "    def append_message(self, message:dict):\n", "        self.counts += 1\n", "        self.messages.append(message)\n", "        self._based_on_count()\n", "    #获取当前短期记忆获取的消息\n", "    def get_messages(self):\n", "        return self.messages"]}, {"cell_type": "code", "execution_count": 52, "id": "4966aef8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["创建 ShortMemoryBaseCount 类对应实例对象\n", "[{'user': '我问你第一个问题，你是谁？'}]\n", "----------------------------------------------\n", "[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第二个问题，重新回答你是谁？'}]\n", "----------------------------------------------\n", "[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第二个问题，重新回答你是谁？'}, {'user': '我问你第三个问题，你是谁？'}]\n"]}], "source": ["short_memory = ShortMemoryBaseCount(count_threshold=3)\n", "short_memory.append_message(message={\"user\":\"我问你第一个问题，你是谁？\"})\n", "print(short_memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "short_memory.append_message(message={\"user\":\"我问你第二个问题，重新回答你是谁？\"})\n", "print(short_memory.get_messages())\n", "print(\"----------------------------------------------\")\n", "short_memory.append_message(message={\"user\":\"我问你第三个问题，你是谁？\"})\n", "print(short_memory.get_messages())"]}, {"cell_type": "code", "execution_count": 53, "id": "32631c24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第三个问题，你是谁？'}, {'user': '我问你第四个问题，重新回答你是谁？'}]\n"]}], "source": ["short_memory.append_message(message={\"user\":\"我问你第四个问题，重新回答你是谁？\"})\n", "print(short_memory.get_messages())"]}, {"cell_type": "code", "execution_count": 54, "id": "586fbe50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'user': '我问你第一个问题，你是谁？'}, {'user': '我问你第四个问题，重新回答你是谁？'}, {'user': '我问你第五个问题，重新回答你是谁？'}]\n"]}], "source": ["short_memory.append_message(message={\"user\":\"我问你第五个问题，重新回答你是谁？\"})\n", "print(short_memory.get_messages())"]}, {"cell_type": "code", "execution_count": 55, "id": "c3c7f667", "metadata": {}, "outputs": [], "source": ["def auto_chat(client,\n", "              user_input,\n", "              system_base_info=\"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\",\n", "              system_extend_info=\"\",\n", "              tools = None,\n", "              model=\"deepseek-chat\"):\n", "    system_message = {\"role\":\"system\",\"content\":system_base_info + \"%s\" %system_extend_info}\n", "    user_message={\"role\":\"user\",\"content\":user_input}\n", "    # global memory\n", "    memory = ShortMemoryBaseCount(count_threshold=6)\n", "    memory.append_message(system_message)\n", "    memory.append_message(user_message)\n", "    while True:                   \n", "        result = auto_call(client,memory.get_messages(),tools=tools,model=model)\n", "        custom_print(f\"模型回复: {result}\")\n", "        memory.append_message({\"role\": \"assistant\", \"content\": result})\n", "        custom_print(\"追加完系统回复后的消息结构: \")\n", "        custom_print(memory.get_messages())\n", "        user_input = input(\"您还有其他问题？(输入0结束对话): \")\n", "        memory.append_message({\"role\":\"user\",\"content\":user_input})\n", "        custom_print(\"重新追加用户问题后的消息结构: \")\n", "        custom_print(memory.get_messages())\n", "        if user_input == \"0\":\n", "            break"]}, {"cell_type": "code", "execution_count": 56, "id": "7437044d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["创建 ShortMemoryBaseCount 类对应实例对象\n", "没有进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">模型回复: 你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，都可以告诉我哦！\n", "</pre>\n"], "text/plain": ["模型回复: 你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，都可以告诉我哦！\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">追加完系统回复后的消息结构: \n", "</pre>\n"], "text/plain": ["追加完系统回复后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，都可以告诉我哦！'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，都可以告诉我哦！'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">重新追加用户问题后的消息结构: \n", "</pre>\n"], "text/plain": ["重新追加用户问题后的消息结构: \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好呀'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，都可以告诉我哦！'</span>\n", "    <span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'0'</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你好呀'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'assistant'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'你好！有什么可以帮您的吗？无论是数据分析、查询还是其他问题，都可以告诉我哦！'\u001b[0m\n", "    \u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'0'\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["user_input = \"你好呀\"\n", "auto_chat(client,user_input=user_input,system_extend_info=\"\",tools=tools)"]}, {"cell_type": "markdown", "id": "ff8108e5", "metadata": {}, "source": ["# 4.测试"]}, {"cell_type": "markdown", "id": "a7e8da33", "metadata": {}, "source": ["## 4.1 类示例演示"]}, {"cell_type": "code", "execution_count": 43, "id": "6e984224", "metadata": {}, "outputs": [], "source": ["class User:\n", "    def __init__(self,name):\n", "        print(\"创建对象\")\n", "        self.name = name\n", "    def get<PERSON><PERSON>(self):\n", "        self.age = 100\n", "        return self.name"]}, {"cell_type": "code", "execution_count": 44, "id": "0bddfd82", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["创建对象\n"]}], "source": ["user = User(\"zhaoyi\")"]}, {"cell_type": "code", "execution_count": 57, "id": "811985c1", "metadata": {}, "outputs": [{"data": {"text/plain": ["'zhaoyi'"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["user.name"]}, {"cell_type": "code", "execution_count": 58, "id": "5037236c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'zhaoyi'"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["user.getName()"]}, {"cell_type": "code", "execution_count": 59, "id": "6320206a", "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["user.age"]}, {"cell_type": "markdown", "id": "93ec3485", "metadata": {}, "source": ["## 4.2 代码拆解"]}, {"cell_type": "markdown", "id": "7bb51b36", "metadata": {}, "source": ["```\n", "        if self.counts > self.count_threshold:\n", "            diff = self.counts - self.count_threshold\n", "            self.counts -= diff\n", "            del self.messages[1:(1 + diff)]\n", "```"]}, {"cell_type": "code", "execution_count": 91, "id": "9ef26b3e", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["counts = 4\n", "count_threshold = 2\n", "diff  = counts - count_threshold\n", "diff"]}, {"cell_type": "code", "execution_count": 95, "id": "b7b090da", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '系统消息1'},\n", " {'role': 'user', 'content': '系统消息2'},\n", " {'role': 'user', 'content': '系统消息3'},\n", " {'role': 'user', 'content': '系统消息4'}]"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["mess = [{\"role\":\"system\",\"content\":\"系统消息1\"},{\"role\":\"user\",\"content\":\"系统消息2\"},{\"role\":\"user\",\"content\":\"系统消息3\"},{\"role\":\"user\",\"content\":\"系统消息4\"}]\n", "mess"]}, {"cell_type": "code", "execution_count": 96, "id": "2cc4539d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'user', 'content': '系统消息2'}, {'role': 'user', 'content': '系统消息3'}]"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["mess[1:(1+diff)]"]}, {"cell_type": "markdown", "id": "9bf5234e", "metadata": {}, "source": ["## 4.3 消息机制验证"]}, {"cell_type": "markdown", "id": "b90c2973", "metadata": {}, "source": ["验证消息删除的机制，以及大模型回复问题的机制"]}, {"cell_type": "code", "execution_count": 56, "id": "48e310ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/plain": ["'你好！作为大数据领域的专家，我可以帮助你解决各种与数据相关的问题，包括但不限于以下方面：\\n\\n1. **数据分析**：从简单的统计描述到复杂的机器学习模型。\\n2. **数据清洗与预处理**：处理缺失值、异常值、数据标准化等。\\n3. **数据可视化**：生成直观的图表和报告。\\n4. **数据库查询与优化**：编写高效的SQL查询或优化现有查询。\\n5. **大数据技术**：如Hadoop、Spark、Hive等工具的使用。\\n6. **数据建模与预测**：构建预测模型或分类模型。\\n7. **数据集成与ETL**：设计数据管道，实现数据集成。\\n\\n如果你有具体的问题或需求，可以直接告诉我，我会尽力为你提供解决方案！'"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"assistant\", \"content\": \"我是能够处理海量数据的专家，有什么可以帮助你的？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": 57, "id": "b33f4bb3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["进行二次调用\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">SELECT <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">FLOOR</span><span style=\"font-weight: bold\">(</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">AVG</span><span style=\"font-weight: bold\">(</span>age<span style=\"font-weight: bold\">))</span> AS average_age FROM user_base_info;\n", "</pre>\n"], "text/plain": ["SELECT \u001b[1;35mFLOOR\u001b[0m\u001b[1m(\u001b[0m\u001b[1;35mAVG\u001b[0m\u001b[1m(\u001b[0mage\u001b[1m)\u001b[0m\u001b[1m)\u001b[0m AS average_age FROM user_base_info;\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'根据计算，user_base_info数据表中所有人员的平均年龄是29岁（已取整）。'"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？\"}\n", "]\n", "auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": 58, "id": "a41e88a5", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '你是大数据领域的数据专家,精通各种简单和复杂的数据分析'},\n", " {'role': 'user', 'content': '请问user_base_info数据表所有人员平均年龄是多少，结果只保留整数？'},\n", " ChatCompletionMessage(content='', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_0_36add952-e817-42a2-b8e2-f553a04e9097', function=Function(arguments='{\"sql\":\"SELECT FLOOR(AVG(age)) AS average_age FROM user_base_info;\",\"username\":\"root\",\"password\":\"123456\",\"db\":\"user_db\"}', name='get_user_info'), type='function', index=0)]),\n", " {'role': 'tool',\n", "  'tool_call_id': 'call_0_36add952-e817-42a2-b8e2-f553a04e9097',\n", "  'name': 'get_user_info',\n", "  'content': '[{\"average_age\":29}]'}]"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["messages"]}, {"cell_type": "code", "execution_count": 59, "id": "6d63a293", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '你是大数据领域的数据专家,精通各种简单和复杂的数据分析'},\n", " ChatCompletionMessage(content='', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_0_36add952-e817-42a2-b8e2-f553a04e9097', function=Function(arguments='{\"sql\":\"SELECT FLOOR(AVG(age)) AS average_age FROM user_base_info;\",\"username\":\"root\",\"password\":\"123456\",\"db\":\"user_db\"}', name='get_user_info'), type='function', index=0)]),\n", " {'role': 'tool',\n", "  'tool_call_id': 'call_0_36add952-e817-42a2-b8e2-f553a04e9097',\n", "  'name': 'get_user_info',\n", "  'content': '[{\"average_age\":29}]'}]"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["del messages[1:2]\n", "messages"]}, {"cell_type": "code", "execution_count": 60, "id": "350b8227", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["没有进行二次调用\n"]}, {"data": {"text/plain": ["'根据查询结果，用户的平均年龄约为29岁。'"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["auto_call(client,messages,tools)"]}, {"cell_type": "code", "execution_count": 61, "id": "744f55eb", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '你是大数据领域的数据专家,精通各种简单和复杂的数据分析'},\n", " {'role': 'tool',\n", "  'tool_call_id': 'call_0_36add952-e817-42a2-b8e2-f553a04e9097',\n", "  'name': 'get_user_info',\n", "  'content': '[{\"average_age\":29}]'}]"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["del messages[1:2]\n", "messages"]}, {"cell_type": "code", "execution_count": 62, "id": "3bd4ee90", "metadata": {}, "outputs": [{"ename": "BadRequestError", "evalue": "Error code: 400 - {'error': {'message': \"Messages with role 'tool' must be a response to a preceding message with 'tool_calls'\", 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mBadRequestError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[62], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m auto_call(client,messages,tools)\n", "Cell \u001b[1;32mIn[28], line 10\u001b[0m, in \u001b[0;36mauto_call\u001b[1;34m(client, messages, tools, model)\u001b[0m\n\u001b[0;32m      7\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\u001b[38;5;241m.\u001b[39mchoices[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mmessage\u001b[38;5;241m.\u001b[39mcontent\n\u001b[0;32m      8\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m :\n\u001b[0;32m      9\u001b[0m     \u001b[38;5;66;03m#函数第一次调用\u001b[39;00m\n\u001b[1;32m---> 10\u001b[0m     first_response \u001b[38;5;241m=\u001b[39m client\u001b[38;5;241m.\u001b[39mchat\u001b[38;5;241m.\u001b[39mcompletions\u001b[38;5;241m.\u001b[39mcreate(\n\u001b[0;32m     11\u001b[0m         model\u001b[38;5;241m=\u001b[39mmodel,\n\u001b[0;32m     12\u001b[0m         messages\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[0;32m     13\u001b[0m         tools \u001b[38;5;241m=\u001b[39m tools,\n\u001b[0;32m     14\u001b[0m         tool_choice\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mauto\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     15\u001b[0m     )\n\u001b[0;32m     16\u001b[0m     message_call \u001b[38;5;241m=\u001b[39m first_response\u001b[38;5;241m.\u001b[39mchoices[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mmessage\n\u001b[0;32m     17\u001b[0m     tools_calls \u001b[38;5;241m=\u001b[39m message_call\u001b[38;5;241m.\u001b[39mtool_calls\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\openai\\_utils\\_utils.py:275\u001b[0m, in \u001b[0;36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    273\u001b[0m             msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[38;5;241m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    274\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[1;32m--> 275\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\openai\\resources\\chat\\completions.py:829\u001b[0m, in \u001b[0;36mCompletions.create\u001b[1;34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[0;32m    788\u001b[0m \u001b[38;5;129m@required_args\u001b[39m([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m], [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[0;32m    789\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcreate\u001b[39m(\n\u001b[0;32m    790\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    826\u001b[0m     timeout: \u001b[38;5;28mfloat\u001b[39m \u001b[38;5;241m|\u001b[39m httpx\u001b[38;5;241m.\u001b[39mTimeout \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m|\u001b[39m NotGiven \u001b[38;5;241m=\u001b[39m NOT_GIVEN,\n\u001b[0;32m    827\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ChatCompletion \u001b[38;5;241m|\u001b[39m Stream[ChatCompletionChunk]:\n\u001b[0;32m    828\u001b[0m     validate_response_format(response_format)\n\u001b[1;32m--> 829\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_post(\n\u001b[0;32m    830\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/chat/completions\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    831\u001b[0m         body\u001b[38;5;241m=\u001b[39mmaybe_transform(\n\u001b[0;32m    832\u001b[0m             {\n\u001b[0;32m    833\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: messages,\n\u001b[0;32m    834\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m: model,\n\u001b[0;32m    835\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m\"\u001b[39m: audio,\n\u001b[0;32m    836\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfrequency_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: frequency_penalty,\n\u001b[0;32m    837\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunction_call\u001b[39m\u001b[38;5;124m\"\u001b[39m: function_call,\n\u001b[0;32m    838\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunctions\u001b[39m\u001b[38;5;124m\"\u001b[39m: functions,\n\u001b[0;32m    839\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlogit_bias\u001b[39m\u001b[38;5;124m\"\u001b[39m: logit_bias,\n\u001b[0;32m    840\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlogprobs\u001b[39m\u001b[38;5;124m\"\u001b[39m: logprobs,\n\u001b[0;32m    841\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_completion_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: max_completion_tokens,\n\u001b[0;32m    842\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: max_tokens,\n\u001b[0;32m    843\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmetadata\u001b[39m\u001b[38;5;124m\"\u001b[39m: metadata,\n\u001b[0;32m    844\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodalities\u001b[39m\u001b[38;5;124m\"\u001b[39m: modalities,\n\u001b[0;32m    845\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mn\u001b[39m\u001b[38;5;124m\"\u001b[39m: n,\n\u001b[0;32m    846\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mparallel_tool_calls\u001b[39m\u001b[38;5;124m\"\u001b[39m: parallel_tool_calls,\n\u001b[0;32m    847\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprediction\u001b[39m\u001b[38;5;124m\"\u001b[39m: prediction,\n\u001b[0;32m    848\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpresence_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: presence_penalty,\n\u001b[0;32m    849\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse_format\u001b[39m\u001b[38;5;124m\"\u001b[39m: response_format,\n\u001b[0;32m    850\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mseed\u001b[39m\u001b[38;5;124m\"\u001b[39m: seed,\n\u001b[0;32m    851\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mservice_tier\u001b[39m\u001b[38;5;124m\"\u001b[39m: service_tier,\n\u001b[0;32m    852\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstop\u001b[39m\u001b[38;5;124m\"\u001b[39m: stop,\n\u001b[0;32m    853\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstore\u001b[39m\u001b[38;5;124m\"\u001b[39m: store,\n\u001b[0;32m    854\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m: stream,\n\u001b[0;32m    855\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream_options\u001b[39m\u001b[38;5;124m\"\u001b[39m: stream_options,\n\u001b[0;32m    856\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtemperature\u001b[39m\u001b[38;5;124m\"\u001b[39m: temperature,\n\u001b[0;32m    857\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtool_choice\u001b[39m\u001b[38;5;124m\"\u001b[39m: tool_choice,\n\u001b[0;32m    858\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtools\u001b[39m\u001b[38;5;124m\"\u001b[39m: tools,\n\u001b[0;32m    859\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtop_logprobs\u001b[39m\u001b[38;5;124m\"\u001b[39m: top_logprobs,\n\u001b[0;32m    860\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtop_p\u001b[39m\u001b[38;5;124m\"\u001b[39m: top_p,\n\u001b[0;32m    861\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m: user,\n\u001b[0;32m    862\u001b[0m             },\n\u001b[0;32m    863\u001b[0m             completion_create_params\u001b[38;5;241m.\u001b[39mCompletionCreateParams,\n\u001b[0;32m    864\u001b[0m         ),\n\u001b[0;32m    865\u001b[0m         options\u001b[38;5;241m=\u001b[39mmake_request_options(\n\u001b[0;32m    866\u001b[0m             extra_headers\u001b[38;5;241m=\u001b[39mextra_headers, extra_query\u001b[38;5;241m=\u001b[39mextra_query, extra_body\u001b[38;5;241m=\u001b[39mextra_body, timeout\u001b[38;5;241m=\u001b[39mtimeout\n\u001b[0;32m    867\u001b[0m         ),\n\u001b[0;32m    868\u001b[0m         cast_to\u001b[38;5;241m=\u001b[39mChatCompletion,\n\u001b[0;32m    869\u001b[0m         stream\u001b[38;5;241m=\u001b[39mstream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[0;32m    870\u001b[0m         stream_cls\u001b[38;5;241m=\u001b[39mStream[ChatCompletionChunk],\n\u001b[0;32m    871\u001b[0m     )\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\openai\\_base_client.py:1277\u001b[0m, in \u001b[0;36mSyncAPIClient.post\u001b[1;34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[0m\n\u001b[0;32m   1263\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\n\u001b[0;32m   1264\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   1265\u001b[0m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1272\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m   1273\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[0;32m   1274\u001b[0m     opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[0;32m   1275\u001b[0m         method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39mto_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[0;32m   1276\u001b[0m     )\n\u001b[1;32m-> 1277\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest(cast_to, opts, stream\u001b[38;5;241m=\u001b[39mstream, stream_cls\u001b[38;5;241m=\u001b[39mstream_cls))\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\openai\\_base_client.py:954\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[1;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[0;32m    951\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    952\u001b[0m     retries_taken \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m--> 954\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_request(\n\u001b[0;32m    955\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[0;32m    956\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[0;32m    957\u001b[0m     stream\u001b[38;5;241m=\u001b[39mstream,\n\u001b[0;32m    958\u001b[0m     stream_cls\u001b[38;5;241m=\u001b[39mstream_cls,\n\u001b[0;32m    959\u001b[0m     retries_taken\u001b[38;5;241m=\u001b[39mretries_taken,\n\u001b[0;32m    960\u001b[0m )\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\pkq3\\Lib\\site-packages\\openai\\_base_client.py:1058\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[1;34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[0m\n\u001b[0;32m   1055\u001b[0m         err\u001b[38;5;241m.\u001b[39mresponse\u001b[38;5;241m.\u001b[39mread()\n\u001b[0;32m   1057\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRe-raising status error\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m-> 1058\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_status_error_from_response(err\u001b[38;5;241m.\u001b[39mresponse) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m   1060\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_response(\n\u001b[0;32m   1061\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[0;32m   1062\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1066\u001b[0m     retries_taken\u001b[38;5;241m=\u001b[39mretries_taken,\n\u001b[0;32m   1067\u001b[0m )\n", "\u001b[1;31mBadRequestError\u001b[0m: Error code: 400 - {'error': {'message': \"Messages with role 'tool' must be a response to a preceding message with 'tool_calls'\", 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}"]}], "source": ["auto_call(client,messages,tools)"]}], "metadata": {"kernelspec": {"display_name": "zjou-2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}