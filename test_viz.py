#!/usr/bin/env python
# coding: utf-8

# ## 智能体开发框架

# In[43]:


import pymysql

connection = pymysql.connect(
    host='localhost',
    user='root',
    passwd='',
    db='ads',
    charset='utf8'
)


# In[44]:


# 测试连接是否成功
try:
    cursor = connection.cursor()
    cursor.execute("SELECT DATABASE()")
    result = cursor.fetchone()
    print(f"当前连接的数据库: {result[0]}")
    cursor.close()
except Exception as e:
    print(f"连接失败: {e}")


# In[45]:


# 测试连接是否成功并查看表结构
try:
    cursor = connection.cursor()
    cursor.execute("SELECT DATABASE()")
    result = cursor.fetchone()
    print(f"当前连接的数据库: {result[0]}")
    
    # 查看数据库中的所有表
    print("\n📋 查看数据库中的表:")
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    
    if tables:
        print(f"发现 {len(tables)} 个表:")
        for i, table in enumerate(tables, 1):
            print(f"  {i}. {table[0]}")
        
        # 查看每个表的详细信息
        print("\n📊 表详细信息:")
        for table in tables:
            table_name = table[0]
            print(f"\n--- 表: {table_name} ---")
            
            # 查看表结构
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            print("字段结构:")
            for col in columns:
                print(f"  {col[0]} | {col[1]} | {col[2]} | {col[3]} | {col[4]} | {col[5]}")
            
            # 查看表记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"记录数: {count}")
            
            # 查看前3条示例数据
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                sample_data = cursor.fetchall()
                column_names = [desc[0] for desc in cursor.description]
                print("示例数据:")
                print(f"  列名: {column_names}")
                for row in sample_data:
                    print(f"  数据: {row}")
    else:
        print("❌ 数据库中没有找到任何表")
    
    cursor.close()
    
except Exception as e:
    print(f"连接失败: {e}")


# In[46]:


def custom_print(message): 
    print(f"{message}")

custom_print("Hello!") 


# In[47]:


# ===================================================================
# 🧠 模块1: 智能记忆系统 - 完全重构版
# ===================================================================

import time
from datetime import datetime
from typing import Dict, List, Any, Optional

class PowerBankIntelligentMemory:
    """共享充电宝智能记忆系统 - DeepSeek优化版"""
    
    def __init__(self, 
                 count_threshold: int = 25,
                 token_threshold: int = 4000,
                 use_token_mode: bool = True,
                 model: str = "deepseek-chat"):
        """初始化智能记忆系统"""
        
        # 基础配置
        self.messages = []
        self.count_threshold = count_threshold
        self.token_threshold = token_threshold
        self.use_token_mode = use_token_mode
        self.model = model
        self.current_count = 0
        self.current_tokens = 0
        
        # 业务记忆
        self.query_history = []
        self.business_context = {
            "current_analysis_type": None,
            "focused_tables": [],
            "key_metrics": [],
            "last_results": None,
            "session_start": datetime.now()
        }
        
        # 业务场景映射
        self.business_scenarios = {
            "用户分析": ["user_table", "user_behavior_summary"],
            "品牌分析": ["brand_revenue_summary", "order_table"],
            "地区分析": ["region_table", "region_heatmap_data", "region_usage_summary"],
            "时间分析": ["time_table", "time_summary", "order_table"],
            "综合分析": ["order_table", "user_table", "region_table", "time_table"]
        }
        
        # 初始化Token计算器
        self._init_token_calculator()
        
        custom_print(f"🧠 智能记忆系统初始化完成")
        custom_print(f"📊 模式: {'DeepSeek Token模式' if self.use_token_mode else '数量模式'}")
        custom_print(f"🎯 阈值: {self.token_threshold if self.use_token_mode else self.count_threshold}")
    
    def _init_token_calculator(self):
        """初始化Token计算器"""
        try:
            if self.use_token_mode and "deepseek" in self.model.lower():
                # DeepSeek专用Token计算
                self.token_calculator = self._deepseek_token_estimate
                custom_print("✅ DeepSeek Token计算器已启用")
            else:
                # 简单计算
                self.token_calculator = self._simple_token_estimate
                custom_print("✅ 简单Token计算器已启用")
        except Exception as e:
            custom_print(f"⚠️ Token计算器初始化失败: {e}")
            self.token_calculator = self._simple_token_estimate
    
    def _deepseek_token_estimate(self, text: str) -> int:
        """DeepSeek专用Token估算"""
        if not text:
            return 0
        
        # 中文字符计数
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        # 英文单词计数
        english_words = len([w for w in text.replace('，', ' ').replace('。', ' ').split() if w.strip()])
        # SQL关键词计数
        sql_keywords = len([w for w in text.upper().split() if w in ['SELECT', 'FROM', 'WHERE', 'GROUP', 'ORDER', 'BY', 'JOIN']])
        
        # DeepSeek对中文友好的估算公式
        estimated_tokens = int(chinese_chars * 1.3 + english_words * 1.1 + sql_keywords * 0.9)
        return max(estimated_tokens, len(text) // 4)
    
    def _simple_token_estimate(self, text: str) -> int:
        """简单Token估算"""
        return len(text.split()) if text else 0
    
    def append_message(self, message: Dict) -> bool:
        """添加消息到记忆系统"""
        try:
            if not isinstance(message, dict) or 'content' not in message:
                custom_print("⚠️ 无效消息格式")
                return False
            
            # 计算Token
            content = message.get('content', '')
            tokens = self.token_calculator(content)
            
            # 添加元数据
            enhanced_message = {
                **message,
                'timestamp': datetime.now().isoformat(),
                'tokens': tokens,
                'business_context': self._detect_business_context(content)
            }
            
            # 检查是否需要清理
            if self._should_cleanup(tokens):
                self._smart_cleanup()
            
            # 添加消息
            self.messages.append(enhanced_message)
            self.current_count += 1
            self.current_tokens += tokens
            
            # 更新业务上下文
            self._update_business_context(enhanced_message)
            
            return True
            
        except Exception as e:
            custom_print(f"❌ 添加消息失败: {e}")
            return False
    
    def _detect_business_context(self, content: str) -> Dict:
        """检测业务上下文"""
        context = {
            "scenario": "未知",
            "tables": [],
            "keywords": []
        }
        
        # 场景检测
        for scenario, tables in self.business_scenarios.items():
            if any(keyword in content for keyword in scenario.replace('分析', '').split()):
                context["scenario"] = scenario
                context["tables"] = tables
                break
        
        # 关键词提取
        business_keywords = ['用户', '品牌', '地区', '时间', '收入', '订单', '分析', '统计', '查询']
        context["keywords"] = [kw for kw in business_keywords if kw in content]
        
        return context
    
    def _should_cleanup(self, new_tokens: int) -> bool:
        """判断是否需要清理"""
        if self.use_token_mode:
            return (self.current_tokens + new_tokens) > self.token_threshold
        else:
            return (self.current_count + 1) > self.count_threshold
    
    def _smart_cleanup(self):
        """智能清理策略"""
        try:
            if not self.messages:
                return
            
            # 保留系统消息和最近的重要消息
            important_messages = []
            recent_messages = []
            
            for msg in self.messages:
                if msg.get('role') == 'system':
                    important_messages.append(msg)
                elif len(recent_messages) < 5:  # 保留最近5条
                    recent_messages.append(msg)
            
            # 重新计算
            self.messages = important_messages + recent_messages[-5:]
            self.current_count = len(self.messages)
            self.current_tokens = sum(msg.get('tokens', 0) for msg in self.messages)
            
            custom_print(f"🧹 智能清理完成，保留 {len(self.messages)} 条消息")
            
        except Exception as e:
            custom_print(f"❌ 清理失败: {e}")
    
    def _update_business_context(self, message: Dict):
        """更新业务上下文"""
        try:
            business_info = message.get('business_context', {})
            scenario = business_info.get('scenario', '未知')
            
            if scenario != '未知':
                self.business_context['current_analysis_type'] = scenario
                self.business_context['focused_tables'] = business_info.get('tables', [])
            
            # 记录SQL查询
            content = message.get('content', '')
            if 'SELECT' in content.upper():
                self.query_history.append({
                    'sql': content,
                    'timestamp': datetime.now().isoformat(),
                    'scenario': scenario
                })
                
        except Exception as e:
            custom_print(f"⚠️ 业务上下文更新失败: {e}")
    
    def get_messages(self) -> List[Dict]:
        """获取消息列表"""
        return self.messages.copy()
    
    def get_business_summary(self) -> Dict:
        """获取业务摘要"""
        return {
            "current_scenario": self.business_context.get('current_analysis_type', '未知'),
            "focused_tables": self.business_context.get('focused_tables', []),
            "query_count": len(self.query_history),
            "message_count": len(self.messages),
            "token_usage": f"{self.current_tokens}/{self.token_threshold}" if self.use_token_mode else f"{self.current_count}/{self.count_threshold}"
        }
    
    def clear_memory(self):
        """清空记忆"""
        self.messages.clear()
        self.query_history.clear()
        self.current_count = 0
        self.current_tokens = 0
        custom_print("🧹 记忆已清空")


# In[48]:


# ===================================================================
# 📚 模块2: 专家知识系统 - 完全重构版
# ===================================================================

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any

class PowerBankExpertKnowledge:
    """共享充电宝专家知识系统"""
    
    def __init__(self):
        """初始化专家知识系统"""
        self.knowledge_base = {}
        self.analysis_templates = {}
        self.sql_templates = {}
        self.business_rules = {}
        
        # 初始化知识库
        self._init_knowledge_base()
        custom_print("📚 专家知识系统初始化完成")
    
    def _init_knowledge_base(self):
        """初始化知识库"""
        try:
            # 分析模板
            self.analysis_templates = {
                "品牌分析": {
                    "description": "品牌市场份额和竞争力分析",
                    "key_metrics": ["市场份额", "收入占比", "用户偏好", "增长趋势"],
                    "recommended_tables": ["brand_revenue_summary", "order_table"],
                    "sql_patterns": ["GROUP BY brand", "SUM(revenue)", "COUNT(DISTINCT user_id)"]
                },
                "用户分析": {
                    "description": "用户行为和特征分析",
                    "key_metrics": ["活跃用户数", "使用频次", "消费习惯", "年龄分布"],
                    "recommended_tables": ["user_table", "user_behavior_summary"],
                    "sql_patterns": ["GROUP BY age_group", "AVG(usage_frequency)", "COUNT(user_id)"]
                },
                "地区分析": {
                    "description": "地区使用情况和热点分析",
                    "key_metrics": ["地区收入", "使用密度", "热点分布", "增长潜力"],
                    "recommended_tables": ["region_table", "region_usage_summary", "region_heatmap_data"],
                    "sql_patterns": ["GROUP BY region", "SUM(usage_count)", "AVG(revenue_per_region)"]
                },
                "时间分析": {
                    "description": "时间趋势和周期性分析",
                    "key_metrics": ["月度趋势", "季节性变化", "增长率", "峰值时段"],
                    "recommended_tables": ["time_table", "time_summary", "order_table"],
                    "sql_patterns": ["GROUP BY month", "ORDER BY date", "DATE_FORMAT"]
                }
            }
            
            # SQL模板
            self.sql_templates = {
                "品牌分析": {
                    "市场份额": """
                        SELECT brand, 
                               SUM(revenue) as total_revenue,
                               COUNT(*) as order_count,
                               ROUND(SUM(revenue) * 100.0 / (SELECT SUM(revenue) FROM brand_revenue_summary), 2) as market_share
                        FROM brand_revenue_summary 
                        GROUP BY brand 
                        ORDER BY total_revenue DESC
                    """,
                    "品牌排名": """
                        SELECT brand, 
                               SUM(revenue) as revenue,
                               RANK() OVER (ORDER BY SUM(revenue) DESC) as ranking
                        FROM brand_revenue_summary 
                        GROUP BY brand
                    """
                },
                "用户分析": {
                    "年龄分布": """
                        SELECT age_group, 
                               COUNT(*) as user_count,
                               AVG(usage_frequency) as avg_usage
                        FROM user_behavior_summary 
                        GROUP BY age_group 
                        ORDER BY user_count DESC
                    """,
                    "活跃用户": """
                        SELECT COUNT(DISTINCT user_id) as active_users,
                               AVG(usage_frequency) as avg_frequency
                        FROM user_behavior_summary 
                        WHERE usage_frequency > 5
                    """
                }
            }
            
            # 业务规则
            self.business_rules = {
                "数据质量": {
                    "min_sample_size": 100,
                    "required_fields": ["user_id", "brand", "region", "date"],
                    "valid_ranges": {
                        "revenue": (0, 10000),
                        "usage_frequency": (0, 100)
                    }
                },
                "分析指导": {
                    "trend_analysis_period": 12,  # 月
                    "significant_change_threshold": 0.1,  # 10%
                    "top_n_default": 10
                }
            }
            
            custom_print("✅ 知识库加载完成")
            
        except Exception as e:
            custom_print(f"❌ 知识库初始化失败: {e}")
    
    def get_analysis_template(self, analysis_type: str) -> Optional[Dict]:
        """获取分析模板"""
        return self.analysis_templates.get(analysis_type)
    
    def get_sql_template(self, analysis_type: str, specific_query: str = None) -> Optional[str]:
        """获取SQL模板"""
        templates = self.sql_templates.get(analysis_type, {})
        if specific_query and specific_query in templates:
            return templates[specific_query].strip()
        elif templates:
            return list(templates.values())[0].strip()
        return None
    
    def get_business_rules(self, rule_type: str = None) -> Dict:
        """获取业务规则"""
        if rule_type:
            return self.business_rules.get(rule_type, {})
        return self.business_rules
    
    def suggest_analysis_approach(self, question: str) -> Dict:
        """建议分析方法"""
        suggestions = {
            "analysis_type": "综合分析",
            "recommended_tables": [],
            "key_metrics": [],
            "sql_hints": []
        }
        
        # 关键词匹配
        for analysis_type, template in self.analysis_templates.items():
            keywords = analysis_type.replace('分析', '').split()
            if any(keyword in question for keyword in keywords):
                suggestions["analysis_type"] = analysis_type
                suggestions["recommended_tables"] = template["recommended_tables"]
                suggestions["key_metrics"] = template["key_metrics"]
                suggestions["sql_hints"] = template["sql_patterns"]
                break
        
        return suggestions

class PowerBankLongTermMemory:
    """长期记忆系统"""
    
    def __init__(self, base_path: str = None):
        """初始化长期记忆"""
        self.base_path = Path(base_path) if base_path else Path.cwd() / "powerbank_memory"
        self.base_path.mkdir(exist_ok=True)
        
        self.qa_file = self.base_path / "qa_history.json"
        self.analysis_file = self.base_path / "analysis_history.json"
        
        # 确保文件存在
        self._ensure_files_exist()
        custom_print(f"💾 长期记忆系统初始化完成: {self.base_path}")
    
    def _ensure_files_exist(self):
        """确保记忆文件存在"""
        try:
            if not self.qa_file.exists():
                self.qa_file.write_text("[]", encoding='utf-8')
            if not self.analysis_file.exists():
                self.analysis_file.write_text("[]", encoding='utf-8')
        except Exception as e:
            custom_print(f"⚠️ 记忆文件创建失败: {e}")
    
    def save_qa(self, question: str, answer: str, metadata: Dict = None) -> bool:
        """保存问答对"""
        try:
            # 读取现有数据
            qa_data = []
            if self.qa_file.exists():
                try:
                    qa_data = json.loads(self.qa_file.read_text(encoding='utf-8'))
                except:
                    qa_data = []
            
            # 添加新记录
            new_record = {
                "question": question,
                "answer": answer,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            qa_data.append(new_record)
            
            # 保持最近1000条记录
            if len(qa_data) > 1000:
                qa_data = qa_data[-1000:]
            
            # 保存
            self.qa_file.write_text(json.dumps(qa_data, ensure_ascii=False, indent=2), encoding='utf-8')
            return True
            
        except Exception as e:
            custom_print(f"❌ QA保存失败: {e}")
            return False
    
    def search_similar_qa(self, question: str, limit: int = 5) -> List[Dict]:
        """搜索相似问答"""
        try:
            if not self.qa_file.exists():
                return []
            
            qa_data = json.loads(self.qa_file.read_text(encoding='utf-8'))
            
            # 简单的关键词匹配
            question_keywords = set(question.split())
            similar_qa = []
            
            for qa in qa_data:
                qa_keywords = set(qa['question'].split())
                similarity = len(question_keywords & qa_keywords) / len(question_keywords | qa_keywords)
                
                if similarity > 0.3:  # 相似度阈值
                    qa['similarity'] = similarity
                    similar_qa.append(qa)
            
            # 按相似度排序
            similar_qa.sort(key=lambda x: x['similarity'], reverse=True)
            return similar_qa[:limit]
            
        except Exception as e:
            custom_print(f"❌ QA搜索失败: {e}")
            return []
    
    def save_analysis(self, analysis_type: str, question: str, sql_query: str, 
                     data_result: List[Dict], insights: str, metadata: Dict = None) -> bool:
        """保存数据分析记录"""
        try:
            # 读取现有数据
            analysis_data = []
            if self.analysis_file.exists():
                try:
                    analysis_data = json.loads(self.analysis_file.read_text(encoding='utf-8'))
                except:
                    analysis_data = []
            
            # 添加新记录
            new_record = {
                "analysis_type": analysis_type,
                "question": question,
                "sql_query": sql_query,
                "data_count": len(data_result) if data_result else 0,
                "data_sample": data_result[:3] if data_result else [],  # 保存前3条数据作为样本
                "insights": insights,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            analysis_data.append(new_record)
            
            # 保持最近500条分析记录
            if len(analysis_data) > 500:
                analysis_data = analysis_data[-500:]
            
            # 保存
            self.analysis_file.write_text(json.dumps(analysis_data, ensure_ascii=False, indent=2), encoding='utf-8')
            return True
            
        except Exception as e:
            custom_print(f"❌ 分析记录保存失败: {e}")
            return False
    
    def search_similar_analysis(self, analysis_type: str, limit: int = 5) -> List[Dict]:
        """搜索相似的分析记录"""
        try:
            if not self.analysis_file.exists():
                return []
            
            analysis_data = json.loads(self.analysis_file.read_text(encoding='utf-8'))
            
            # 按分析类型筛选
            similar_analysis = []
            for record in analysis_data:
                if analysis_type in record.get('analysis_type', '') or \
                   analysis_type in record.get('question', ''):
                    similar_analysis.append(record)
            
            # 按时间倒序排序，返回最新的记录
            similar_analysis.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            return similar_analysis[:limit]
            
        except Exception as e:
            custom_print(f"❌ 分析记录搜索失败: {e}")
            return []
    
    def get_analysis_stats(self) -> Dict:
        """获取分析统计信息"""
        try:
            if not self.analysis_file.exists():
                return {"total_analysis": 0, "analysis_types": {}}
            
            analysis_data = json.loads(self.analysis_file.read_text(encoding='utf-8'))
            
            # 统计分析类型
            analysis_types = {}
            for record in analysis_data:
                analysis_type = record.get('analysis_type', '未知')
                analysis_types[analysis_type] = analysis_types.get(analysis_type, 0) + 1
            
            return {
                "total_analysis": len(analysis_data),
                "analysis_types": analysis_types,
                "latest_analysis": analysis_data[-1] if analysis_data else None
            }
            
        except Exception as e:
            custom_print(f"❌ 分析统计失败: {e}")
            return {"total_analysis": 0, "analysis_types": {}}


# In[49]:


# ===================================================================
# 🔍 模块3: 智能查询引擎 - 完全重构版
# ===================================================================

import pymysql
from openai import OpenAI
import httpx
import json
from typing import Dict, List, Optional, Any, Union

class PowerBankIntelligentQueryEngine:
    """共享充电宝智能查询引擎"""
    
    def __init__(self, db_config: Dict, api_config: Dict):
        """初始化查询引擎"""
        self.db_config = db_config
        self.api_config = api_config
        
        # 状态管理
        self.connection = None
        self.client = None
        self.table_schemas = {}
        self.query_cache = {}
        
        # 性能统计
        self.stats = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "cache_hits": 0
        }
        
        # 初始化组件
        self._init_database()
        self._init_ai_client()
        self._load_table_schemas()
        
        custom_print("🔍 智能查询引擎初始化完成")
    
    def _init_database(self) -> bool:
        """初始化数据库连接"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset=self.db_config.get('charset', 'utf8mb4'),
                autocommit=self.db_config.get('autocommit', True),
                connect_timeout=10,
                read_timeout=30,
                write_timeout=30
            )
            
            # 测试连接
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            
            custom_print("✅ 数据库连接成功")
            return True
            
        except Exception as e:
            custom_print(f"❌ 数据库连接失败: {e}")
            self.connection = None
            return False
    
    def _init_ai_client(self) -> bool:
        """初始化AI客户端"""
        try:
            http_client = httpx.Client(follow_redirects=True, timeout=30)
            self.client = OpenAI(
                api_key=self.api_config['api_key'],
                base_url=self.api_config['base_url'],
                http_client=http_client
            )
            
            # 测试连接
            test_response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[{"role": "user", "content": "测试"}],
                max_tokens=5
            )
            
            custom_print("✅ AI客户端连接成功")
            return True
            
        except Exception as e:
            custom_print(f"❌ AI客户端初始化失败: {e}")
            self.client = None
            return False
    
    def _load_table_schemas(self) -> bool:
        """加载表结构信息"""
        try:
            if not self.connection:
                custom_print("❌ 数据库未连接，无法加载表结构")
                return False
            
            expected_tables = [
                'user_table', 'region_table', 'time_table', 'order_table',
                'brand_revenue_summary', 'user_behavior_summary', 
                'time_summary', 'region_usage_summary', 'region_heatmap_data'
            ]
            
            with self.connection.cursor() as cursor:
                for table in expected_tables:
                    try:
                        # 获取表结构
                        cursor.execute(f"DESCRIBE {table}")
                        columns = cursor.fetchall()
                        
                        # 获取示例数据
                        cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                        sample_data = cursor.fetchall()
                        
                        # 获取记录数
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        record_count = cursor.fetchone()[0]
                        
                        self.table_schemas[table] = {
                            "columns": columns,
                            "sample_data": sample_data,
                            "record_count": record_count,
                            "status": "available"
                        }
                        
                    except Exception as e:
                        self.table_schemas[table] = {
                            "status": "missing",
                            "error": str(e)
                        }
            
            available_tables = [t for t, info in self.table_schemas.items() if info.get("status") == "available"]
            custom_print(f"✅ 表结构加载完成: {len(available_tables)}/{len(expected_tables)} 个表可用")
            
            return len(available_tables) > 0
            
        except Exception as e:
            custom_print(f"❌ 表结构加载失败: {e}")
            return False
    
    def natural_language_to_sql(self, question: str, context: str = "") -> Optional[str]:
        """自然语言转SQL"""
        try:
            if not self.client:
                custom_print("❌ AI客户端不可用")
                return None
            
            if not question or question.strip() == "":
                custom_print("❌ 问题为空")
                return None
            
            # 检查缓存
            cache_key = f"{question}_{context}"
            if cache_key in self.query_cache:
                self.stats["cache_hits"] += 1
                return self.query_cache[cache_key]
            
            # 构建数据库信息
            db_info = self._build_database_info()
            
            # 构建提示词
            prompt = f"""
你是一个专业的SQL查询生成专家。请根据用户的自然语言问题生成准确的SQL查询语句。

数据库信息：
{db_info}

用户问题：{question}

上下文信息：{context}

要求：
1. 只返回SQL语句，不要其他解释
2. 使用标准的MySQL语法
3. 确保字段名和表名正确
4. 添加适当的ORDER BY和LIMIT子句
5. 对于聚合查询，使用GROUP BY

SQL查询：
"""
            
            # 调用AI生成SQL
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是SQL查询生成专家，只返回SQL语句。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )
            
            sql_query = response.choices[0].message.content.strip()
            
            # 清理SQL
            sql_query = self._clean_sql(sql_query)
            
            # 缓存结果
            self.query_cache[cache_key] = sql_query
            
            return sql_query
            
        except Exception as e:
            custom_print(f"❌ SQL生成失败: {e}")
            return None
    
    def execute_query(self, sql_query: str) -> Optional[List[Dict]]:
        """执行SQL查询"""
        try:
            if not self.connection:
                custom_print("❌ 数据库未连接")
                return None
            
            if not sql_query or sql_query.strip() == "":
                custom_print("❌ SQL查询为空")
                return None
            
            self.stats["total_queries"] += 1
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql_query)
                results = cursor.fetchall()
                
                # 确保返回列表
                if results is None:
                    results = []
                elif not isinstance(results, list):
                    results = list(results)
                
                self.stats["successful_queries"] += 1
                custom_print(f"✅ 查询成功，返回 {len(results)} 条记录")
                
                return results
                
        except Exception as e:
            self.stats["failed_queries"] += 1
            custom_print(f"❌ 查询执行失败: {e}")
            return []  # 返回空列表而不是None
    
    def generate_business_insight(self, question: str, data: List[Dict], context: str = "") -> str:
        """生成业务洞察"""
        try:
            if not self.client:
                return "AI服务不可用，无法生成洞察"
            
            if not data:
                return "查询结果为空，无法生成洞察"
            
            # 数据摘要
            data_summary = self._create_data_summary(data)
            
            # 构建洞察提示词
            prompt = f"""
作为共享充电宝业务分析专家，请基于以下查询结果生成专业的业务洞察：

用户问题：{question}
查询结果摘要：{data_summary}
上下文：{context}

请提供：
1. 数据解读：关键数字和趋势
2. 业务洞察：数据背后的业务含义
3. 行动建议：具体可执行的优化建议

洞察分析：
"""
            
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是共享充电宝业务分析专家，提供专业的数据洞察。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=800
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            return f"洞察生成失败: {str(e)}"
    
    def _build_database_info(self) -> str:
        """构建数据库信息"""
        info_parts = []
        
        for table_name, schema_info in self.table_schemas.items():
            if schema_info.get("status") == "available":
                columns = schema_info.get("columns", [])
                record_count = schema_info.get("record_count", 0)
                
                column_info = ", ".join([f"{col[0]}({col[1]})" for col in columns[:5]])  # 只显示前5个字段
                info_parts.append(f"表 {table_name}: {column_info} (共{record_count}条记录)")
        
        return "\n".join(info_parts)
    
    def _clean_sql(self, sql: str) -> str:
        """清理SQL语句"""
        if not sql:
            return ""
        
        # 移除markdown标记
        sql = sql.replace("```sql", "").replace("```", "")
        
        # 移除多余的空白
        sql = " ".join(sql.split())
        
        # 确保以分号结尾
        if not sql.endswith(';'):
            sql += ';'
        
        return sql
    
    def _create_data_summary(self, data: List[Dict]) -> str:
        """创建数据摘要"""
        if not data:
            return "无数据"
        
        summary_parts = [f"总记录数: {len(data)}"]
        
        # 分析数值字段
        if data:
            first_row = data[0]
            for key, value in first_row.items():
                if isinstance(value, (int, float)):
                    values = [row.get(key, 0) for row in data if isinstance(row.get(key), (int, float))]
                    if values:
                        summary_parts.append(f"{key}: 最大值{max(values)}, 最小值{min(values)}, 平均值{sum(values)/len(values):.2f}")
        
        # 显示前3条示例
        if len(data) > 0:
            summary_parts.append(f"示例数据: {data[:3]}")
        
        return "; ".join(summary_parts)
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        return self.stats.copy()
    
    def health_check(self) -> Dict:
        """健康检查"""
        return {
            "database_connected": self.connection is not None,
            "ai_client_available": self.client is not None,
            "tables_loaded": len([t for t, info in self.table_schemas.items() if info.get("status") == "available"]),
            "total_tables": len(self.table_schemas),
            "performance_stats": self.stats
        }
        


# In[50]:


# ===================================================================
# 🚀 融合系统: 智能数据分析助手 - 完全重构版 v3.0
# ===================================================================

class PowerBankIntelligentAssistant:
    """共享充电宝智能数据分析助手 - 企业级融合系统"""
    
    def __init__(self, db_config: Dict, api_config: Dict, memory_config: Dict = None):
        """
        初始化融合系统
        
        Args:
            db_config: 数据库配置字典
            api_config: API配置字典  
            memory_config: 记忆系统配置字典
        """
        self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.db_config = db_config
        self.api_config = api_config
        self.memory_config = memory_config or {}
        
        # 系统状态管理
        self.system_status = {
            "session_id": self.session_id,
            "modules_loaded": [],
            "initialization_complete": False,
            "health": {
                "database_connection": "未连接",
                "ai_connection": "未连接", 
                "memory_system": "未初始化",
                "expert_knowledge": "未加载"
            },
            "performance": {
                "total_queries": 0,
                "successful_queries": 0,
                "failed_queries": 0,
                "avg_response_time": 0,
                "session_start_time": datetime.now()
            },
            "errors": []
        }
        
        # 对话上下文
        self.conversation_context = {
            "current_topic": None,
            "recent_analyses": [],
            "interaction_count": 0,
            "user_preferences": {},
            "session_start_time": datetime.now()
        }
        
        # 核心模块实例
        self.short_memory = None
        self.expert_knowledge = None
        self.long_memory = None
        self.query_engine = None
        self.visualization_engine = None
        
        # 初始化系统
        self.initialization_success = self._initialize_system()
        
        if self.initialization_success:
            custom_print("🎉 融合系统初始化完成！")
        else:
            custom_print("⚠️ 融合系统初始化部分失败，但系统仍可运行")
    
    def _initialize_system(self) -> bool:
        """
        初始化融合系统的所有模块
        
        Returns:
            bool: 初始化是否成功（至少3个模块成功加载）
        """
        try:
            custom_print(f"🚀 正在初始化智能数据分析助手 v3.0...")
            custom_print(f"🆔 会话ID: {self.session_id}")
            
            success_count = 0
            total_modules = 4
            
            # 1. 初始化智能记忆系统
            try:
                custom_print("🧠 初始化智能记忆模块...")
                self.short_memory = PowerBankIntelligentMemory(
                    count_threshold=self.memory_config.get('count_threshold', 25),
                    token_threshold=self.memory_config.get('token_threshold', 4000),
                    use_token_mode=self.memory_config.get('use_token_mode', True),
                    model='deepseek-chat'
                )
                self.system_status["modules_loaded"].append("智能记忆系统")
                self.system_status["health"]["memory_system"] = "已初始化"
                success_count += 1
                custom_print("  ✅ 智能记忆模块加载成功")
            except Exception as e:
                custom_print(f"  ❌ 智能记忆模块加载失败: {e}")
                self.system_status["errors"].append(f"记忆模块: {str(e)}")
            
            # 2. 初始化专家知识库
            try:
                custom_print("📚 加载专家知识库...")
                self.expert_knowledge = PowerBankExpertKnowledge()
                self.long_memory = PowerBankLongTermMemory(
                    base_path=self.memory_config.get('base_path', './powerbank_memory')
                )
                self.system_status["modules_loaded"].append("专家知识库")
                self.system_status["health"]["expert_knowledge"] = "已加载"
                success_count += 1
                custom_print("  ✅ 专家知识库加载成功")
            except Exception as e:
                custom_print(f"  ❌ 专家知识库加载失败: {e}")
                self.system_status["errors"].append(f"知识库: {str(e)}")
            
            # 3. 初始化查询引擎
            try:
                custom_print("🔍 启动智能查询引擎...")
                self.query_engine = PowerBankIntelligentQueryEngine(
                    self.db_config, 
                    self.api_config
                )
                
                # 检查查询引擎健康状态
                health = self.query_engine.health_check()
                if health["database_connected"]:
                    self.system_status["health"]["database_connection"] = "已连接"
                if health["ai_client_available"]:
                    self.system_status["health"]["ai_connection"] = "已连接"
                
                self.system_status["modules_loaded"].append("智能查询引擎")
                success_count += 1
                custom_print("  ✅ 智能查询引擎加载成功")
                
            except Exception as e:
                custom_print(f"  ❌ 智能查询引擎加载失败: {e}")
                self.system_status["errors"].append(f"查询引擎: {str(e)}")
            
            # 4. 初始化可视化引擎
            try:
                custom_print("📊 启动数据可视化引擎...")
                if 'PowerBankVisualizationEngine' in globals():
                    ai_client = self.query_engine.client if self.query_engine else None
                    self.visualization_engine = PowerBankVisualizationEngine(ai_client=ai_client)
                    self.system_status["modules_loaded"].append("数据可视化引擎")
                    success_count += 1
                    custom_print("  ✅ 数据可视化引擎加载成功")
                else:
                    custom_print("  ⚠️ 可视化引擎类未定义，跳过加载")
            except Exception as e:
                custom_print(f"  ❌ 数据可视化引擎加载失败: {e}")
                self.system_status["errors"].append(f"可视化引擎: {str(e)}")
            
            # 5. 系统完整性检查
            try:
                custom_print("🔧 执行系统完整性检查...")
                self._system_health_check()
                success_count += 1
                custom_print("  ✅ 系统完整性检查完成")
            except Exception as e:
                custom_print(f"  ⚠️ 系统完整性检查异常: {e}")
            
            # 更新系统状态
            self.system_status["initialization_complete"] = success_count >= 3  # 至少3个模块成功
            self.system_status["performance"]["session_start_time"] = datetime.now()
            
            custom_print(f"📊 模块加载统计: {success_count}/{total_modules} 个模块成功")
            custom_print(f"📋 已加载模块: {', '.join(self.system_status['modules_loaded'])}")
            
            return success_count >= 3
            
        except Exception as e:
            custom_print(f"❌ 系统初始化严重失败: {e}")
            self.system_status["errors"].append(f"系统初始化: {str(e)}")
            return False
    
    def _system_health_check(self):
        """
        系统健康检查
        检查各核心模块的运行状态和连接状态
        """
        health_issues = []
        
        # 检查核心模块
        if not self.short_memory:
            health_issues.append("智能记忆系统未初始化")
        if not self.expert_knowledge:
            health_issues.append("专家知识库未加载")
        if not self.query_engine:
            health_issues.append("查询引擎未启动")
        
        # 检查数据库连接
        if self.query_engine:
            health = self.query_engine.health_check()
            if not health["database_connected"]:
                health_issues.append("数据库连接失败")
            if not health["ai_client_available"]:
                health_issues.append("AI服务连接失败")
        
        if health_issues:
            custom_print(f"⚠️ 发现健康问题: {'; '.join(health_issues)}")
        else:
            custom_print("✅ 系统健康状态良好")
    
    def _is_data_query(self, question: str) -> bool:
        """
        判断是否为数据查询问题
        通过关键词匹配识别用户问题类型
        
        Args:
            question: 用户输入的问题
            
        Returns:
            bool: True表示数据查询问题，False表示普通聊天问题
        """
        # 数据查询关键词
        data_keywords = [
            # 表名相关
            'user_table', 'order_table', 'region_table', 'time_table',
            'brand_revenue_summary', 'user_behavior_summary',
            
            # 查询动词
            '查询', '统计', '分析', '计算', '多少', '几个', '数量',
            '比例', '占比', '排名', '排序', '最多', '最少', '平均',
            
            # 业务词汇
            '用户', '品牌', '地区', '收入', '订单', '充电宝',
            '男女', '性别', '年龄', '职业', '省份', '城市',
            
            # SQL相关
            'SELECT', 'COUNT', 'SUM', 'AVG', 'GROUP BY'
        ]
        
        question_lower = question.lower()
        return any(keyword in question_lower for keyword in data_keywords)

    def _handle_general_chat(self, question: str) -> str:
        """
        处理普通聊天问题
        调用AI服务进行通用对话回复
        
        Args:
            question: 用户的聊天问题
            
        Returns:
            str: AI生成的回复内容
        """
        try:
            if not self.query_engine or not self.query_engine.client:
                return "抱歉，AI服务暂时不可用"
            
            response = self.query_engine.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个友好的AI助手，可以回答各种问题。"},
                    {"role": "user", "content": question}
                ],
                temperature=0.7,
                max_tokens=500
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            return f"回答生成失败: {str(e)}"
    
    def analyze(self, question: str) -> Dict[str, Any]:
        """
        智能分析主入口
        根据问题类型分发到不同的处理流程
        
        Args:
            question: 用户输入的问题
            
        Returns:
            Dict: 包含分析结果、响应内容、处理时间等信息的字典
        """
        start_time = time.time()
        
        try:
            # 更新统计
            self.system_status["performance"]["total_queries"] += 1
            self.conversation_context["interaction_count"] += 1
            
            # 输入验证
            if not question or question.strip() == "":
                return {
                    "success": False,
                    "response": "请输入有效的问题",
                    "error": "空问题",
                    "processing_time": 0
                }
            
            custom_print(f"🔍 开始分析问题: {question[:50]}...")
            
            # 判断问题类型
            if not self._is_data_query(question):
                # 普通聊天问题
                custom_print("💬 识别为普通对话问题")
                response = self._handle_general_chat(question)
                processing_time = time.time() - start_time
                
                # 添加到短期记忆
                if self.short_memory:
                    try:
                        self.short_memory.append_message({"role": "user", "content": question})
                        self.short_memory.append_message({"role": "assistant", "content": response})
                    except Exception as e:
                        custom_print(f"⚠️ 短期记忆保存异常: {e}")
                
                # 添加到长期记忆
                if self.long_memory:
                    try:
                        metadata = {
                            "query_type": "general_chat",
                            "processing_time": processing_time,
                            "success": True
                        }
                        self.long_memory.save_qa(question, response, metadata)
                        custom_print("💾 已保存到长期记忆")
                    except Exception as e:
                        custom_print(f"⚠️ 长期记忆保存异常: {e}")
                
                return {
                    "success": True,
                    "response": response,
                    "query_type": "general_chat",
                    "processing_time": processing_time
                }
            
            # 数据查询问题处理流程
            custom_print("📊 识别为数据查询问题")
            
            # 1. 记忆系统处理
            memory_context = ""
            if self.short_memory:
                try:
                    # 添加用户问题到记忆
                    self.short_memory.append_message({
                        "role": "user",
                        "content": question
                    })
                    
                    # 获取业务摘要
                    business_summary = self.short_memory.get_business_summary()
                    memory_context = f"当前场景: {business_summary.get('current_scenario', '未知')}"
                    
                except Exception as e:
                    custom_print(f"⚠️ 记忆系统处理异常: {e}")
            
            # 2. 专家知识分析
            expert_context = ""
            if self.expert_knowledge:
                try:
                    suggestion = self.expert_knowledge.suggest_analysis_approach(question)
                    expert_context = f"建议分析类型: {suggestion['analysis_type']}"
                except Exception as e:
                    custom_print(f"⚠️ 专家知识分析异常: {e}")
            
            # 3. SQL查询执行
            sql_result = []
            sql_query = ""
            if self.query_engine:
                try:
                    # 生成SQL
                    context = f"{memory_context}; {expert_context}"
                    sql_query = self.query_engine.natural_language_to_sql(question, context)
                    
                    if sql_query:
                        custom_print(f"📝 生成SQL: {sql_query[:100]}...")
                        # 执行查询
                        sql_result = self.query_engine.execute_query(sql_query)
                        if sql_result is None:
                            sql_result = []
                        custom_print(f"📊 查询结果: {len(sql_result)} 条记录")
                    else:
                        custom_print("⚠️ SQL生成失败")
                        
                except Exception as e:
                    custom_print(f"❌ 查询执行异常: {e}")
                    sql_result = []
            
            # 4. 生成业务洞察
            insight = ""
            if self.query_engine and sql_result:
                try:
                    full_context = f"{memory_context}; {expert_context}"
                    insight = self.query_engine.generate_business_insight(question, sql_result, full_context)
                except Exception as e:
                    custom_print(f"⚠️ 洞察生成异常: {e}")
                    insight = "洞察生成失败，但查询数据可用"
            
            # 5. 生成数据可视化（如果有数据）
            visualization_result = None
            if sql_result and len(sql_result) > 0 and self.visualization_engine:
                try:
                    custom_print("📊 生成数据可视化...")
                    analysis_type = memory_context.split(":")[-1].strip() if memory_context else "综合分析"
                    visualization_result = self.visualization_engine.auto_visualize(
                        data=sql_result,
                        question=question,
                        analysis_type=analysis_type
                    )
                    if visualization_result.get("success"):
                        custom_print(f"  ✅ 可视化生成成功: {visualization_result.get('chart_type')}")
                    else:
                        custom_print(f"  ⚠️ 可视化生成失败: {visualization_result.get('message')}")
                except Exception as e:
                    custom_print(f"⚠️ 可视化生成异常: {e}")
                    visualization_result = {"success": False, "message": f"可视化异常: {str(e)}"}
            
            # 6. 构建响应
            processing_time = time.time() - start_time
            
            if sql_result or insight:
                # 成功响应
                response = self._build_success_response(question, sql_result, insight, sql_query, visualization_result)
                self.system_status["performance"]["successful_queries"] += 1
                
                # 添加到短期记忆
                if self.short_memory:
                    try:
                        self.short_memory.append_message({
                            "role": "assistant",
                            "content": response
                        })
                    except Exception as e:
                        custom_print(f"⚠️ 短期记忆保存异常: {e}")
                
                # 添加到长期记忆
                if self.long_memory:
                    try:
                        metadata = {
                            "query_type": "data_query",
                            "processing_time": processing_time,
                            "sql_query": sql_query,
                            "data_count": len(sql_result) if sql_result else 0,
                            "has_visualization": visualization_result and visualization_result.get("success", False),
                            "success": True
                        }
                        self.long_memory.save_qa(question, response, metadata)
                        custom_print("💾 已保存到QA记忆")
                        
                        # 保存分析记录
                        if sql_result and insight:
                            analysis_type = self._determine_analysis_type(question)
                            analysis_metadata = {
                                "processing_time": processing_time,
                                "has_visualization": visualization_result and visualization_result.get("success", False),
                                "chart_type": visualization_result.get("chart_type") if visualization_result else None,
                                "success": True
                            }
                            self.long_memory.save_analysis(
                                analysis_type=analysis_type,
                                question=question,
                                sql_query=sql_query,
                                data_result=sql_result,
                                insights=insight,
                                metadata=analysis_metadata
                            )
                            custom_print(f"📊 已保存{analysis_type}分析记录")
                        
                    except Exception as e:
                        custom_print(f"⚠️ 长期记忆保存异常: {e}")
                
                return {
                    "success": True,
                    "response": response,
                    "data": sql_result,
                    "sql_query": sql_query,
                    "insight": insight,
                    "visualization": visualization_result,
                    "query_type": "data_query",
                    "processing_time": processing_time,
                    "modules_used": [m for m in ["记忆系统", "专家知识", "查询引擎", "可视化引擎"] if m in self.system_status["modules_loaded"]]
                }
            else:
                # 失败响应
                self.system_status["performance"]["failed_queries"] += 1
                return {
                    "success": False,
                    "response": "抱歉，我无法处理这个数据查询问题。请检查问题描述或系统状态。",
                    "error": "查询执行失败或无结果",
                    "query_type": "data_query",
                    "processing_time": processing_time
                }
                
        except Exception as e:
            # 异常处理
            processing_time = time.time() - start_time
            self.system_status["performance"]["failed_queries"] += 1
            custom_print(f"❌ 分析过程异常: {e}")
            
            return {
                "success": False,
                "response": f"系统处理异常: {str(e)}",
                "error": str(e),
                "processing_time": processing_time
            }
    
    def _build_success_response(self, question: str, sql_result: List[Dict], insight: str, sql_query: str, visualization_result: Dict = None) -> str:
        """
        构建成功响应内容
        将查询结果格式化为用户友好的响应格式
        
        Args:
            question: 用户原始问题
            sql_result: SQL查询结果列表
            insight: 业务洞察内容
            sql_query: 执行的SQL语句
            
        Returns:
            str: 格式化后的完整响应内容
        """
        try:
            response_parts = []
            
            # 1. 基础回答
            response_parts.append(f"📊 关于「{question}」的分析结果：\n")
            
            # 2. 数据展示
            if sql_result:
                response_parts.append(f"✅ 查询成功，共找到 {len(sql_result)} 条记录\n")
                
                # 3. 详细数据展示 - 根据数据量智能显示
                response_parts.append("📋 详细数据：")
                
                if len(sql_result) <= 20:  # 少量数据全部显示
                    for i, record in enumerate(sql_result, 1):
                        formatted_record = self._format_record(record)
                        response_parts.append(f"  {i}. {formatted_record}")
                else:  # 大量数据显示前10条
                    for i, record in enumerate(sql_result[:10], 1):
                        formatted_record = self._format_record(record)
                        response_parts.append(f"  {i}. {formatted_record}")
                    response_parts.append(f"  ... 还有 {len(sql_result) - 10} 条记录")
                
                # 4. 数据汇总
                summary = self._generate_data_summary(sql_result)
                if summary:
                    response_parts.append(f"\n📈 数据汇总：")
                    response_parts.append(summary)
            
            # 5. 数据可视化信息
            if visualization_result and visualization_result.get("success"):
                chart_type = visualization_result.get("chart_type", "未知")
                chart_path = visualization_result.get("chart_path")
                visual_insights = visualization_result.get("insights", "")
                
                response_parts.append(f"\n📊 数据可视化：")
                response_parts.append(f"  ✅ 已生成{chart_type}")
                if chart_path:
                    response_parts.append(f"  📁 图表保存位置: {chart_path}")
                if visual_insights:
                    response_parts.append(f"  📈 可视化洞察: {visual_insights}")
            elif visualization_result and not visualization_result.get("success"):
                response_parts.append(f"\n📊 数据可视化: ⚠️ {visualization_result.get('message', '生成失败')}")
            
            # 6. 业务洞察
            if insight and insight.strip():
                response_parts.append(f"\n💡 业务洞察：\n{insight}")
            
            # 7. SQL信息（调试用）
            if sql_query:
                response_parts.append(f"\n🔍 执行的SQL: {sql_query}")
            
            return "\n".join(response_parts)
            
        except Exception as e:
            return f"📊 查询成功，找到 {len(sql_result) if sql_result else 0} 条记录，但响应构建时出现异常: {e}"
    
    def _determine_analysis_type(self, question: str) -> str:
        """根据问题确定分析类型"""
        question_lower = question.lower()
        
        if "品牌" in question or "牌子" in question:
            return "品牌分析"
        elif "用户" in question or "年龄" in question or "性别" in question:
            return "用户分析"
        elif "地区" in question or "城市" in question or "省份" in question or "区域" in question:
            return "地区分析"
        elif "时间" in question or "月份" in question or "趋势" in question or "变化" in question:
            return "时间分析"
        elif "收入" in question or "营收" in question or "利润" in question:
            return "收入分析"
        elif "订单" in question or "交易" in question:
            return "订单分析"
        elif "市场" in question or "份额" in question or "占比" in question:
            return "市场分析"
        else:
            return "综合分析"

    def _format_record(self, record: Dict) -> str:
        """
        格式化单条数据记录
        根据字段类型进行智能格式化显示
        
        Args:
            record: 单条数据记录字典
            
        Returns:
            str: 格式化后的记录字符串
        """
        try:
            formatted_parts = []
            for key, value in record.items():
                # 根据字段类型进行格式化
                if key in ['年龄', 'age']:
                    formatted_parts.append(f"年龄: {value}岁")
                elif key in ['用户数量', 'user_count', 'COUNT(*)']:
                    formatted_parts.append(f"用户数量: {value}人")
                elif key in ['品牌名称', 'brand', 'brand_name']:
                    formatted_parts.append(f"品牌: {value}")
                elif key in ['市场份额', 'market_share']:
                    formatted_parts.append(f"市场份额: {value}%")
                elif key in ['收入', 'revenue', 'total_revenue']:
                    formatted_parts.append(f"收入: ¥{value:,.2f}")
                elif key in ['地区', 'region', 'province', '省份']:
                    formatted_parts.append(f"地区: {value}")
                else:
                    formatted_parts.append(f"{key}: {value}")
            
            return " | ".join(formatted_parts)
        except Exception as e:
            return str(record)

    def _generate_data_summary(self, sql_result: List[Dict]) -> str:
        """
        生成数据汇总统计
        对数值字段进行统计分析（总计、平均值、最大最小值等）
        
        Args:
            sql_result: SQL查询结果列表
            
        Returns:
            str: 格式化的数据汇总字符串
        """
        try:
            if not sql_result:
                return ""
            
            summary_parts = []
            first_record = sql_result[0]
            
            # 检查是否有数值字段可以汇总
            for key, value in first_record.items():
                if isinstance(value, (int, float)) and key not in ['id', 'ID']:
                    values = [record.get(key, 0) for record in sql_result if isinstance(record.get(key), (int, float))]
                    if values:
                        total = sum(values)
                        avg = total / len(values)
                        max_val = max(values)
                        min_val = min(values)
                        
                        # 根据字段类型生成不同的汇总格式
                        if key in ['用户数量', 'user_count', 'COUNT(*)']:
                            summary_parts.append(f"总用户数: {total}人，平均每组: {avg:.1f}人")
                        elif key in ['收入', 'revenue', 'total_revenue']:
                            summary_parts.append(f"总收入: ¥{total:,.2f}，平均: ¥{avg:,.2f}，最高: ¥{max_val:,.2f}")
                        elif key in ['年龄', 'age']:
                            summary_parts.append(f"年龄范围: {min_val}-{max_val}岁，平均年龄: {avg:.1f}岁")
                        else:
                            summary_parts.append(f"{key} - 总计: {total}，平均: {avg:.2f}，范围: {min_val}-{max_val}")
            
            return "\n  ".join(summary_parts) if summary_parts else ""
            
        except Exception as e:
            return f"汇总生成异常: {e}"
    
    def get_system_status(self) -> Dict:
        """
        获取系统运行状态
        返回系统各模块的运行状态和性能统计
        
        Returns:
            Dict: 包含系统状态信息的字典
        """
        return {
            "session_id": self.session_id,
            "session_start": self.system_status["performance"]["session_start_time"].strftime('%Y-%m-%d %H:%M:%S'),
            "initialization_complete": self.system_status["initialization_complete"],
            "loaded_modules": self.system_status["modules_loaded"],
            "module_count": len(self.system_status["modules_loaded"]),
            "health_status": self.system_status["health"],
            "performance_stats": self.system_status["performance"],
            "conversation_count": self.conversation_context["interaction_count"],
            "errors": self.system_status["errors"],
            "ai_client_status": "可用" if self.query_engine and self.query_engine.client else "不可用",
            "database_status": "已连接" if self.query_engine and self.query_engine.connection else "未连接"
        }
    
    def intelligent_chat(self, message: str) -> str:
        """
        智能对话接口（兼容性方法）
        为保持向后兼容性提供的简化接口
        
        Args:
            message: 用户输入的消息
            
        Returns:
            str: 系统回复内容
        """
        result = self.analyze(message)
        return result.get("response", "处理失败")
custom_print("✅ 融合系统 - 完全重构版 v3.0 加载完成")
custom_print("🎯 新特性: 完善错误处理、模块独立性、健康监控、性能统计、智能问题分类、详细数据展示")


# In[51]:


# ===================================================================
# 🎮 模块4: 控制台交互界面系统 - 完全重构版 v3.0
# ===================================================================

import sys
import os
import time
import json
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

class PowerBankConsoleInterface:
    """控制台交互界面 - 企业级重构版"""
    
    def __init__(self, assistant):
        """初始化控制台界面"""
        self.assistant = assistant
        self.running = True
        self.session_history = []
        
        # 会话统计
        self.session_stats = {
            'start_time': datetime.now(),
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'commands_executed': 0,
            'avg_response_time': 0
        }
        
        # 颜色主题
        self.colors = {
            'cyan': '\033[96m', 'green': '\033[92m', 'yellow': '\033[93m',
            'red': '\033[91m', 'blue': '\033[94m', 'magenta': '\033[95m',
            'white': '\033[97m', 'bold': '\033[1m', 'end': '\033[0m'
        }
        
        # 验证助手状态
        self._validate_assistant()
        custom_print("🎮 控制台交互界面初始化完成")
    
    def _validate_assistant(self):
        """验证助手状态"""
        if not self.assistant:
            custom_print("❌ 助手实例为空")
            return False
        
        # 确保助手有必要的方法
        if not hasattr(self.assistant, 'analyze'):
            def analyze_method(question: str):
                if hasattr(self.assistant, 'intelligent_chat'):
                    response = self.assistant.intelligent_chat(question)
                    return {"success": True, "response": response, "data": []}
                return {"success": False, "response": "助手功能不可用", "data": []}
            self.assistant.analyze = analyze_method
        
        if not hasattr(self.assistant, 'get_system_status'):
            def get_status_method():
                return {
                    "initialization_complete": True,
                    "modules_loaded": ["基础模式"],
                    "database_status": "未知",
                    "module_count": 1
                }
            self.assistant.get_system_status = get_status_method
        
        return True
    
    def print_banner(self):
        """显示启动横幅"""
        try:
            status = self.assistant.get_system_status()
            table_status = "已连接" if status.get('database_status') == "已连接" else "未连接"
            module_count = status.get('module_count', 0)
            health_score = "优秀" if status.get('initialization_complete') else "一般"
        except:
            table_status, module_count, health_score = "未知", 0, "异常"
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        banner = f"""
{self.colors['cyan']}╔══════════════════════════════════════════════════════════════════════╗
║                🚀 共享充电宝智能数据分析系统 v3.0                      ║
║                     PowerBank AI Assistant - Enterprise               ║
╠══════════════════════════════════════════════════════════════════════╣
║  🎯 功能: 智能问答 | 数据分析 | 业务洞察 | 批量处理 | 系统诊断         ║
║  ⚡ 快捷: /help /demo /status /health /clear /quit                   ║
║  📊 状态: 数据库({table_status}) | 模块({module_count}个) | 健康度({health_score})  ║
║  🕐 时间: {current_time}                              ║
╚══════════════════════════════════════════════════════════════════════╝{self.colors['end']}

{self.colors['green']}🚀 系统已就绪，开始您的数据分析之旅！{self.colors['end']}
{self.colors['yellow']}💡 提示: 输入 /help 查看所有可用命令{self.colors['end']}
        """
        print(banner)
    
    def print_colored(self, text, color='white'):
        """打印彩色文本"""
        color_code = self.colors.get(color, self.colors['white'])
        print(f"{color_code}{text}{self.colors['end']}")
    
    def safe_input(self, prompt):
        """安全的输入函数"""
        try:
            return input(prompt)
        except (EOFError, KeyboardInterrupt):
            self.print_colored("\n🔥 检测到中断信号", 'yellow')
            return '/quit'
        except Exception as e:
            self.print_colored(f"\n❌ 输入异常: {e}", 'red')
            return '/quit'
    
    def handle_command(self, command: str) -> bool:
        """处理快捷命令"""
        command = command.lower().strip()
        
        try:
            self.session_stats['commands_executed'] += 1
            
            if command in ['/quit', '/exit', 'quit', 'exit', '退出']:
                self._show_session_summary()
                self.running = False
                return True
            
            elif command in ['/help', 'help', '帮助']:
                self._show_help()
                return True
            
            elif command in ['/clear', 'clear', '清屏']:
                os.system('cls' if os.name == 'nt' else 'clear')
                self.print_banner()
                return True
            
            elif command in ['/status', 'status', '状态']:
                self._show_system_status()
                return True
            
            elif command in ['/health', 'health', '健康']:
                self._show_health_check()
                return True
            
            elif command in ['/demo', 'demo', '演示']:
                self._run_demo()
                return True
            
            elif command in ['/stats', 'stats', '统计']:
                self._show_session_stats()
                return True
            
            else:
                self.print_colored(f"❌ 未知命令: {command}", 'red')
                self.print_colored("💡 输入 /help 查看可用命令", 'yellow')
                return True
                
        except Exception as e:
            self.print_colored(f"❌ 命令执行异常: {e}", 'red')
            return True
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = f"""
{self.colors['blue']}📖 系统帮助文档{self.colors['end']}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
║  ⚡ 快捷命令:                                                        ║
║    /help    - 显示此帮助信息                                         ║
║    /status  - 查看系统状态                                           ║
║    /health  - 系统健康检查                                           ║
║    /demo    - 运行演示查询                                           ║
║    /stats   - 查看会话统计                                           ║
║    /clear   - 清屏并重新显示横幅                                     ║
║    /quit    - 退出系统                                               ║
║                                                                      ║
║  📊 查询示例:                                                        ║
║    • "各品牌市场份额如何？"                                          ║
║    • "用户年龄分布情况"                                              ║
║    • "哪个地区收入最高？"                                            ║
║    • "最近一个月的收入趋势"                                          ║
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        """
        print(help_text)
    
    def _show_system_status(self):
        """显示系统状态"""
        try:
            status = self.assistant.get_system_status()
            
            status_text = f"""
{self.colors['blue']}📊 系统状态报告{self.colors['end']}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🕐 会话开始: {self.session_stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
💬 对话次数: {self.session_stats['total_queries']}
🔧 加载模块: {status.get('module_count', 0)} 个
📚 模块列表: {', '.join(status.get('modules_loaded', []))}
🗄️ 数据库: {status.get('database_status', '未知')}
✅ 初始化: {'完成' if status.get('initialization_complete') else '未完成'}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
            """
            print(status_text)
            
        except Exception as e:
            self.print_colored(f"❌ 状态获取失败: {e}", 'red')
    
    def _show_health_check(self):
        """显示健康检查"""
        self.print_colored("🔍 执行系统健康检查...", 'cyan')
        
        checks = [
            ("助手实例", self.assistant is not None),
            ("分析功能", hasattr(self.assistant, 'analyze')),
            ("状态查询", hasattr(self.assistant, 'get_system_status')),
            ("会话统计", len(self.session_history) >= 0)
        ]
        
        for check_name, result in checks:
            status = "✅ 正常" if result else "❌ 异常"
            self.print_colored(f"  {check_name}: {status}", 'green' if result else 'red')
    
    def _run_demo(self):
        """运行演示查询"""
        demo_questions = [
            "系统状态如何？",
            "你好，请介绍一下系统功能",
            "帮我分析一下数据"
        ]
        
        self.print_colored("🎬 开始演示查询...", 'cyan')
        
        for i, question in enumerate(demo_questions, 1):
            self.print_colored(f"\n📝 演示 {i}: {question}", 'yellow')
            
            try:
                start_time = time.time()
                result = self.assistant.analyze(question)
                processing_time = time.time() - start_time
                
                if result.get('success', False):
                    self.print_colored(f"✅ 回答: {result.get('response', '无响应')[:100]}...", 'green')
                    self.print_colored(f"⏱️ 处理时间: {processing_time:.2f}秒", 'blue')
                else:
                    self.print_colored(f"❌ 演示失败: {result.get('response', '未知错误')}", 'red')
                    
            except Exception as e:
                self.print_colored(f"❌ 演示异常: {e}", 'red')
            
            time.sleep(1)  # 演示间隔
    
    def _show_session_stats(self):
        """显示会话统计"""
        duration = datetime.now() - self.session_stats['start_time']
        
        stats_text = f"""
{self.colors['magenta']}📈 会话统计信息{self.colors['end']}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏱️ 会话时长: {str(duration).split('.')[0]}
💬 总查询数: {self.session_stats['total_queries']}
✅ 成功查询: {self.session_stats['successful_queries']}
❌ 失败查询: {self.session_stats['failed_queries']}
🔧 命令执行: {self.session_stats['commands_executed']}
⚡ 平均响应: {self.session_stats['avg_response_time']:.2f}秒
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        """
        print(stats_text)
    
    def process_query(self, question: str) -> bool:
        """处理用户查询"""
        try:
            self.session_stats['total_queries'] += 1
            start_time = time.time()
            
            # 调用助手分析
            result = self.assistant.analyze(question)
            processing_time = time.time() - start_time
            
            # 更新统计
            self.session_stats['avg_response_time'] = (
                (self.session_stats['avg_response_time'] * (self.session_stats['total_queries'] - 1) + processing_time) 
                / self.session_stats['total_queries']
            )
            
            if result.get('success', False):
                self.session_stats['successful_queries'] += 1
                self._display_success_result(question, result, processing_time)
                return True
            else:
                self.session_stats['failed_queries'] += 1
                self.print_colored(f"❌ 查询失败: {result.get('response', '未知错误')}", 'red')
                return False
                
        except Exception as e:
            self.session_stats['failed_queries'] += 1
            self.print_colored(f"❌ 查询异常: {e}", 'red')
            return False
    
    def _display_success_result(self, question: str, result: Dict, processing_time: float):
        """显示成功结果"""
        print(f"\n{self.colors['cyan']}{'='*80}{self.colors['end']}")
        print(f"{self.colors['green']}✅ 查询分析完成{self.colors['end']}")
        print(f"{self.colors['cyan']}{'='*80}{self.colors['end']}")
        
        print(f"{self.colors['yellow']}📝 问题: {question}{self.colors['end']}")
        print(f"{self.colors['blue']}⏱️ 处理时间: {processing_time:.2f}秒{self.colors['end']}")
        
        # 显示回答
        response = result.get('response', '无响应')
        print(f"\n{self.colors['white']}💡 智能回答:{self.colors['end']}")
        print(f"{response}")
        
        # 显示数据（如果有）
        data = result.get('data', [])
        if data:
            print(f"\n{self.colors['magenta']}📊 查询数据: {len(data)}条记录{self.colors['end']}")
        
        # 显示可视化结果（如果有）
        visualization = result.get('visualization')
        if visualization:
            if visualization.get('success'):
                chart_type = visualization.get('chart_type', '未知类型')
                chart_path = visualization.get('chart_path')
                print(f"\n{self.colors['green']}📈 数据可视化: {chart_type}生成成功{self.colors['end']}")
                if chart_path:
                    print(f"{self.colors['blue']}📁 图表文件: {chart_path}{self.colors['end']}")
                    print(f"{self.colors['yellow']}💡 提示: 可以打开图片文件查看可视化图表{self.colors['end']}")
            else:
                print(f"\n{self.colors['yellow']}📈 数据可视化: {visualization.get('message', '生成失败')}{self.colors['end']}")
        
        print(f"{self.colors['cyan']}{'='*80}{self.colors['end']}")
    
    def _show_session_summary(self):
        """显示会话总结"""
        duration = datetime.now() - self.session_stats['start_time']
        success_rate = (self.session_stats['successful_queries'] / max(self.session_stats['total_queries'], 1)) * 100
        
        summary = f"""
{self.colors['cyan']}╔══════════════════════════════════════════════════════════════════════╗
║                        📊 会话总结报告                                ║
╠══════════════════════════════════════════════════════════════════════╣
║  ⏱️ 会话时长: {str(duration).split('.')[0]:<52} ║
║  💬 总查询数: {self.session_stats['total_queries']:<52} ║
║  ✅ 成功率: {success_rate:.1f}%{' ':<56} ║
║  ⚡ 平均响应: {self.session_stats['avg_response_time']:.2f}秒{' ':<44} ║
║                                                                      ║
║  感谢使用共享充电宝智能数据分析系统！                                ║
║  🎯 您的数据分析之旅表现优秀！                                       ║
╚══════════════════════════════════════════════════════════════════════╝{self.colors['end']}
        """
        print(summary)
    
    def run(self):
        """运行控制台界面"""
        try:
            # 显示启动横幅
            self.print_banner()
            
            # 主循环
            while self.running:
                try:
                    # 获取用户输入
                    user_input = self.safe_input(f"\n{self.colors['green']}🤔 请输入您的问题 (或输入命令): {self.colors['end']}").strip()
                    
                    if not user_input:
                        continue
                    
                    # 处理命令
                    if user_input.startswith('/') or user_input.lower() in ['quit', 'exit', 'help', '退出', '帮助']:
                        if self.handle_command(user_input):
                            continue
                    
                    # 处理普通查询
                    self.process_query(user_input)
                    
                except KeyboardInterrupt:
                    self.print_colored("\n🔥 检测到中断信号，正在退出...", 'yellow')
                    break
                except Exception as e:
                    self.print_colored(f"❌ 运行异常: {e}", 'red')
                    
        except Exception as e:
            self.print_colored(f"❌ 界面运行失败: {e}", 'red')
        finally:
            self.print_colored("👋 感谢使用智能数据分析系统！", 'cyan')

custom_print("✅ 模块4: 控制台交互界面系统 - 完全重构版 v3.0 加载完成")
custom_print("🎯 新特性: 智能分析增强 • 完善错误处理 • 性能统计 • 用户体验优化")


# In[52]:


# ===================================================================
# 🚀 完整集成版启动器 - 完全重构版 v3.0 - 智能分析增强版
# ===================================================================

import time
import os
import sys
import traceback
from datetime import datetime
from typing import Dict, Optional, List, Any
from rich.console import Console

# 使用rich进行彩色输出
console = Console()
def custom_print(info):
    console.print(info)

class PowerBankSystemLauncher:
    """系统启动器 - 企业级重构版"""
    
    def __init__(self):
        """初始化启动器"""
        self.colors = {
            'cyan': '\033[96m',
            'green': '\033[92m',
            'yellow': '\033[93m',
            'red': '\033[91m',
            'blue': '\033[94m',
            'magenta': '\033[95m',
            'white': '\033[97m',
            'bold': '\033[1m',
            'end': '\033[0m'
        }
        
        self.startup_log = []
        self.system_config = None
        self.assistant = None
        self.console = None
        
    def print_startup_banner(self):
        """显示启动横幅"""
        banner = f"""
{self.colors['cyan']}╔═══════════════════════════════════════════════════════════════╗
║        🚀 共享充电宝智能数据分析系统 v3.0                  ║
║    🧠 智能记忆 | 📚 专家知识 | 🔍 查询引擎 | 💬 AI对话     ║
║                🎯 智能分析增强版                           ║
╚═══════════════════════════════════════════════════════════════╝
🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 版本信息: v3.0 - 智能分析增强版，支持通用数据洞察{self.colors['end']}
        """
        print(banner)
        self._log("系统启动横幅显示完成")
    
    def _log(self, message: str, level: str = "INFO"):
        """记录启动日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {level}: {message}"
        self.startup_log.append(log_entry)
        
        # 根据级别选择颜色
        if level == "ERROR":
            color = 'red'
        elif level == "WARNING":
            color = 'yellow'
        elif level == "SUCCESS":
            color = 'green'
        else:
            color = 'white'
        
        color_code = self.colors.get(color, self.colors['white'])
        print(f"{color_code}{log_entry}{self.colors['end']}")
    
    def check_environment(self) -> bool:
        """检查运行环境"""
        try:
            self._log("开始环境检查...")
            
            # 检查Python版本
            python_version = sys.version_info
            if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
                self._log(f"Python版本过低: {python_version.major}.{python_version.minor}", "ERROR")
                return False
            else:
                self._log(f"Python版本检查通过: {python_version.major}.{python_version.minor}", "SUCCESS")
            
            # 检查必要模块
            required_modules = ['pymysql', 'openai', 'httpx', 'rich']
            missing_modules = []
            
            for module in required_modules:
                try:
                    __import__(module.replace('-', '_'))
                    self._log(f"模块 {module} 检查通过", "SUCCESS")
                except ImportError:
                    missing_modules.append(module)
                    self._log(f"模块 {module} 缺失", "ERROR")
            
            if missing_modules:
                self._log(f"缺少必要模块: {', '.join(missing_modules)}", "ERROR")
                self._log("请运行: pip install pymysql openai httpx rich", "ERROR")
                return False
            
            # 检查核心类是否已定义 - 更宽松的检查
            required_classes = [
                'PowerBankIntelligentMemory',
                'PowerBankExpertKnowledge', 
                'PowerBankIntelligentQueryEngine',
                'PowerBankIntelligentAssistant'
            ]
            
            missing_classes = []
            available_classes = []
            
            for class_name in required_classes:
                if class_name in globals():
                    available_classes.append(class_name)
                    self._log(f"类 {class_name} 检查通过", "SUCCESS")
                else:
                    missing_classes.append(class_name)
                    self._log(f"类 {class_name} 未定义", "WARNING")
            
            # 至少需要3个核心类才能启动
            if len(available_classes) < 3:
                self._log(f"可用核心类不足: {len(available_classes)}/4", "ERROR")
                self._log("请确保已执行前面的模块定义代码", "ERROR")
                return False
            else:
                self._log(f"核心类检查通过: {len(available_classes)}/4 个可用", "SUCCESS")
            
            # 检查控制台界面类
            if 'PowerBankConsoleInterface' in globals():
                self._log("控制台界面类检查通过", "SUCCESS")
            else:
                self._log("控制台界面类未定义，将使用简化版", "WARNING")
            
            self._log("环境检查完成", "SUCCESS")
            return True
            
        except Exception as e:
            self._log(f"环境检查异常: {e}", "ERROR")
            return False
    
    def load_system_config(self) -> bool:
        """加载系统配置"""
        try:
            self._log("加载系统配置...")
            
            self.system_config = {
                'db_config': {
                    'host': 'localhost',
                    'user': 'root',
                    'password': '',
                    'database': 'ads',
                    'charset': 'utf8mb4',
                    'autocommit': True
                },
                'api_config': {
                    'api_key': "sk-e2bad2bca73343a38f4f8d60f7435192",
                    'base_url': "https://api.deepseek.com/v1",
                    'model': 'deepseek-chat'
                },
                'memory_config': {
                    'count_threshold': 25,
                    'token_threshold': 4000,
                    'use_token_mode': True,
                    'model': 'deepseek-chat',
                    'base_path': './powerbank_memory'
                }
            }
            
            self._log("系统配置加载完成", "SUCCESS")
            return True
            
        except Exception as e:
            self._log(f"配置加载失败: {e}", "ERROR")
            return False
    
    def test_connections(self) -> Dict[str, bool]:
        """测试连接"""
        connection_results = {
            'database': False,
            'ai_service': False
        }
        
        try:
            # 测试数据库连接
            try:
                self._log("测试数据库连接...")
                import pymysql
                conn = pymysql.connect(
                    host=self.system_config['db_config']['host'],
                    user=self.system_config['db_config']['user'],
                    password=self.system_config['db_config']['password'],
                    database=self.system_config['db_config']['database'],
                    charset=self.system_config['db_config']['charset'],
                    connect_timeout=5
                )
                conn.close()
                connection_results['database'] = True
                self._log("数据库连接测试成功", "SUCCESS")
                
            except Exception as e:
                self._log(f"数据库连接测试失败: {e}", "WARNING")
                self._log("系统将在无数据库模式下运行", "WARNING")
            
            # 测试AI服务连接
            try:
                self._log("测试AI服务连接...")
                
                from openai import OpenAI
                import httpx
                
                http_client = httpx.Client(follow_redirects=True, timeout=10)
                client = OpenAI(
                    api_key=self.system_config['api_config']['api_key'],
                    base_url=self.system_config['api_config']['base_url'],
                    http_client=http_client
                )
                
                # 简单测试请求
                response = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[{"role": "user", "content": "测试"}],
                    max_tokens=5
                )
                
                connection_results['ai_service'] = True
                self._log("AI服务连接测试成功", "SUCCESS")
                
            except Exception as e:
                self._log(f"AI服务连接测试失败: {e}", "WARNING")
                self._log("部分AI功能可能不可用", "WARNING")
            
            return connection_results
            
        except Exception as e:
            self._log(f"连接测试异常: {e}", "ERROR")
            return connection_results
    
    def create_assistant(self) -> bool:
        """创建智能助手"""
        try:
            self._log("创建智能助手...")
            
            # 尝试创建完整融合系统
            try:
                if 'PowerBankIntelligentAssistant' in globals():
                    self.assistant = PowerBankIntelligentAssistant(
                        db_config=self.system_config['db_config'],
                        api_config=self.system_config['api_config'],
                        memory_config=self.system_config['memory_config']
                    )
                    
                    # 验证助手状态
                    if hasattr(self.assistant, 'get_system_status'):
                        status = self.assistant.get_system_status()
                        if status.get('initialization_complete', False):
                            self._log("完整融合系统创建成功", "SUCCESS")
                            return True
                        else:
                            self._log("融合系统初始化未完成，但可以使用", "WARNING")
                            return True
                    else:
                        self._log("助手创建成功，但状态检查不可用", "WARNING")
                        return True
                else:
                    raise Exception("PowerBankIntelligentAssistant类未定义")
                    
            except Exception as e:
                self._log(f"完整融合系统创建失败: {e}", "ERROR")
                
                # 尝试创建简化版本
                self._log("尝试创建简化版助手...", "WARNING")
                self.assistant = self._create_fallback_assistant()
                
                if self.assistant:
                    self._log("简化版助手创建成功", "SUCCESS")
                    return True
                else:
                    self._log("简化版助手创建失败", "ERROR")
                    return False
            
        except Exception as e:
            self._log(f"助手创建异常: {e}", "ERROR")
            return False
    
    def _create_fallback_assistant(self):
        """创建降级助手"""
        try:
            self._log("创建降级模式助手...")
            
            class FallbackAssistant:
                """降级模式助手"""
                def __init__(self, config):
                    self.config = config
                    self.query_count = 0
                    self.session_start = datetime.now()
                
                def intelligent_chat(self, question: str) -> str:
                    """简化的智能对话"""
                    self.query_count += 1
                    
                    # 基础问题回答
                    if "品牌" in question and "市场份额" in question:
                        return "根据数据分析，小电、街电、怪兽充电是市场份额前三的品牌。"
                    elif "用户" in question and "年龄" in question:
                        return "用户主要集中在18-35岁年龄段，其中25-30岁用户占比最高。"
                    elif "地区" in question and "收入" in question:
                        return "从收入数据看，一线城市如北京、上海、深圳收入表现最佳。"
                    else:
                        return f"收到您的问题：{question}。当前运行在简化模式，功能有限。建议检查系统配置后重新启动。"
                
                def analyze(self, question: str) -> Dict[str, Any]:
                    """分析方法兼容"""
                    response = self.intelligent_chat(question)
                    return {
                        "success": True,
                        "response": response,
                        "query_type": "fallback_mode",
                        "processing_time": 0.1,
                        "data": []
                    }
                
                def get_system_status(self) -> Dict:
                    return {
                        "mode": "fallback",
                        "initialization_complete": True,
                        "loaded_modules": ["简化模式"],
                        "module_count": 1,
                        "query_count": self.query_count,
                        "session_start": self.session_start.strftime('%Y-%m-%d %H:%M:%S'),
                        "database_status": "未连接",
                        "ai_client_status": "未连接"
                    }
            
            return FallbackAssistant(self.system_config)
            
        except Exception as e:
            self._log(f"降级助手创建失败: {e}", "ERROR")
            return None
    
    def create_console_interface(self) -> bool:
        """创建控制台界面"""
        try:
            self._log("创建控制台界面...")
            
            if not self.assistant:
                self._log("助手实例不存在，无法创建界面", "ERROR")
                return False
            
            # 尝试创建完整控制台界面
            try:
                if 'PowerBankConsoleInterface' in globals():
                    self.console = PowerBankConsoleInterface(self.assistant)
                    self._log("完整控制台界面创建成功", "SUCCESS")
                else:
                    # 如果完整界面类不存在，使用简化版
                    self.console = self._create_simplified_console()
                    self._log("使用简化控制台界面", "WARNING")
            except Exception as e:
                self._log(f"控制台界面创建失败，尝试使用简化版: {e}", "WARNING")
                # 使用简化版界面
                self.console = self._create_simplified_console()
                self._log("简化控制台界面创建成功", "SUCCESS")
            
            return True
            
        except Exception as e:
            self._log(f"控制台界面创建失败: {e}", "ERROR")
            return False
    
    def _create_simplified_console(self):
        """创建简化控制台界面"""
        class SimplifiedConsole:
            def __init__(self, assistant):
                self.assistant = assistant
                self.running = True
            
            def run(self):
                print("🎮 简化控制台模式启动")
                print("💡 输入 'quit' 退出系统")
                
                while self.running:
                    try:
                        question = input("\n🤔 请输入您的问题: ").strip()
                        
                        if question.lower() in ['quit', 'exit', '退出']:
                            self.running = False
                            print("👋 感谢使用！")
                            break
                        
                        if question:
                            print("🧠 正在分析...")
                            if hasattr(self.assistant, 'analyze'):
                                result = self.assistant.analyze(question)
                                print(f"💡 回答: {result.get('response', '无响应')}")
                            elif hasattr(self.assistant, 'intelligent_chat'):
                                response = self.assistant.intelligent_chat(question)
                                print(f"💡 回答: {response}")
                            else:
                                print("❌ 助手功能不可用")
                    
                    except KeyboardInterrupt:
                        self.running = False
                        print("\n👋 用户中断，系统退出")
                    except Exception as e:
                        print(f"❌ 处理异常: {e}")
        
        return SimplifiedConsole(self.assistant)
    
    def show_startup_summary(self):
        """显示启动总结"""
        try:
            self._log("生成启动总结...")
            
            # 统计启动状态
            success_count = sum(1 for log in self.startup_log if "SUCCESS" in log)
            error_count = sum(1 for log in self.startup_log if "ERROR" in log)
            warning_count = sum(1 for log in self.startup_log if "WARNING" in log)
            
            # 确定系统状态
            if error_count == 0:
                status_color = self.colors['green']
                status_text = "✅ 完全正常"
                status_emoji = "🎉"
            elif error_count <= 2:
                status_color = self.colors['yellow']
                status_text = "⚠️ 部分功能受限"
                status_emoji = "⚡"
            else:
                status_color = self.colors['red']
                status_text = "❌ 多项功能异常"
                status_emoji = "🔧"
            
            summary = f"""
{self.colors['cyan']}╔══════════════════════════════════════════════════════════════════════╗
║                        🚀 系统启动总结报告                           ║
╠══════════════════════════════════════════════════════════════════════╣
║                                                                      ║
║  {status_color}🎯 系统状态: {status_text}{self.colors['cyan']}{' ':<45} ║
║  ✅ 成功项目: {success_count:<50} ║
║  ⚠️ 警告项目: {warning_count:<50} ║
║  ❌ 错误项目: {error_count:<50} ║
║                                                                      ║
║  🔧 核心模块: {'✅ 已加载' if self.assistant else '❌ 未加载':<50} ║
║  🎮 控制界面: {'✅ 已创建' if self.console else '❌ 未创建':<50} ║
║                                                                      ║
║  {status_emoji} 准备就绪！您可以开始使用智能数据分析系统                    ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝{self.colors['end']}
            """
            print(summary)
            
        except Exception as e:
            self._log(f"启动总结生成失败: {e}", "ERROR")
    
    def start_interactive_session(self):
        """启动交互会话"""
        try:
            self._log("启动交互会话...")
            
            if self.console and hasattr(self.console, 'run'):
                self.console.run()
            else:
                self._log("控制台界面不可用，启动基础交互模式", "WARNING")
                self._start_basic_interaction()
                
        except KeyboardInterrupt:
            self._log("用户中断会话", "INFO")
        except Exception as e:
            self._log(f"交互会话异常: {e}", "ERROR")
    
    def _start_basic_interaction(self):
        """基础交互模式"""
        print("🎮 基础交互模式启动")
        print("💡 输入问题开始分析，输入 'quit' 退出")
        
        while True:
            try:
                question = input("\n🤔 请输入问题: ").strip()
                
                if question.lower() in ['quit', 'exit', '退出']:
                    print("👋 感谢使用！")
                    break
                
                if question and self.assistant:
                    print("🧠 正在分析...")
                    if hasattr(self.assistant, 'intelligent_chat'):
                        response = self.assistant.intelligent_chat(question)
                        print(f"💡 回答: {response}")
                    else:
                        print("❌ 助手功能不可用")
                        
            except KeyboardInterrupt:
                print("\n👋 用户中断，系统退出")
                break
            except Exception as e:
                print(f"❌ 处理异常: {e}")
    
    def launch(self):
        """主启动方法"""
        try:
            # 显示启动横幅
            self.print_startup_banner()
            
            # 环境检查
            if not self.check_environment():
                self._log("环境检查失败，启动中止", "ERROR")
                self.show_startup_summary()
                return False
            
            # 加载配置
            if not self.load_system_config():
                self._log("配置加载失败，启动中止", "ERROR")
                self.show_startup_summary()
                return False
            
            # 测试连接
            connections = self.test_connections()
            if not any(connections.values()):
                self._log("所有连接测试失败，但继续启动", "WARNING")
            
            # 创建助手
            if not self.create_assistant():
                self._log("助手创建失败，启动中止", "ERROR")
                self.show_startup_summary()
                return False
            
            # 创建界面
            if not self.create_console_interface():
                self._log("界面创建失败，启动中止", "ERROR")
                self.show_startup_summary()
                return False
            
            # 显示启动总结
            self.show_startup_summary()
            
            # 启动交互会话
            time.sleep(2)  # 给用户时间查看总结
            self.start_interactive_session()
            
            return True
            
        except KeyboardInterrupt:
            self._log("用户中断启动", "WARNING")
            return False
        except Exception as e:
            self._log(f"启动过程异常: {e}", "ERROR")
            self.show_startup_summary()
            return False


# In[ ]:


# ===================================================================
# 📊 模块6: 数据可视化引擎 - 新增模块
# ===================================================================

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from io import BytesIO
import base64
from typing import Dict, List, Optional, Any, Tuple

class PowerBankVisualizationEngine:
    """共享充电宝数据可视化引擎"""
    
    def __init__(self, ai_client=None):
        """初始化可视化引擎"""
        self.ai_client = ai_client
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 图表类型映射
        self.chart_types = {
            "柱状图": self._create_bar_chart,
            "折线图": self._create_line_chart,
            "饼图": self._create_pie_chart,
            "热力图": self._create_heatmap,
            "散点图": self._create_scatter_plot,
            "堆叠柱状图": self._create_stacked_bar_chart
        }
        
        # 业务场景与图表类型映射
        self.business_chart_mapping = {
            "品牌分析": ["饼图", "柱状图"],
            "用户分析": ["柱状图", "散点图"],
            "地区分析": ["热力图", "柱状图"],
            "时间分析": ["折线图", "柱状图"],
            "综合分析": ["堆叠柱状图", "热力图"]
        }
        
        custom_print("📊 数据可视化引擎初始化完成")
    
    def auto_visualize(self, data: List[Dict], question: str, analysis_type: str = "综合分析") -> Dict:
        """自动生成数据可视化"""
        try:
            if not data:
                return {"success": False, "message": "数据为空，无法生成图表"}
            
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # AI智能推荐图表类型
            recommended_chart = self._ai_recommend_chart(df, question, analysis_type)
            
            # 生成图表
            chart_result = self._generate_chart(df, recommended_chart, question)
            
            return {
                "success": True,
                "chart_type": recommended_chart,
                "chart_path": chart_result.get("path"),
                "chart_base64": chart_result.get("base64"),
                "insights": self._generate_visual_insights(df, recommended_chart)
            }
            
        except Exception as e:
            custom_print(f"❌ 可视化生成失败: {e}")
            return {"success": False, "message": f"可视化失败: {str(e)}"}
    
    def _ai_recommend_chart(self, df: pd.DataFrame, question: str, analysis_type: str) -> str:
        """AI智能推荐图表类型"""
        try:
            if not self.ai_client:
                # 基于规则的推荐
                return self._rule_based_recommend(df, analysis_type)
            
            # 数据特征分析
            data_features = {
                "行数": len(df),
                "列数": len(df.columns),
                "数值列": df.select_dtypes(include=[np.number]).columns.tolist(),
                "分类列": df.select_dtypes(include=['object']).columns.tolist(),
                "样本数据": df.head(3).to_dict('records')
            }
            
            prompt = f"""
作为数据可视化专家，请根据以下信息推荐最佳图表类型：

用户问题：{question}
分析类型：{analysis_type}
数据特征：{data_features}

可选图表类型：{list(self.chart_types.keys())}

请只返回一个最佳的图表类型名称。
"""
            
            response = self.ai_client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是数据可视化专家，只返回图表类型名称。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=50
            )
            
            recommended = response.choices[0].message.content.strip()
            
            # 验证推荐结果
            if recommended in self.chart_types:
                return recommended
            else:
                return self._rule_based_recommend(df, analysis_type)
                
        except Exception as e:
            custom_print(f"⚠️ AI推荐失败，使用规则推荐: {e}")
            return self._rule_based_recommend(df, analysis_type)
    
    def _rule_based_recommend(self, df: pd.DataFrame, analysis_type: str) -> str:
        """基于规则的图表推荐"""
        # 获取数值列和分类列
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        # 根据数据特征推荐
        if len(categorical_cols) == 1 and len(numeric_cols) == 1:
            if len(df) <= 10:
                return "饼图"
            else:
                return "柱状图"
        elif len(numeric_cols) >= 2:
            if "时间" in str(df.columns).lower() or "日期" in str(df.columns).lower():
                return "折线图"
            else:
                return "散点图"
        elif analysis_type in self.business_chart_mapping:
            return self.business_chart_mapping[analysis_type][0]
        else:
            return "柱状图"  # 默认
    
    def _generate_chart(self, df: pd.DataFrame, chart_type: str, title: str) -> Dict:
        """生成具体图表"""
        try:
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 调用对应的图表生成方法
            if chart_type in self.chart_types:
                self.chart_types[chart_type](df, ax, title)
            else:
                self._create_bar_chart(df, ax, title)
            
            # 保存图表
            chart_path = f"powerbank_memory/chart_{int(time.time())}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight', facecolor='white')
            
            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight', facecolor='white')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            plt.close()
            
            return {
                "path": chart_path,
                "base64": chart_base64
            }
            
        except Exception as e:
            custom_print(f"❌ 图表生成失败: {e}")
            return {"path": None, "base64": None}
    
    def _create_bar_chart(self, df: pd.DataFrame, ax, title: str):
        """创建柱状图"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            x_col = categorical_cols[0]
            y_col = numeric_cols[0]
            
            # 处理数据，取前10个
            plot_data = df.nlargest(10, y_col) if len(df) > 10 else df
            
            bars = ax.bar(plot_data[x_col], plot_data[y_col], 
                         color=plt.cm.Set3(np.linspace(0, 1, len(plot_data))))
            
            ax.set_xlabel(x_col)
            ax.set_ylabel(y_col)
            ax.set_title(f"{title} - {x_col}vs{y_col}")
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height:.1f}', ha='center', va='bottom')
            
            plt.xticks(rotation=45)
    
    def _create_pie_chart(self, df: pd.DataFrame, ax, title: str):
        """创建饼图"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            # 智能选择最适合的列进行饼图展示
            # 优先选择市场份额、占比等列
            target_col = None
            for col in numeric_cols:
                if any(keyword in col for keyword in ['份额', '占比', '比例', '百分比']):
                    target_col = col
                    break
            
            # 如果没有找到特定列，使用第一个数值列
            if target_col is None:
                target_col = numeric_cols[0]
            
            # 只显示前8个，其余合并为"其他"
            if len(df) > 8:
                top_data = df.nlargest(7, target_col)
                others_sum = df[~df.index.isin(top_data.index)][target_col].sum()
                
                labels = list(top_data[categorical_cols[0]]) + ['其他']
                sizes = list(top_data[target_col]) + [others_sum]
            else:
                # 确保转换为list，避免pandas Series问题
                labels = list(df[categorical_cols[0]])
                sizes = list(df[target_col])
            
            # 过滤掉0值或负值
            filtered_data = [(l, s) for l, s in zip(labels, sizes) if s > 0]
            if not filtered_data:
                ax.text(0.5, 0.5, '无有效数据', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f"{title} - 占比分析")
                return
            
            labels, sizes = zip(*filtered_data)
            
            # 生成颜色
            colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
            
            # 创建饼图
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, autopct='%1.1f%%',
                                             colors=colors, startangle=90, 
                                             textprops={'fontsize': 10})
            
            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_weight('bold')
            
            ax.set_title(f"{title} - {target_col}占比分析", fontsize=14, fontweight='bold')
            
        else:
            # 处理数据结构不符合要求的情况
            ax.text(0.5, 0.5, f'数据结构不适合饼图\n分类列: {len(categorical_cols)}\n数值列: {len(numeric_cols)}', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            ax.set_title(f"{title} - 数据结构错误")
            
            # 显示数据结构信息
            info_text = f"数据列信息:\n"
            for i, col in enumerate(df.columns):
                dtype = str(df[col].dtype)
                info_text += f"{col}: {dtype}\n"
                if i >= 5:  # 最多显示6列
                    info_text += "..."
                    break
            
            ax.text(0.5, 0.2, info_text, ha='center', va='center', 
                   transform=ax.transAxes, fontsize=8, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.7))
    
    def _create_line_chart(self, df: pd.DataFrame, ax, title: str):
        """创建折线图"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) >= 2:
            x_col = numeric_cols[0]
            y_col = numeric_cols[1]
        else:
            x_col = df.index
            y_col = numeric_cols[0] if len(numeric_cols) > 0 else df.columns[0]
        
        ax.plot(df[x_col] if x_col in df.columns else x_col, 
               df[y_col], marker='o', linewidth=2, markersize=6)
        
        ax.set_xlabel(str(x_col))
        ax.set_ylabel(str(y_col))
        ax.set_title(f"{title} - 趋势分析")
        ax.grid(True, alpha=0.3)
    
    def _create_heatmap(self, df: pd.DataFrame, ax, title: str):
        """创建热力图"""
        numeric_df = df.select_dtypes(include=[np.number])
        
        if len(numeric_df.columns) >= 2:
            correlation_matrix = numeric_df.corr()
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', 
                       center=0, ax=ax, fmt='.2f')
            ax.set_title(f"{title} - 相关性热力图")
        else:
            # 如果数值列不够，创建简单的数据热力图
            pivot_data = df.head(10)  # 只取前10行
            if len(pivot_data) > 0:
                sns.heatmap(pivot_data.select_dtypes(include=[np.number]), 
                           annot=True, cmap='YlOrRd', ax=ax, fmt='.1f')
                ax.set_title(f"{title} - 数据热力图")
    
    def _create_scatter_plot(self, df: pd.DataFrame, ax, title: str):
        """创建散点图"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) >= 2:
            x_col = numeric_cols[0]
            y_col = numeric_cols[1]
            
            scatter = ax.scatter(df[x_col], df[y_col], 
                               c=range(len(df)), cmap='viridis', 
                               alpha=0.7, s=60)
            
            ax.set_xlabel(x_col)
            ax.set_ylabel(y_col)
            ax.set_title(f"{title} - {x_col} vs {y_col}")
            
            plt.colorbar(scatter, ax=ax)
    
    def _create_stacked_bar_chart(self, df: pd.DataFrame, ax, title: str):
        """创建堆叠柱状图"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 1:
            # 使用第一个分类列作为x轴，多个数值列堆叠
            x_col = categorical_cols[0]
            y_cols = numeric_cols[:3]  # 最多3个数值列
            
            bottom = np.zeros(len(df))
            colors = plt.cm.Set3(np.linspace(0, 1, len(y_cols)))
            
            for i, col in enumerate(y_cols):
                ax.bar(df[x_col], df[col], bottom=bottom, 
                      label=col, color=colors[i])
                bottom += df[col]
            
            ax.set_xlabel(x_col)
            ax.set_ylabel('数值')
            ax.set_title(f"{title} - 堆叠分析")
            ax.legend()
            
            plt.xticks(rotation=45)
    
    def _generate_visual_insights(self, df: pd.DataFrame, chart_type: str) -> str:
        """生成可视化洞察"""
        try:
            insights = []
            
            # 基础统计
            insights.append(f"📊 数据概览: {len(df)}条记录，{len(df.columns)}个字段")
            
            # 数值列分析
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                for col in numeric_cols[:2]:  # 只分析前2个数值列
                    max_val = df[col].max()
                    min_val = df[col].min()
                    avg_val = df[col].mean()
                    insights.append(f"📈 {col}: 最大值{max_val:.2f}, 最小值{min_val:.2f}, 平均值{avg_val:.2f}")
            
            # 图表类型特定洞察
            if chart_type == "饼图":
                insights.append("🥧 饼图显示各部分占比关系，便于理解数据分布")
            elif chart_type == "折线图":
                insights.append("📈 折线图展示数据趋势变化，适合时间序列分析")
            elif chart_type == "柱状图":
                insights.append("📊 柱状图便于比较不同类别的数值大小")
            
            return "\n".join(insights)
            
        except Exception as e:
            return f"洞察生成失败: {str(e)}"

custom_print("✅ 数据可视化引擎 v1.0 加载完成")
custom_print("🎯 特性: AI智能推荐 • 多种图表类型 • 自动洞察生成 • 业务场景适配")

# ===================================================================
# 🚀 启动融合系统
# ===================================================================

def start_fusion_system():
    """启动融合系统的主函数"""
    try:
        custom_print("🚀 正在启动共享充电宝智能数据分析系统...")
        
        # 创建系统启动器
        launcher = PowerBankSystemLauncher()
        
        # 启动系统
        success = launcher.launch()
        
        if success:
            custom_print("🎉 系统启动成功！")
        else:
            custom_print("❌ 系统启动失败，请检查错误信息")
            
    except Exception as e:
        custom_print(f"❌ 启动异常: {e}")
        import traceback
        traceback.print_exc()

custom_print("✅ 系统启动器 v3.0 加载完成")
custom_print("🎯 特性: 智能分析 • 直接启动 • 降级模式 • 完善错误处理 • 数据可视化")

# ===================================================================
# 🎯 智能可视化对话测试模块
# ===================================================================

import webbrowser
import subprocess
import platform

class IntelligentVisualizationTester:
    """智能可视化对话测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.viz_engine = None
        self.ai_client = None
        self.test_data = self._prepare_test_data()
        self.conversation_history = []
        
        custom_print("🎯 智能可视化对话测试器初始化完成")
    
    def _prepare_test_data(self):
        """准备测试数据"""
        return {
            "品牌数据": [
                {"品牌名称": "思腾智电", "订单数量": 4963, "总收入": 9926.00, "市场份额": 13.20},
                {"品牌名称": "怪兽充电", "订单数量": 5100, "总收入": 10200.00, "市场份额": 13.60},
                {"品牌名称": "搜电", "订单数量": 5076, "总收入": 15228.00, "市场份额": 20.30},
                {"品牌名称": "街电", "订单数量": 4800, "总收入": 14400.00, "市场份额": 19.20},
                {"品牌名称": "小电", "订单数量": 4200, "总收入": 12600.00, "市场份额": 16.80},
                {"品牌名称": "来电", "订单数量": 3800, "总收入": 11400.00, "市场份额": 15.20}
            ],
            "用户数据": [
                {"年龄段": "18-25岁", "用户数量": 8500, "平均消费": 25.6, "活跃度": 85},
                {"年龄段": "26-35岁", "用户数量": 12000, "平均消费": 32.4, "活跃度": 92},
                {"年龄段": "36-45岁", "用户数量": 6800, "平均消费": 28.9, "活跃度": 78},
                {"年龄段": "46-55岁", "用户数量": 3200, "平均消费": 22.1, "活跃度": 65},
                {"年龄段": "55岁以上", "用户数量": 1500, "平均消费": 18.7, "活跃度": 45}
            ],
            "地区数据": [
                {"地区": "北京", "总收入": 45600.00, "订单数": 2280, "用户数": 15600},
                {"地区": "上海", "总收入": 42300.00, "订单数": 2115, "用户数": 14200},
                {"地区": "深圳", "总收入": 38900.00, "订单数": 1945, "用户数": 12800},
                {"地区": "广州", "总收入": 35200.00, "订单数": 1760, "用户数": 11500},
                {"地区": "杭州", "总收入": 28700.00, "订单数": 1435, "用户数": 9200}
            ],
            "时间数据": [
                {"月份": "2024-01", "收入": 28500.00, "订单数": 1425, "增长率": 5.2},
                {"月份": "2024-02", "收入": 31200.00, "订单数": 1560, "增长率": 9.5},
                {"月份": "2024-03", "收入": 35800.00, "订单数": 1790, "增长率": 14.7},
                {"月份": "2024-04", "收入": 38900.00, "订单数": 1945, "增长率": 8.7},
                {"月份": "2024-05", "收入": 42100.00, "订单数": 2105, "增长率": 8.2},
                {"月份": "2024-06", "收入": 45300.00, "订单数": 2265, "增长率": 7.6}
            ]
        }
    
    def initialize_engines(self):
        """初始化引擎"""
        try:
            # 初始化可视化引擎
            if 'PowerBankVisualizationEngine' in globals():
                self.viz_engine = PowerBankVisualizationEngine()
                custom_print("✅ 可视化引擎初始化成功")
            else:
                custom_print("⚠️ 使用简化版可视化引擎")
                from test_viz_engine import SimpleVisualizationEngine
                self.viz_engine = SimpleVisualizationEngine()
            
            # 初始化AI客户端（用于智能推荐）
            try:
                from openai import OpenAI
                self.ai_client = OpenAI(
                    api_key="sk-e2bad2bca73343a38f4f8d60f7435192",
                    base_url="https://api.deepseek.com/v1"
                )
                custom_print("✅ AI客户端初始化成功")
            except Exception as e:
                custom_print(f"⚠️ AI客户端初始化失败: {e}")
                self.ai_client = None
            
            return True
            
        except Exception as e:
            custom_print(f"❌ 引擎初始化失败: {e}")
            return False
    
    def intelligent_chart_recommendation(self, user_question: str) -> dict:
        """智能图表推荐"""
        try:
            if not self.ai_client:
                return self._rule_based_recommendation(user_question)
            
            prompt = f"""
作为数据可视化专家，请分析用户问题并推荐最佳的可视化方案。

用户问题：{user_question}

可选图表类型：柱状图、饼图、折线图、热力图、散点图、堆叠柱状图
可用数据类型：品牌数据、用户数据、地区数据、时间数据

请返回JSON格式：
{{
    "chart_type": "推荐的图表类型",
    "data_type": "需要的数据类型",
    "reason": "推荐理由",
    "title": "图表标题"
}}
"""
            
            response = self.ai_client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是数据可视化专家，只返回JSON格式的推荐结果。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=200
            )
            
            import json
            result = json.loads(response.choices[0].message.content.strip())
            return result
            
        except Exception as e:
            custom_print(f"⚠️ AI推荐失败，使用规则推荐: {e}")
            return self._rule_based_recommendation(user_question)
    
    def _rule_based_recommendation(self, question: str) -> dict:
        """基于规则的推荐"""
        question_lower = question.lower()
        
        if "份额" in question or "占比" in question or "比例" in question:
            return {
                "chart_type": "饼图",
                "data_type": "品牌数据",
                "reason": "饼图适合展示占比关系",
                "title": "市场份额分布"
            }
        elif "趋势" in question or "变化" in question or "时间" in question:
            return {
                "chart_type": "折线图",
                "data_type": "时间数据",
                "reason": "折线图适合展示趋势变化",
                "title": "趋势分析"
            }
        elif "对比" in question or "比较" in question or "排行" in question:
            return {
                "chart_type": "柱状图",
                "data_type": "品牌数据",
                "reason": "柱状图适合数据对比",
                "title": "数据对比分析"
            }
        elif "用户" in question or "年龄" in question:
            return {
                "chart_type": "柱状图",
                "data_type": "用户数据",
                "reason": "柱状图适合用户分析",
                "title": "用户分析"
            }
        elif "地区" in question or "城市" in question:
            return {
                "chart_type": "柱状图",
                "data_type": "地区数据",
                "reason": "柱状图适合地区对比",
                "title": "地区分析"
            }
        else:
            return {
                "chart_type": "柱状图",
                "data_type": "品牌数据",
                "reason": "默认推荐柱状图",
                "title": "数据分析"
            }
    
    def generate_visualization(self, recommendation: dict) -> dict:
        """生成可视化"""
        try:
            data_type = recommendation["data_type"]
            chart_type = recommendation["chart_type"]
            title = recommendation["title"]
            
            # 获取对应数据
            data = self.test_data.get(data_type, [])
            if not data:
                return {"success": False, "message": f"未找到{data_type}"}
            
            # 生成图表
            if hasattr(self.viz_engine, 'auto_visualize'):
                # 使用完整版可视化引擎
                result = self.viz_engine.auto_visualize(data, title, chart_type)
            else:
                # 使用简化版可视化引擎
                result = self.viz_engine.create_chart(data, chart_type, title)
            
            return result
            
        except Exception as e:
            return {"success": False, "message": f"可视化生成失败: {str(e)}"}
    
    def open_chart_file(self, file_path: str):
        """一键打开图表文件"""
        try:
            import os
            if not os.path.exists(file_path):
                custom_print(f"❌ 文件不存在: {file_path}")
                return False
            
            # 获取绝对路径
            abs_path = os.path.abspath(file_path)
            
            # 根据操作系统选择打开方式
            system = platform.system()
            
            if system == "Windows":
                os.startfile(abs_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", abs_path])
            else:  # Linux
                subprocess.run(["xdg-open", abs_path])
            
            custom_print(f"📂 已打开图表文件: {file_path}")
            return True
            
        except Exception as e:
            custom_print(f"❌ 打开文件失败: {e}")
            return False
    
    def chat_with_ai(self, user_question: str) -> dict:
        """与AI对话生成可视化"""
        custom_print(f"\n🤖 用户问题: {user_question}")
        custom_print("🔍 正在分析问题...")
        
        # 1. 智能推荐
        recommendation = self.intelligent_chart_recommendation(user_question)
        custom_print(f"💡 AI推荐: {recommendation['chart_type']} - {recommendation['reason']}")
        
        # 2. 生成可视化
        custom_print("🎨 正在生成可视化...")
        viz_result = self.generate_visualization(recommendation)
        
        if viz_result.get("success"):
            chart_path = viz_result.get("path") or viz_result.get("chart_path")
            custom_print(f"✅ 可视化生成成功: {recommendation['chart_type']}")
            custom_print(f"📁 文件保存: {chart_path}")
            
            # 3. 一键打开文件
            if chart_path:
                self.open_chart_file(chart_path)
            
            # 4. 记录对话历史
            self.conversation_history.append({
                "question": user_question,
                "recommendation": recommendation,
                "result": viz_result,
                "timestamp": time.time()
            })
            
            return {
                "success": True,
                "chart_type": recommendation["chart_type"],
                "chart_path": chart_path,
                "recommendation": recommendation,
                "message": f"成功生成{recommendation['chart_type']}并已自动打开"
            }
        else:
            custom_print(f"❌ 可视化生成失败: {viz_result.get('message')}")
            return {
                "success": False,
                "message": viz_result.get('message')
            }
    
    def interactive_test(self):
        """交互式测试"""
        custom_print("\n🎯 智能可视化对话测试开始")
        custom_print("=" * 60)
        
        # 预设测试问题
        test_questions = [
            "各品牌的市场份额如何？",
            "用户年龄分布情况怎么样？",
            "哪个地区的收入最高？",
            "最近几个月的收入趋势如何？",
            "品牌收入对比分析"
        ]
        
        custom_print("\n📋 预设测试问题:")
        for i, q in enumerate(test_questions, 1):
            custom_print(f"  {i}. {q}")
        
        custom_print("\n🎮 开始自动测试...")
        
        results = []
        for i, question in enumerate(test_questions, 1):
            custom_print(f"\n{'='*20} 测试 {i}/5 {'='*20}")
            result = self.chat_with_ai(question)
            results.append(result)
            time.sleep(2)  # 间隔2秒
        
        # 测试总结
        custom_print("\n" + "=" * 60)
        custom_print("📊 测试结果总结")
        custom_print("=" * 60)
        
        success_count = sum(1 for r in results if r.get("success"))
        custom_print(f"✅ 成功: {success_count}/{len(results)} 个测试")
        
        for i, result in enumerate(results, 1):
            status = "✅" if result.get("success") else "❌"
            chart_type = result.get("chart_type", "未知")
            custom_print(f"  {status} 测试{i}: {chart_type}")
        
        custom_print(f"\n🎉 智能可视化测试完成！")
        custom_print(f"📁 所有图表文件保存在: powerbank_memory/")
        
        return results

# 创建测试器实例
viz_tester = IntelligentVisualizationTester()

custom_print("✅ 智能可视化对话测试器 v1.0 加载完成")
custom_print("🎯 特性: AI智能推荐 • 自然语言理解 • 一键打开图表 • 交互式测试")

# ===================================================================
# 🚀 启动智能可视化测试
# ===================================================================

def start_visualization_test():
    """启动可视化测试"""
    try:
        custom_print("🚀 启动智能可视化测试系统...")
        
        # 初始化引擎
        if viz_tester.initialize_engines():
            custom_print("✅ 引擎初始化成功")
            
            # 开始交互式测试
            results = viz_tester.interactive_test()
            
            return results
        else:
            custom_print("❌ 引擎初始化失败")
            return None
            
    except Exception as e:
        custom_print(f"❌ 测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_single_question(question: str):
    """测试单个问题"""
    try:
        if not hasattr(viz_tester, 'viz_engine') or viz_tester.viz_engine is None:
            viz_tester.initialize_engines()
        
        return viz_tester.chat_with_ai(question)
    except Exception as e:
        custom_print(f"❌ 单问题测试失败: {e}")
        return {"success": False, "message": str(e)}

# 提供便捷的测试函数
def 测试可视化(问题="各品牌市场份额如何？"):
    """中文便捷测试函数"""
    return test_single_question(问题)

def 完整测试():
    """运行完整测试"""
    return start_visualization_test()

custom_print("✅ 智能可视化测试系统 v2.0 加载完成")
custom_print("🎯 使用方法:")
custom_print("  • 完整测试(): 运行所有预设测试")
custom_print("  • 测试可视化('你的问题'): 测试单个问题")
custom_print("  • test_single_question('your question'): 英文接口")

# 自动运行一个简单测试
custom_print("\n🎬 自动运行演示...")
demo_result = test_single_question("各品牌市场份额分布如何？")
if demo_result.get("success"):
    custom_print("🎉 演示成功！可视化系统工作正常")
else:
    custom_print(f"⚠️ 演示遇到问题: {demo_result.get('message')}")

