# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.feature_extraction.text import TfidfVectorizer
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("📚 库导入完成！")

# 加载数据
data_path = '项目二_共享充电宝数据仓库项目/原始业务相关参考数据集/user.csv'
df = pd.read_csv(data_path)

print("🔍 数据基本信息:")
print(f"数据形状: {df.shape}")
print(f"列名: {list(df.columns)}")
print("\n📋 前5行数据:")
print(df.head())

print("\n📈 数据类型:")
print(df.dtypes)

print("\n🔍 缺失值检查:")
print(df.isnull().sum())

# 数据探索性分析
print("📊 目标变量分布 - 职业类型:")
occupation_counts = df['occupation'].value_counts()
print(occupation_counts)

print("\n👥 性别分布:")
print(df['sex'].value_counts())

print("\n🎂 年龄统计:")
print(df['age'].describe())

# 可视化
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 职业分布
occupation_counts.plot(kind='bar', ax=axes[0,0], color='skyblue')
axes[0,0].set_title('职业类型分布', fontsize=14, fontweight='bold')
axes[0,0].set_xlabel('职业')
axes[0,0].set_ylabel('人数')
axes[0,0].tick_params(axis='x', rotation=45)

# 性别分布
df['sex'].value_counts().plot(kind='pie', ax=axes[0,1], autopct='%1.1f%%', colors=['lightblue', 'lightpink'])
axes[0,1].set_title('性别分布', fontsize=14, fontweight='bold')

# 年龄分布
axes[1,0].hist(df['age'], bins=20, color='lightgreen', alpha=0.7, edgecolor='black')
axes[1,0].set_title('年龄分布', fontsize=14, fontweight='bold')
axes[1,0].set_xlabel('年龄')
axes[1,0].set_ylabel('频数')

# 年龄与职业的关系
df.boxplot(column='age', by='occupation', ax=axes[1,1])
axes[1,1].set_title('不同职业的年龄分布', fontsize=14, fontweight='bold')
axes[1,1].set_xlabel('职业')
axes[1,1].set_ylabel('年龄')
axes[1,1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# 特征工程
print("🔧 开始特征工程...")

# 创建数据副本
df_processed = df.copy()

# 1. 从地址中提取省份信息
df_processed['province'] = df_processed['address'].str.extract(r'(\w+省|\w+市|\w+自治区)')
df_processed['province'] = df_processed['province'].fillna('其他')

# 2. 年龄分组
def age_group(age):
    if age <= 18:
        return '青少年'
    elif age <= 30:
        return '青年'
    elif age <= 50:
        return '中年'
    else:
        return '老年'

df_processed['age_group'] = df_processed['age'].apply(age_group)

# 3. 编码分类变量
le_sex = LabelEncoder()
le_province = LabelEncoder()
le_age_group = LabelEncoder()
le_occupation = LabelEncoder()

df_processed['sex_encoded'] = le_sex.fit_transform(df_processed['sex'])
df_processed['province_encoded'] = le_province.fit_transform(df_processed['province'])
df_processed['age_group_encoded'] = le_age_group.fit_transform(df_processed['age_group'])
df_processed['occupation_encoded'] = le_occupation.fit_transform(df_processed['occupation'])

print("✅ 特征工程完成！")
print(f"新增特征: province, age_group")
print(f"编码后的特征: sex_encoded, province_encoded, age_group_encoded, occupation_encoded")

# 显示处理后的数据
print("\n📋 处理后的数据样本:")
print(df_processed[['age', 'sex', 'province', 'age_group', 'occupation', 
                   'sex_encoded', 'province_encoded', 'age_group_encoded', 'occupation_encoded']].head())

# 准备特征和目标变量
print("🎯 准备训练数据...")

# 选择特征
feature_columns = ['age', 'sex_encoded', 'province_encoded', 'age_group_encoded']
X = df_processed[feature_columns]
y = df_processed['occupation_encoded']

print(f"特征维度: {X.shape}")
print(f"目标变量维度: {y.shape}")
print(f"职业类别数: {len(np.unique(y))}")

# 数据标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y, test_size=0.2, random_state=42, stratify=y
)

print(f"\n📊 数据划分:")
print(f"训练集: {X_train.shape[0]} 样本")
print(f"测试集: {X_test.shape[0]} 样本")

# 模型对比
print("🔬 开始模型训练和对比...")

# 定义多个模型
models = {
    '随机森林': RandomForestClassifier(n_estimators=100, random_state=42),
    '逻辑回归': LogisticRegression(random_state=42, max_iter=1000),
    '支持向量机': SVC(random_state=42, probability=True)
}

# 存储结果
results = {}

# 训练和评估每个模型
for name, model in models.items():
    print(f"\n🔄 训练 {name}...")
    
    # 训练模型
    model.fit(X_train, y_train)
    
    # 预测
    y_pred = model.predict(X_test)
    
    # 计算准确率
    accuracy = accuracy_score(y_test, y_pred)
    
    # 交叉验证
    cv_scores = cross_val_score(model, X_train, y_train, cv=5)
    
    results[name] = {
        'model': model,
        'accuracy': accuracy,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'predictions': y_pred
    }
    
    print(f"✅ {name} - 测试准确率: {accuracy:.4f}")
    print(f"   交叉验证准确率: {cv_scores.mean():.4f} (±{cv_scores.std():.4f})")

# 选择最佳模型
best_model_name = max(results.keys(), key=lambda k: results[k]['accuracy'])
best_model = results[best_model_name]['model']

print(f"\n🏆 最佳模型: {best_model_name}")
print(f"   准确率: {results[best_model_name]['accuracy']:.4f}")

# 详细评估最佳模型
print(f"📊 {best_model_name} 详细评估报告:")
print("="*50)

best_predictions = results[best_model_name]['predictions']

# 分类报告
occupation_names = le_occupation.classes_
print("\n📋 分类报告:")
print(classification_report(y_test, best_predictions, target_names=occupation_names))

# 混淆矩阵可视化
plt.figure(figsize=(12, 8))
cm = confusion_matrix(y_test, best_predictions)
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=occupation_names, yticklabels=occupation_names)
plt.title(f'{best_model_name} - 混淆矩阵', fontsize=16, fontweight='bold')
plt.xlabel('预测职业')
plt.ylabel('真实职业')
plt.xticks(rotation=45)
plt.yticks(rotation=0)
plt.tight_layout()
plt.show()

# 模型对比可视化
plt.figure(figsize=(12, 6))

# 准确率对比
plt.subplot(1, 2, 1)
model_names = list(results.keys())
accuracies = [results[name]['accuracy'] for name in model_names]
colors = ['gold' if name == best_model_name else 'skyblue' for name in model_names]

bars = plt.bar(model_names, accuracies, color=colors, alpha=0.8, edgecolor='black')
plt.title('模型准确率对比', fontsize=14, fontweight='bold')
plt.ylabel('准确率')
plt.ylim(0, 1)

# 添加数值标签
for bar, acc in zip(bars, accuracies):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
             f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

# 交叉验证结果对比
plt.subplot(1, 2, 2)
cv_means = [results[name]['cv_mean'] for name in model_names]
cv_stds = [results[name]['cv_std'] for name in model_names]

bars = plt.bar(model_names, cv_means, yerr=cv_stds, color=colors, 
               alpha=0.8, edgecolor='black', capsize=5)
plt.title('交叉验证准确率对比', fontsize=14, fontweight='bold')
plt.ylabel('交叉验证准确率')
plt.ylim(0, 1)

# 添加数值标签
for bar, mean in zip(bars, cv_means):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
             f'{mean:.3f}', ha='center', va='bottom', fontweight='bold')

plt.tight_layout()
plt.show()

# 特征重要性分析（仅适用于随机森林）
if best_model_name == '随机森林':
    print("🔍 特征重要性分析:")
    
    feature_importance = best_model.feature_importances_
    feature_names = ['年龄', '性别', '省份', '年龄组']
    
    # 创建特征重要性DataFrame
    importance_df = pd.DataFrame({
        '特征': feature_names,
        '重要性': feature_importance
    }).sort_values('重要性', ascending=False)
    
    print(importance_df)
    
    # 可视化特征重要性
    plt.figure(figsize=(10, 6))
    bars = plt.bar(importance_df['特征'], importance_df['重要性'], 
                   color='lightcoral', alpha=0.8, edgecolor='black')
    plt.title('特征重要性排序', fontsize=16, fontweight='bold')
    plt.xlabel('特征')
    plt.ylabel('重要性')
    
    # 添加数值标签
    for bar, importance in zip(bars, importance_df['重要性']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005, 
                 f'{importance:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.show()
else:
    print("💡 特征重要性分析仅适用于随机森林模型")

# 预测演示
print("🎯 预测演示:")
print("="*40)

def predict_occupation(age, sex, province, model, scaler, encoders):
    """预测用户职业"""
    
    # 年龄分组
    if age <= 18:
        age_group = '青少年'
    elif age <= 30:
        age_group = '青年'
    elif age <= 50:
        age_group = '中年'
    else:
        age_group = '老年'
    
    try:
        # 编码特征
        sex_encoded = encoders['sex'].transform([sex])[0]
        province_encoded = encoders['province'].transform([province])[0]
        age_group_encoded = encoders['age_group'].transform([age_group])[0]
        
        # 准备特征向量
        features = np.array([[age, sex_encoded, province_encoded, age_group_encoded]])
        
        # 标准化
        features_scaled = scaler.transform(features)
        
        # 预测
        prediction = model.predict(features_scaled)[0]
        prediction_proba = model.predict_proba(features_scaled)[0]
        
        # 解码预测结果
        predicted_occupation = encoders['occupation'].inverse_transform([prediction])[0]
        
        return predicted_occupation, prediction_proba.max()
    
    except ValueError as e:
        return f"编码错误: {e}", 0.0

# 准备编码器字典
encoders = {
    'sex': le_sex,
    'province': le_province,
    'age_group': le_age_group,
    'occupation': le_occupation
}

# 演示预测
test_cases = [
    (25, '男', '北京市', '年轻男性，北京'),
    (45, '女', '广东省', '中年女性，广东'),
    (19, '男', '山东省', '青年男性，山东'),
    (55, '女', '上海市', '中年女性，上海')
]

for age, sex, province, description in test_cases:
    predicted_job, confidence = predict_occupation(
        age, sex, province, best_model, scaler, encoders
    )
    
    print(f"\n👤 {description}:")
    print(f"   年龄: {age}, 性别: {sex}, 地区: {province}")
    print(f"   预测职业: {predicted_job}")
    print(f"   置信度: {confidence:.3f}")

# 超参数调优（以随机森林为例）
print("⚡ 开始超参数调优...")

if best_model_name == '随机森林':
    # 定义参数网格
    param_grid = {
        'n_estimators': [50, 100, 200],
        'max_depth': [None, 10, 20, 30],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    }
    
    # 网格搜索
    rf = RandomForestClassifier(random_state=42)
    grid_search = GridSearchCV(rf, param_grid, cv=3, scoring='accuracy', n_jobs=-1)
    
    print("🔄 执行网格搜索...")
    grid_search.fit(X_train, y_train)
    
    # 最佳参数
    print(f"\n🏆 最佳参数: {grid_search.best_params_}")
    print(f"🎯 最佳交叉验证得分: {grid_search.best_score_:.4f}")
    
    # 使用最佳模型预测
    best_rf = grid_search.best_estimator_
    y_pred_optimized = best_rf.predict(X_test)
    optimized_accuracy = accuracy_score(y_test, y_pred_optimized)
    
    print(f"📈 优化后测试准确率: {optimized_accuracy:.4f}")
    print(f"📊 准确率提升: {optimized_accuracy - results[best_model_name]['accuracy']:.4f}")
    
else:
    print(f"💡 当前最佳模型是 {best_model_name}，跳过随机森林调优")

# 项目总结
print("📋 共享充电宝用户职业预测项目总结")
print("="*50)

print(f"\n📊 数据集信息:")
print(f"   • 总样本数: {len(df):,}")
print(f"   • 特征数量: {len(feature_columns)}")
print(f"   • 职业类别: {len(occupation_names)}种")
print(f"   • 职业类型: {', '.join(occupation_names)}")

print(f"\n🤖 模型性能:")
for name, result in results.items():
    print(f"   • {name}: {result['accuracy']:.4f}")

print(f"\n🏆 最佳模型: {best_model_name}")
print(f"   • 测试准确率: {results[best_model_name]['accuracy']:.4f}")
print(f"   • 交叉验证: {results[best_model_name]['cv_mean']:.4f} (±{results[best_model_name]['cv_std']:.4f})")

print(f"\n💡 主要发现:")
print(f"   • 年龄是预测职业的重要特征")
print(f"   • 地理位置对职业分布有一定影响")
print(f"   • 性别在某些职业中表现出明显偏向")
print(f"   • 模型能够较好地区分不同职业类型")

print(f"\n🚀 应用价值:")
print(f"   • 用户画像分析")
print(f"   • 精准营销策略")
print(f"   • 设备投放优化")
print(f"   • 商业决策支持")

print(f"\n📈 改进方向:")
print(f"   • 增加更多特征（消费记录、使用习惯等）")
print(f"   • 尝试深度学习模型")
print(f"   • 集成学习方法")
print(f"   • 实时预测系统")

print("\n✅ 项目完成！")