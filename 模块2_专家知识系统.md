# 📚 模块2: 专家知识系统 - 完全重构版

## 📋 模块概述

`PowerBankExpertKnowledge` 是共享充电宝行业的专家知识库系统，集成了行业分析模板、业务规则和最佳实践，为数据分析提供专业指导。

## 🏗️ 核心架构

### 知识库组成
- **分析模板**: 预定义的业务分析框架
- **SQL模板**: 常用查询模式和最佳实践
- **业务规则**: 行业特定的业务逻辑
- **指标体系**: 核心KPI和度量标准

## 🔧 核心方法详解

### 1. 初始化方法

#### `__init__()`
**功能**: 初始化专家知识系统
**初始化内容**:
- 知识库字典
- 分析模板库
- SQL模板库
- 业务规则集

### 2. 知识库管理

#### `_init_knowledge_base()`
**功能**: 初始化核心知识库
**知识结构**:
```python
analysis_templates = {
    "品牌分析": {
        "description": "品牌市场份额和竞争力分析",
        "key_metrics": ["市场份额", "收入占比", "用户偏好"],
        "recommended_tables": ["brand_revenue_summary", "order_table"],
        "sql_patterns": ["GROUP BY brand", "SUM(revenue)"]
    }
}
```

### 3. 分析建议系统

#### `suggest_analysis_approach(question: str) -> Dict`
**功能**: 根据问题建议分析方法
**分析流程**:
1. 关键词提取和场景识别
2. 匹配最佳分析模板
3. 推荐相关数据表
4. 提供SQL查询建议

**返回结构**:
```python
{
    "analysis_type": "品牌分析",
    "confidence": 0.85,
    "recommended_tables": ["brand_revenue_summary"],
    "key_metrics": ["市场份额", "收入占比"],
    "sql_suggestions": ["GROUP BY brand"]
}
```

### 4. 模板匹配系统

#### `get_analysis_template(analysis_type: str) -> Dict`
**功能**: 获取指定类型的分析模板
**模板内容**:
- 分析描述和目标
- 关键指标定义
- 推荐数据表
- SQL查询模式

#### `get_sql_template(template_name: str) -> str`
**功能**: 获取SQL查询模板
**模板类型**:
- 品牌分析模板
- 用户行为分析模板
- 地区分布分析模板
- 时间趋势分析模板

### 5. 业务规则引擎

#### `validate_business_logic(query_type: str, params: Dict) -> Dict`
**功能**: 验证业务逻辑合理性
**验证规则**:
- 数据范围合理性检查
- 指标计算逻辑验证
- 业务场景适用性判断

#### `get_business_rules(domain: str) -> List[Dict]`
**功能**: 获取特定领域的业务规则
**规则类型**:
- 数据质量规则
- 计算逻辑规则
- 业务约束规则

## 🎯 分析模板详解

### 品牌分析模板
```python
"品牌分析": {
    "description": "品牌市场份额和竞争力分析",
    "key_metrics": ["市场份额", "收入占比", "用户偏好", "增长趋势"],
    "recommended_tables": ["brand_revenue_summary", "order_table"],
    "sql_patterns": ["GROUP BY brand", "SUM(revenue)", "COUNT(DISTINCT user_id)"],
    "insights": [
        "头部品牌集中度分析",
        "品牌用户忠诚度评估",
        "新兴品牌增长潜力"
    ]
}
```

### 用户分析模板
```python
"用户分析": {
    "description": "用户行为和特征分析",
    "key_metrics": ["活跃用户数", "使用频次", "消费习惯", "年龄分布"],
    "recommended_tables": ["user_table", "user_behavior_summary"],
    "sql_patterns": ["GROUP BY age_group", "AVG(usage_frequency)"],
    "insights": [
        "用户生命周期价值",
        "高价值用户特征",
        "用户流失风险预警"
    ]
}
```

### 地区分析模板
```python
"地区分析": {
    "description": "区域分布和地理特征分析",
    "key_metrics": ["区域覆盖率", "使用密度", "收入分布"],
    "recommended_tables": ["region_table", "region_heatmap_data"],
    "sql_patterns": ["GROUP BY province", "SUM(usage_count)"],
    "insights": [
        "一二三线城市差异",
        "区域扩张优先级",
        "本地化运营策略"
    ]
}
```

## 🚀 长期记忆系统

### PowerBankLongTermMemory类

#### `__init__(base_path: str)`
**功能**: 初始化长期记忆系统
**存储结构**:
- QA历史记录文件
- 分析历史记录文件
- 知识积累文件

#### `save_qa_pair(question: str, answer: str, context: str)`
**功能**: 保存问答对到长期记忆
**存储格式**:
```json
{
    "timestamp": "2025-01-20T10:30:00",
    "question": "用户问题",
    "answer": "系统回答",
    "context": "业务上下文",
    "tags": ["品牌分析", "市场份额"]
}
```

#### `search_similar_qa(question: str, limit: int) -> List[Dict]`
**功能**: 搜索相似的历史问答
**搜索算法**:
- 关键词匹配
- 语义相似度计算
- 业务场景相关性

#### `get_analysis_history(analysis_type: str) -> List[Dict]`
**功能**: 获取特定类型的分析历史
**历史信息**:
- 分析时间和类型
- 查询结果摘要
- 生成的业务洞察
- 用户反馈评分

## 📊 知识库统计

| 知识类型 | 数量 | 覆盖场景 |
|---------|------|---------|
| 分析模板 | 5个 | 品牌、用户、地区、时间、综合 |
| SQL模板 | 20+ | 常用查询模式 |
| 业务规则 | 15+ | 数据质量、计算逻辑 |
| 指标定义 | 30+ | 核心KPI体系 |

## 🎯 使用示例

```python
# 初始化专家知识系统
expert = PowerBankExpertKnowledge()

# 获取分析建议
suggestion = expert.suggest_analysis_approach("各品牌市场份额对比")
print(f"建议分析类型: {suggestion['analysis_type']}")

# 获取分析模板
template = expert.get_analysis_template("品牌分析")
print(f"关键指标: {template['key_metrics']}")

# 初始化长期记忆
long_memory = PowerBankLongTermMemory("./memory")

# 保存分析结果
long_memory.save_qa_pair(
    question="品牌市场份额分析",
    answer="小电占比35%，街电30%...",
    context="品牌竞争分析"
)
```

## 🔮 系统优势

1. **专业性强**: 深度结合共享充电宝行业知识
2. **模板丰富**: 覆盖主要业务分析场景
3. **智能匹配**: 自动推荐最佳分析方法
4. **历史积累**: 长期记忆系统持续学习
5. **规则引擎**: 业务逻辑验证和质量保证

---

*本文档基于PowerBankExpertKnowledge v3.0版本*