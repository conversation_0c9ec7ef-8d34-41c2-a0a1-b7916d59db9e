{"cells": [{"cell_type": "markdown", "id": "9ca2d36f", "metadata": {}, "source": ["智能信息查询目标\n", "1. 了解什么是 Function Calling 技术\n", "2. 掌握如何通过基于 OpenAI、Function Calling 等技术让大模型能够回复基于私有化知识的问题\n"]}, {"cell_type": "markdown", "id": "2c48d2b1", "metadata": {}, "source": ["# 1.私有知识库问题提问"]}, {"cell_type": "markdown", "id": "0bd7aa2f", "metadata": {}, "source": ["导入相关模块"]}, {"cell_type": "code", "execution_count": 1, "id": "db9ea0a1", "metadata": {}, "outputs": [], "source": ["import os\n", "import io\n", "import json\n", "import inspect\n", "import numpy as np\n", "import pandas as pd\n", "import pymysql\n", "import openai\n", "from openai import OpenAI"]}, {"cell_type": "markdown", "id": "1a35b2ca", "metadata": {}, "source": ["创建客户端"]}, {"cell_type": "code", "execution_count": 2, "id": "4de1ed36", "metadata": {}, "outputs": [], "source": ["from rich.console import Console\n", "\n", "console = Console()\n", "\n", "def custom_print(info):\n", "    console.print(info)"]}, {"cell_type": "code", "execution_count": 3, "id": "6ce06f93", "metadata": {}, "outputs": [], "source": ["# 创建HTTP客户端时不使用proxies参数\n", "import httpx\n", "http_client = httpx.Client(follow_redirects=True)"]}, {"cell_type": "code", "execution_count": 4, "id": "c3bb069f", "metadata": {}, "outputs": [], "source": ["api_key = \"sk-e2bad2bca73343a38f4f8d60f7435192\"  # 注意保护你的API密钥\n", "base_url = \"https://api.deepseek.com/v1\"\n", "\n", "# 使用自定义HTTP客户端创建OpenAI实例\n", "client = OpenAI(\n", "    api_key=api_key,\n", "    base_url=base_url,\n", "    http_client=http_client\n", ")"]}, {"cell_type": "markdown", "id": "1437a51b", "metadata": {}, "source": ["创建消息"]}, {"cell_type": "code", "execution_count": 5, "id": "300e88d2", "metadata": {}, "outputs": [], "source": ["messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "] "]}, {"cell_type": "code", "execution_count": 6, "id": "cdae7c68", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletion</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'bfeea325-3356-4475-b374-341d414a8a6d'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">choices</span>=<span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Choice</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">finish_reason</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'stop'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">logprobs</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">message</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">\"要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">COUNT(*) AS total_records \\nFROM user_base_info;\\n```\\n\\n这条SQL会返回表中的总行数，列名为 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">`total_records`。\\n\\n如果您需要更具体的查询（比如按条件统计），可以添加WHERE子句，例如：\\n```sql\\nSELECT COUNT(*) </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">AS active_users \\nFROM user_base_info \\nWHERE status = 'active';\\n```\\n\\n如果您能提供数据库类型(MySQL, </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">PostgreSQL等)或具体需求，我可以给出更针对性的建议。\"</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">]</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">created</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1752999293</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">model</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'deepseek-chat'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">object</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'chat.completion'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">service_tier</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">system_fingerprint</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'fp_8802369eaa_prod0623_fp8_kvcache'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">usage</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">CompletionUsage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">completion_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">116</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">total_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">143</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">completion_tokens_details</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_tokens_details</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">PromptTokensDetails</span><span style=\"font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">audio_tokens</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>, <span style=\"color: #808000; text-decoration-color: #808000\">cached_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"font-weight: bold\">)</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_cache_hit_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">prompt_cache_miss_tokens</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27</span>\n", "    <span style=\"font-weight: bold\">)</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletion\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mid\u001b[0m=\u001b[32m'bfeea325-3356-4475-b374-341d414a8a6d'\u001b[0m,\n", "    \u001b[33mchoices\u001b[0m=\u001b[1m[\u001b[0m\n", "        \u001b[1;35mChoice\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mfinish_reason\u001b[0m=\u001b[32m'stop'\u001b[0m,\n", "            \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "            \u001b[33mlogprobs\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "            \u001b[33mmessage\u001b[0m=\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mcontent\u001b[0m=\u001b[32m\"要查询\u001b[0m\u001b[32m `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT \u001b[0m\n", "\u001b[32mCOUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records \\nFROM user_base_info;\\n```\\n\\n这条SQL会返回表中的总行数，列名为 \u001b[0m\n", "\u001b[32m`total_records`。\\n\\n如果您需要更具体的查询（比如按条件统计），可以添加WHERE子句，例如：\\n```sql\\nSELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m \u001b[0m\n", "\u001b[32mAS active_users \\nFROM user_base_info \\nWHERE status = 'active';\\n```\\n\\n如果您能提供数据库类型\u001b[0m\u001b[32m(\u001b[0m\u001b[32mMySQL, \u001b[0m\n", "\u001b[32mPostgreSQL等\u001b[0m\u001b[32m)\u001b[0m\u001b[32m或具体需求，我可以给出更针对性的建议。\"\u001b[0m,\n", "                \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "                \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "                \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "                \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "                \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m]\u001b[0m,\n", "    \u001b[33mcreated\u001b[0m=\u001b[1;36m1752999293\u001b[0m,\n", "    \u001b[33mmodel\u001b[0m=\u001b[32m'deepseek-chat'\u001b[0m,\n", "    \u001b[33mobject\u001b[0m=\u001b[32m'chat.completion'\u001b[0m,\n", "    \u001b[33mservice_tier\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33msystem_fingerprint\u001b[0m=\u001b[32m'fp_8802369eaa_prod0623_fp8_kvcache'\u001b[0m,\n", "    \u001b[33musage\u001b[0m=\u001b[1;35mCompletionUsage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcompletion_tokens\u001b[0m=\u001b[1;36m116\u001b[0m,\n", "        \u001b[33mprompt_tokens\u001b[0m=\u001b[1;36m27\u001b[0m,\n", "        \u001b[33mtotal_tokens\u001b[0m=\u001b[1;36m143\u001b[0m,\n", "        \u001b[33mcompletion_tokens_details\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mprompt_tokens_details\u001b[0m=\u001b[1;35mPromptTokensDetails\u001b[0m\u001b[1m(\u001b[0m\u001b[33maudio_tokens\u001b[0m=\u001b[3;35mNone\u001b[0m, \u001b[33mcached_tokens\u001b[0m=\u001b[1;36m0\u001b[0m\u001b[1m)\u001b[0m,\n", "        \u001b[33mprompt_cache_hit_tokens\u001b[0m=\u001b[1;36m0\u001b[0m,\n", "        \u001b[33mprompt_cache_miss_tokens\u001b[0m=\u001b[1;36m27\u001b[0m\n", "    \u001b[1m)\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "\tmodel=\"deepseek-chat\",\n", "\tmessages=messages,\n", ")\n", "custom_print(response)"]}, {"cell_type": "code", "execution_count": 7, "id": "184dcb4a", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT COUNT(*) AS </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">total_records \\nFROM user_base_info;\\n```\\n\\n这条SQL会返回表中的总行数，列名为 </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">`total_records`。\\n\\n如果您需要更具体的查询（比如按条件统计），可以告诉我您的具体需求，我可以提供更精确的分析建议。</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">\\n\\n注意：实际执行查询前，请确认您有该表的查询权限，且表名拼写正确（包括大小写敏感性，取决于您的数据库系统）。'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m'要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS \u001b[0m\n", "\u001b[32mtotal_records \\nFROM user_base_info;\\n```\\n\\n这条SQL会返回表中的总行数，列名为 \u001b[0m\n", "\u001b[32m`total_records`。\\n\\n如果您需要更具体的查询（比如按条件统计），可以告诉我您的具体需求，我可以提供更精确的分析建议。\u001b[0m\n", "\u001b[32m\\n\\n注意：实际执行查询前，请确认您有该表的查询权限，且表名拼写正确（包括大小写敏感性，取决于您的数据库系统）。'\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=messages,\n", "    )\n", "custom_print(response.choices[0].message)"]}, {"cell_type": "markdown", "id": "b090b8c7", "metadata": {}, "source": ["# 2.定义数据查询函数"]}, {"cell_type": "markdown", "id": "ef261cea", "metadata": {}, "source": ["## 2.1 定义函数"]}, {"cell_type": "markdown", "id": "b5b8f6e8", "metadata": {}, "source": ["定义函数。输入SQL，返回从MySQL查询的结果（以JSON格式返回）"]}, {"cell_type": "markdown", "id": "a75e4298", "metadata": {}, "source": ["预留一个问题：为什么我要编写函数字典，以及为什么参数有的是必选、有的是可选"]}, {"cell_type": "markdown", "id": "5fc7fd43", "metadata": {}, "source": ["补充说明：当 orient='records' 时，生成的 JSON 是一个数组，数组中的每个元素是 DataFrame 的一行，格式为键值对（列名: 值）。这种格式适合直接传递给前端或 API 接口"]}, {"cell_type": "code", "execution_count": 8, "id": "8b0dfa88", "metadata": {}, "outputs": [], "source": ["def get_user_base_info(sql,username='root',password='',db='user_info'):\n", "    \"\"\"\n", "    当前函数，用于查询指定数据库下的相关表中的数据\n", "    ：param sql：参数为必要参数,字符串类型的SQL语句,\n", "    ：param username：参数为可选参数,代表:用户名,字符串类型,\n", "    ：param password：参数为可选参数,代表:用户密码,字符串类型,\n", "    ：param db：参数为可选参数,代表:需要连接的数据库名字,字符串类型,\n", "    ：return:当前SQL语句执行完的查询结果\n", "    \"\"\"\n", "    conn = pymysql.connect(\n", "        host = 'localhost',\n", "        user = username,\n", "        password = password,\n", "        db = db,\n", "        charset = 'utf8'\n", "    )\n", "    try: \n", "        with conn.cursor() as cursor:\n", "            cursor.execute(sql)\n", "            results = cursor.fetchall()\n", "    finally:\n", "        cursor.close()\n", "        conn.close()\n", "    column_names = [desc[0] for desc in cursor.description]\n", "    print(type(column_names))\n", "    print(column_names)\n", "    print(\"===============================================================================\")\n", "    print(results)\n", "    df = pd.DataFrame(results,columns=column_names)\n", "    print(\"===============================================================================\")\n", "    print(df)\n", "    return df.to_json(orient = 'records')"]}, {"cell_type": "markdown", "id": "885c1b11", "metadata": {}, "source": ["## 2.2 测试函数"]}, {"cell_type": "markdown", "id": "42b829fc", "metadata": {}, "source": ["get_user_base_info函数准确性验证：对于函数准确性验证是开发过程中十分重要的环节，可以通过调用函数，传递一个SQL进行测试，如果函数编写正确，则会返回预期的结果。"]}, {"cell_type": "code", "execution_count": 9, "id": "3e433139", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n", "['Tables_in_user_info']\n", "===============================================================================\n", "(('is_survived',), ('user_base_info',), ('user_extend_info',))\n", "===============================================================================\n", "  Tables_in_user_info\n", "0         is_survived\n", "1      user_base_info\n", "2    user_extend_info\n"]}, {"data": {"text/plain": ["'[{\"Tables_in_user_info\":\"is_survived\"},{\"Tables_in_user_info\":\"user_base_info\"},{\"Tables_in_user_info\":\"user_extend_info\"}]'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_base_info('SHOW TABLES')"]}, {"cell_type": "markdown", "id": "23540a0c", "metadata": {}, "source": ["```\n", "column_names = [desc[0] for desc in cursor.description]\n", "    print(type(column_names))\n", "    print(column_names)\n", "    print(\"===============================================================================\")\n", "    print(results)\n", "    df = pd.DataFrame(results,columns=column_names)\n", "    print(\"===============================================================================\")\n", "    print(df)\n", "    return df.to_json(orient = 'records')\n", "```"]}, {"cell_type": "markdown", "id": "39cb04f3", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "507cb1c6", "metadata": {}, "source": ["```\n", "'passenger_id', 'name', 'sex', 'age', 'sib_sp', 'parch'\n", "((1, '<PERSON><PERSON>, Mr. <PERSON>', 'male', 22, '1', '0'),\n", " (2, '<PERSON><PERSON><PERSON>s, Mrs. <PERSON> (<PERSON>)', 'female', 38, '1', '0'), \n", " (3, '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>', 'female', 26, '0', '0'))\n", "```"]}, {"cell_type": "code", "execution_count": 10, "id": "29c348ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n", "['passenger_id', 'name', 'sex', 'age', 'sib_sp', 'parch']\n", "===============================================================================\n", "((1, '<PERSON><PERSON>, Mr. <PERSON>', 'male', 22, '1', '0'), (2, '<PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (Florence <PERSON>)', 'female', 38, '1', '0'), (3, '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>', 'female', 26, '0', '0'))\n", "===============================================================================\n", "   passenger_id                                               name     sex  \\\n", "0             1                            <PERSON><PERSON>, Mr. <PERSON>    male   \n", "1             2  <PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...  female   \n", "2             3                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female   \n", "\n", "   age sib_sp parch  \n", "0   22      1     0  \n", "1   38      1     0  \n", "2   26      0     0  \n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"<PERSON><PERSON>, Mr. <PERSON> </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">Harris\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"male\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">22</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"1\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Cumings, Mrs. John Bradley </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">(<PERSON>)\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"female\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"1\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}</span>,<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">\"passenger_id\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"name\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"Heikkinen, </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">Miss. Laina\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sex\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"female\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"age\"</span>:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"sib_sp\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span>,<span style=\"color: #008000; text-decoration-color: #008000\">\"parch\"</span>:<span style=\"color: #008000; text-decoration-color: #008000\">\"0\"</span><span style=\"font-weight: bold\">}]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m1\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON>, Mr. <PERSON> \u001b[0m\n", "\u001b[32m<PERSON><PERSON><PERSON>\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"male\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m22\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"1\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m2\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> \u001b[0m\n", "\u001b[32m(\u001b[0m\u001b[32mFlorence <PERSON>\u001b[0m\u001b[32m)\u001b[0m\u001b[32m\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"female\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m38\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"1\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m,\u001b[1m{\u001b[0m\u001b[32m\"passenger_id\"\u001b[0m:\u001b[1;36m3\u001b[0m,\u001b[32m\"name\"\u001b[0m:\u001b[32m\"<PERSON><PERSON><PERSON><PERSON>, \u001b[0m\n", "\u001b[32mMiss. <PERSON><PERSON>\"\u001b[0m,\u001b[32m\"sex\"\u001b[0m:\u001b[32m\"female\"\u001b[0m,\u001b[32m\"age\"\u001b[0m:\u001b[1;36m26\u001b[0m,\u001b[32m\"sib_sp\"\u001b[0m:\u001b[32m\"0\"\u001b[0m,\u001b[32m\"parch\"\u001b[0m:\u001b[32m\"0\"\u001b[0m\u001b[1m}\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["custom_print(get_user_base_info('SELECT * FROM user_base_info LIMIT 3'))"]}, {"cell_type": "markdown", "id": "c833326f", "metadata": {}, "source": ["# 3.基于大模型的私有化知识查询"]}, {"cell_type": "markdown", "id": "255c3c1b", "metadata": {}, "source": ["## 3.1 定义 tools "]}, {"cell_type": "markdown", "id": "63839ef6", "metadata": {}, "source": ["def get_user_base_info(sql,username='root',password='',db='user_info'):"]}, {"cell_type": "code", "execution_count": 11, "id": "711bc484", "metadata": {}, "outputs": [], "source": ["tools = [{'type': 'function',\n", "         'function': {\n", "            'name': 'get_user_base_info',\n", "            'description': '当前函数，用于查询指定数据库下的相关表中的数据。输入参数为字符串类型的SQL语句，输出为当前SQL语句执行完的查询结果',\n", "            'parameters': {'type': 'object',\n", "                           'properties': {'sql': {'description': '必要参数，字符串类型的SQL语句','type': 'string'},\n", "                                          'username': {'description': '用户名,字符串类型','type': 'string'},\n", "                                          'password': {'description': '用户密码,字符串类型','type': 'string'},\n", "                                          'db': {'description': '需要连接的数据库名字,字符串类型','type': 'string'}\n", "                                         },\n", "         'required': ['sql']},\n", "        }}]"]}, {"cell_type": "markdown", "id": "a128e35d", "metadata": {}, "source": ["## 3.2 通过工具获取答案"]}, {"cell_type": "markdown", "id": "6d682b2a", "metadata": {}, "source": ["```\n", "messages = [    \n", "    {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家,精通各种简单和复杂的数据分析\"},\n", "    {\"role\": \"user\", \"content\": \"请问user_base_info数据表中一共有多少条数据？\"}\n", "] \n", "```"]}, {"cell_type": "code", "execution_count": 12, "id": "969bea1b", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'要查询 `user_base_info` 数据表中的记录总数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT COUNT(*) AS </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">total_records \\nFROM </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">user_base_info;\\n```\\n\\n这条SQL会返回表中的总行数，列名为`total_records`。\\n\\n如果您使用的是特定的大数据平台，可能</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">有更优化的查询方式：\\n- 在Hive中：`SELECT COUNT(1) FROM user_base_info;`\\n- 在Spark </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">SQL中同样适用标准SQL语法\\n\\n注意事项：\\n1. 对于超大规模表(亿级以上)，COUNT操作可能会比较耗时\\n2. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">如果表有分区，考虑添加分区条件提高查询效率\\n3. </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">某些平台可能有元数据可以直接获取近似行数而不需要全表扫描\\n\\n需要我针对特定平台提供更详细的查询方案吗？'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m'要查询 `user_base_info` 数据表中的记录总数，可以使用以下SQL语句：\\n\\n```sql\\nSELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS \u001b[0m\n", "\u001b[32mtotal_records \\nFROM \u001b[0m\n", "\u001b[32muser_base_info;\\n```\\n\\n这条SQL会返回表中的总行数，列名为`total_records`。\\n\\n如果您使用的是特定的大数据平台，可能\u001b[0m\n", "\u001b[32m有更优化的查询方式：\\n- 在Hive中：`SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m1\u001b[0m\u001b[32m)\u001b[0m\u001b[32m FROM user_base_info;`\\n- 在Spark \u001b[0m\n", "\u001b[32mSQL中同样适用标准SQL语法\\n\\n注意事项：\\n1. 对于超大规模表\u001b[0m\u001b[32m(\u001b[0m\u001b[32m亿级以上\u001b[0m\u001b[32m)\u001b[0m\u001b[32m，COUNT操作可能会比较耗时\\n2. \u001b[0m\n", "\u001b[32m如果表有分区，考虑添加分区条件提高查询效率\\n3. \u001b[0m\n", "\u001b[32m某些平台可能有元数据可以直接获取近似行数而不需要全表扫描\\n\\n需要我针对特定平台提供更详细的查询方案吗？'\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[3;35mNone\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=messages,\n", "    )\n", "custom_print(response.choices[0].message)"]}, {"cell_type": "markdown", "id": "35d32a98", "metadata": {}, "source": ["def get_user_base_info(sql,username='root',password='',db='user_info'):"]}, {"cell_type": "code", "execution_count": 13, "id": "970ee49e", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_33014284-a3fa-4fc6-aba6-012bef5dc7a8'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_base_info'</span>\n", "            <span style=\"font-weight: bold\">)</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">]</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "        \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mid\u001b[0m=\u001b[32m'call_0_33014284-a3fa-4fc6-aba6-012bef5dc7a8'\u001b[0m,\n", "            \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                \u001b[33mname\u001b[0m=\u001b[32m'get_user_base_info'\u001b[0m\n", "            \u001b[1m)\u001b[0m,\n", "            \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "            \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m]\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=messages,\n", "        tools = tools,\n", "        tool_choice=\"auto\",\n", "    )\n", "custom_print(response.choices[0].message)"]}, {"cell_type": "code", "execution_count": 14, "id": "0ce37874", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n", "['total_records']\n", "===============================================================================\n", "((891,),)\n", "===============================================================================\n", "   total_records\n", "0            891\n"]}, {"data": {"text/plain": ["'[{\"total_records\":891}]'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["get_user_base_info(\"SELECT COUNT(*) AS total_records FROM user_base_info\")"]}, {"cell_type": "markdown", "id": "3718ebdf", "metadata": {}, "source": ["创建函数指针"]}, {"cell_type": "code", "execution_count": 15, "id": "74fef4c3", "metadata": {}, "outputs": [], "source": ["tools_list = {\"get_user_base_info\": get_user_base_info} "]}, {"cell_type": "markdown", "id": "6cfbdf8c", "metadata": {}, "source": ["获取函数名"]}, {"cell_type": "code", "execution_count": 16, "id": "36b286d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["get_user_base_info\n"]}], "source": ["func_name = response.choices[0].message.tool_calls[0].function.name\n", "print(func_name)"]}, {"cell_type": "markdown", "id": "5f38382f", "metadata": {}, "source": ["获取函数参数"]}, {"cell_type": "code", "execution_count": 17, "id": "94cb6a5f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT COUNT(*) AS total_records FROM user_base_info\n"]}], "source": ["func_args = json.loads(response.choices[0].message.tool_calls[0].function.arguments)['sql']\n", "print(func_args) "]}, {"cell_type": "markdown", "id": "788432c8", "metadata": {}, "source": ["获取函数执行结果"]}, {"cell_type": "code", "execution_count": 18, "id": "61d2d799", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n", "['total_records']\n", "===============================================================================\n", "((891,),)\n", "===============================================================================\n", "   total_records\n", "0            891\n", "[{\"total_records\":891}]\n"]}], "source": ["func_result = tools_list[func_name](func_args)\n", "print(func_result)"]}, {"cell_type": "markdown", "id": "7cf56cce", "metadata": {}, "source": ["## 3.3 消息二次封装"]}, {"cell_type": "code", "execution_count": 19, "id": "c4f71879", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "    <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_33014284-a3fa-4fc6-aba6-012bef5dc7a8'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_base_info'</span>\n", "            <span style=\"font-weight: bold\">)</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "            <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "        <span style=\"font-weight: bold\">)</span>\n", "    <span style=\"font-weight: bold\">]</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "    \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "    \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "    \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "    \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "        \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "            \u001b[33mid\u001b[0m=\u001b[32m'call_0_33014284-a3fa-4fc6-aba6-012bef5dc7a8'\u001b[0m,\n", "            \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                \u001b[33mname\u001b[0m=\u001b[32m'get_user_base_info'\u001b[0m\n", "            \u001b[1m)\u001b[0m,\n", "            \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "            \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "        \u001b[1m)\u001b[0m\n", "    \u001b[1m]\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["message_call = response.choices[0].message\n", "custom_print(message_call)"]}, {"cell_type": "code", "execution_count": 20, "id": "5844e4ed", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '你是大数据领域的数据专家,精通各种简单和复杂的数据分析'},\n", " {'role': 'user', 'content': '请问user_base_info数据表中一共有多少条数据？'}]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["messages"]}, {"cell_type": "code", "execution_count": 21, "id": "3ee7fdfe", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'system'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'user'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'请问user_base_info数据表中一共有多少条数据？'</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessage</span><span style=\"font-weight: bold\">(</span>\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">content</span>=<span style=\"color: #008000; text-decoration-color: #008000\">''</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">refusal</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">role</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'assistant'</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">audio</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">function_call</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>,\n", "        <span style=\"color: #808000; text-decoration-color: #808000\">tool_calls</span>=<span style=\"font-weight: bold\">[</span>\n", "            <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">ChatCompletionMessageToolCall</span><span style=\"font-weight: bold\">(</span>\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">id</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'call_0_33014284-a3fa-4fc6-aba6-012bef5dc7a8'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">function</span>=<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Function</span><span style=\"font-weight: bold\">(</span>\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">arguments</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}'</span>,\n", "                    <span style=\"color: #808000; text-decoration-color: #808000\">name</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'get_user_base_info'</span>\n", "                <span style=\"font-weight: bold\">)</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">type</span>=<span style=\"color: #008000; text-decoration-color: #008000\">'function'</span>,\n", "                <span style=\"color: #808000; text-decoration-color: #808000\">index</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "            <span style=\"font-weight: bold\">)</span>\n", "        <span style=\"font-weight: bold\">]</span>\n", "    <span style=\"font-weight: bold\">)</span>,\n", "    <span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'role'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'tool'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'tool_call_id'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'call_0_33014284-a3fa-4fc6-aba6-012bef5dc7a8'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'name'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'get_user_base_info'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'content'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'[{\"total_records\":891}]'</span>\n", "    <span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'system'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'你是大数据领域的数据专家,精通各种简单和复杂的数据分析'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1m{\u001b[0m\u001b[32m'role'\u001b[0m: \u001b[32m'user'\u001b[0m, \u001b[32m'content'\u001b[0m: \u001b[32m'请问user_base_info数据表中一共有多少条数据？'\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[1;35mChatCompletionMessage\u001b[0m\u001b[1m(\u001b[0m\n", "        \u001b[33mcontent\u001b[0m=\u001b[32m''\u001b[0m,\n", "        \u001b[33mrefusal\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mrole\u001b[0m=\u001b[32m'assistant'\u001b[0m,\n", "        \u001b[33maudio\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mfunction_call\u001b[0m=\u001b[3;35mNone\u001b[0m,\n", "        \u001b[33mtool_calls\u001b[0m=\u001b[1m[\u001b[0m\n", "            \u001b[1;35mChatCompletionMessageToolCall\u001b[0m\u001b[1m(\u001b[0m\n", "                \u001b[33mid\u001b[0m=\u001b[32m'call_0_33014284-a3fa-4fc6-aba6-012bef5dc7a8'\u001b[0m,\n", "                \u001b[33mfunction\u001b[0m=\u001b[1;35mFunction\u001b[0m\u001b[1m(\u001b[0m\n", "                    \u001b[33marguments\u001b[0m=\u001b[32m'\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"sql\":\"SELECT COUNT\u001b[0m\u001b[32m(\u001b[0m\u001b[32m*\u001b[0m\u001b[32m)\u001b[0m\u001b[32m AS total_records FROM user_base_info\"\u001b[0m\u001b[32m}\u001b[0m\u001b[32m'\u001b[0m,\n", "                    \u001b[33mname\u001b[0m=\u001b[32m'get_user_base_info'\u001b[0m\n", "                \u001b[1m)\u001b[0m,\n", "                \u001b[33mtype\u001b[0m=\u001b[32m'function'\u001b[0m,\n", "                \u001b[33mindex\u001b[0m=\u001b[1;36m0\u001b[0m\n", "            \u001b[1m)\u001b[0m\n", "        \u001b[1m]\u001b[0m\n", "    \u001b[1m)\u001b[0m,\n", "    \u001b[1m{\u001b[0m\n", "        \u001b[32m'role'\u001b[0m: \u001b[32m'tool'\u001b[0m,\n", "        \u001b[32m'tool_call_id'\u001b[0m: \u001b[32m'call_0_33014284-a3fa-4fc6-aba6-012bef5dc7a8'\u001b[0m,\n", "        \u001b[32m'name'\u001b[0m: \u001b[32m'get_user_base_info'\u001b[0m,\n", "        \u001b[32m'content'\u001b[0m: \u001b[32m'\u001b[0m\u001b[32m[\u001b[0m\u001b[32m{\u001b[0m\u001b[32m\"total_records\":891\u001b[0m\u001b[32m}\u001b[0m\u001b[32m]\u001b[0m\u001b[32m'\u001b[0m\n", "    \u001b[1m}\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["message_call = response.choices[0].message\n", "tool_calls = message_call.tool_calls\n", "tool_call_id = tool_calls[0].id\n", "\n", "messages.append(message_call)\n", "\n", "messages.append(\n", "    {\n", "        \"role\":\"tool\",\n", "        \"tool_call_id\":tool_call_id,\n", "        \"name\":func_name,\n", "        \"content\":func_result\n", "    }\n", ")\n", "custom_print(messages)"]}, {"cell_type": "markdown", "id": "d175c5a0", "metadata": {}, "source": ["通过新封装的消息向大模型提问"]}, {"cell_type": "code", "execution_count": 22, "id": "535c7e35", "metadata": {}, "outputs": [{"data": {"text/plain": ["'user_base_info数据表中一共有891条数据记录。'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["second_response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages,\n", ")\n", "second_response.choices[0].message.content"]}, {"cell_type": "markdown", "id": "90f28fab", "metadata": {}, "source": ["# 4. 问题"]}, {"cell_type": "markdown", "id": "97863f89", "metadata": {}, "source": ["4.1 为什么大模型要进行二次调用"]}, {"cell_type": "markdown", "id": "05abcc2d", "metadata": {"vscode": {"languageId": "ini"}}, "source": ["大模型进行二次调用的原因是：第一次调用时，大模型根据用户问题判断需要调用外部工具（如数据库查询函数），生成工具调用请求；工具执行后返回结果，再将工具的执行结果与原始问题一起传递给大模型进行第二次调用，由大模型生成最终的自然语言答案。这种方式可以让大模型结合外部知识或数据，给出更准确、专业的回复。"]}, {"cell_type": "markdown", "id": "e15b0f15", "metadata": {}, "source": ["4.2 进行二次调用为什么要传递ChatCompletionMessage对象信息以及'role': 'tool'"]}, {"cell_type": "markdown", "id": "2a1e7c2c", "metadata": {"vscode": {"languageId": "ini"}}, "source": ["\n", "进行二次调用时，需要传递 `ChatCompletionMessage` 对象信息以及 `'role': 'tool'`，原因如下：\n", "\n", "1. ChatCompletionMessage对象：它包含了大模型上一次回复的详细内容（如工具调用请求、参数等），将其加入 `messages` 列表，可以让大模型完整了解对话上下文，包括它自己发起的工具调用请求。\n", "\n", "2. 'role': 'tool'：这个角色用于标记工具（如数据库查询函数）的执行结果。大模型通过识别 `'role': 'tool'` 的消息，知道这是外部工具返回的数据，可以结合这些数据和原始问题，生成最终的自然语言答案。\n", "\n", "这样设计可以让大模型具备“调用外部工具—获取结果—整合回复”的能力，实现更智能的问答流程。"]}, {"cell_type": "markdown", "id": "32be2ce8", "metadata": {}, "source": ["```\n", "作业: 自己声明一个函数 , auto_call(...)\n", "要求：用户发起一个请求(消息)，auto_call ---》 直接返回结果\n", "\n", "示例：\n", "用户：user_base_info表中存在多少条数据\n", "函数的返回值：user_base_info数据表中一共有891条记录。\n", "```"]}, {"cell_type": "code", "execution_count": 23, "id": "90921a64", "metadata": {}, "outputs": [], "source": ["import pymysql\n", "\n", "# 建立连接\n", "connection = pymysql.connect(\n", "    host='localhost',      # 数据库地址，本地就是localhost\n", "    user='root',           # 用户名\n", "    password='',    # 密码（如123456）\n", "    db='user_info',        # 数据库名\n", "    charset='utf8'         # 字符集，防止中文乱码\n", ")\n", "\n", "# 创建游标\n", "cursor = connection.cursor()\n"]}, {"cell_type": "code", "execution_count": 24, "id": "09eb93a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(('is_survived',), ('user_base_info',), ('user_extend_info',))\n"]}], "source": ["# 示例：查询表\n", "cursor.execute(\"SHOW TABLES\")\n", "print(cursor.fetchall())\n"]}, {"cell_type": "code", "execution_count": 25, "id": "c62cfd8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["要查询 `user_base_info` 数据表中的总记录数，可以使用以下SQL语句：\n", "\n", "```sql\n", "SELECT COUNT(*) AS total_records \n", "FROM user_base_info;\n", "```\n", "\n", "这条SQL会返回表中的总行数，列名为 `total_records`。\n", "\n", "注意事项：\n", "1. 如果表很大，COUNT(*) 可能会比较耗时\n", "2. 如果需要更精确的计数，某些数据库系统提供了估算函数（如PostgreSQL的pg_class.reltuples）\n", "3. 对于分区表，可能需要分别计算各分区后求和\n", "\n", "您使用的是哪种数据库系统？不同数据库可能有更优化的计数方法。\n"]}], "source": ["def auto_call(client, message):\n", "    \"\"\"\n", "    1. user_base_info表中存在多少条数据\n", "    2. user_extend_info表中有多少条数据\n", "    3. is_survived表里有多少条数据\n", "    4. user_base_info表中有多少女性\n", "    \"\"\"\n", "    # 构造 messages，向大模型提问\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家, 精通各种简单和复杂的数据分析\"},\n", "        {\"role\": \"user\", \"content\": f\"请问{message}\"}\n", "    ]\n", "\n", "    # 调用大模型\n", "    response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=messages,\n", "        # 如果你有 tools 相关配置，可以加上 tools+tools, tool_choice=\"auto\"\n", "    )\n", "    # 返回大模型回复内容\n", "    return response.choices[0].message.content\n", "\n", "# 示例测试\n", "print(auto_call(client, \"user_base_info数据表中一共有多少条数据\"))"]}, {"cell_type": "code", "execution_count": 26, "id": "ee1521d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型请求执行SQL查询: Function(arguments='{\"sql\":\"SELECT COUNT(*) AS total_records FROM user_base_info\"}', name='get_user_base_info')\n"]}], "source": ["def auto_call(client, message):\n", "    tools = [\n", "        {\n", "            'type': 'function',\n", "            'function': {\n", "                'name': 'get_user_base_info',\n", "                'description': '查询指定数据库下的相关表中的数据',\n", "                'parameters': {\n", "                    'type': 'object',\n", "                    'properties': {\n", "                        'sql': {\n", "                            'description': '必要参数，字符串类型的SQL语句',\n", "                            'type': 'string'\n", "                        },\n", "                        'username': {\n", "                            'description': '用户名,字符串类型',\n", "                            'type': 'string'\n", "                        },\n", "                        'password': {\n", "                            'description': '用户密码,字符串类型',\n", "                            'type': 'string'\n", "                        },\n", "                        'db': {\n", "                            'description': '需要连接的数据库名字,字符串类型',\n", "                            'type': 'string'\n", "                        }\n", "                    },\n", "                    'required': ['sql']\n", "                }\n", "            }\n", "        }\n", "    ]\n", "\n", "    # Construct messages\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": \"你是大数据领域的数据专家, 精通各种简单和复杂的数据分析\"},\n", "        {\"role\": \"user\", \"content\": f\"请问{message}\"}\n", "    ]\n", "\n", "    # Call the model with tools\n", "    response = client.chat.completions.create(\n", "        model=\"deepseek-chat\",\n", "        messages=messages,\n", "        tools=tools,\n", "        tool_choice=\"auto\"  # Let the model decide whether to use the function\n", "    )\n", "    \n", "    # Handle the response\n", "    response_message = response.choices[0].message\n", "    \n", "    # Check if the model wants to call a function\n", "    if response_message.tool_calls:\n", "        # Here you would normally execute the SQL query and return results\n", "        # For now, we'll just return the function call details\n", "        return f\"模型请求执行SQL查询: {response_message.tool_calls[0].function}\"\n", "    \n", "    return response_message.content\n", "\n", "# 示例测试\n", "print(auto_call(client, \"user_base_info数据表中一共有多少条数据\"))"]}, {"cell_type": "markdown", "id": "04dcffbd", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "zjou-2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}