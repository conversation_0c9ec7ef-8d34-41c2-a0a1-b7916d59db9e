{"cells": [{"cell_type": "markdown", "id": "a1c3c7d8", "metadata": {}, "source": ["# 2.1 MinMaxScaler"]}, {"cell_type": "code", "execution_count": 23, "id": "068fd485", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   0  1\n", "0  1  3\n", "1  4  4\n", "2  5  5\n", "=======================================\n", "[[0.   0.  ]\n", " [0.75 0.5 ]\n", " [1.   1.  ]]\n"]}], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "import numpy as np\n", "import pandas as pd\n", "\n", "data = [\n", "    [1,3],\n", "    [4,4],\n", "    [5,5]\n", "]\n", "\n", "df = pd.DataFrame(data)\n", "\n", "print(df)\n", "print(\"=======================================\")\n", "\n", "scaler = MinMaxScaler()\n", "\n", "scaler = scaler.fit(data)\n", "\n", "result = scaler.transform(data)\n", "\n", "print(result)"]}, {"cell_type": "code", "execution_count": 24, "id": "13a54a55", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   0  1\n", "0  1  3\n", "1  4  4\n", "2  5  5\n", "=======================================\n", "[[ 0.   0. ]\n", " [ 7.5  5. ]\n", " [10.  10. ]]\n"]}], "source": ["data = [\n", "    [1,3],\n", "    [4,4],\n", "    [5,5]\n", "]\n", "df = pd.DataFrame(data)\n", "print(df)\n", "print(\"=======================================\")\n", "scaler = MinMaxScaler(feature_range=(0,10))\n", "scaler = scaler.fit(data)\n", "result = scaler.transform(data)\n", "print(result)"]}, {"cell_type": "code", "execution_count": 25, "id": "6e1841c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0. ,  0. ],\n", "       [ 7.5,  5. ],\n", "       [10. , 10. ]])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["scaler.fit_transform(data)"]}, {"cell_type": "code", "execution_count": 26, "id": "a58cb62a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'numpy.ndarray'>\n"]}, {"data": {"text/plain": ["array([[1, 3],\n", "       [4, 4],\n", "       [5, 5]])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "\n", "data = [\n", "    [1,3],\n", "    [4,4],\n", "    [5,5]\n", "]\n", "\n", "data1 = np.array(data)\n", "\n", "print(type(data1))\n", "\n", "data1"]}, {"cell_type": "code", "execution_count": 27, "id": "d1890505", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 3])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["data1.min(axis=0)"]}, {"cell_type": "code", "execution_count": 32, "id": "e8d35ff8", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 0],\n", "       [3, 1],\n", "       [4, 2]])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["#[[1, 3],   [1, 3]   [1-1,3-3]   [0,0] [4, 4],    [1, 3]   [4-1,4-3]   [3,1][5, 5]]    [1, 3]   [5-1,5-3]   [4,2]\n", "data1 - data1.min(axis=0)"]}, {"cell_type": "code", "execution_count": 33, "id": "dd903a1a", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([5, 5])"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["data1.max(axis=0)"]}, {"cell_type": "code", "execution_count": 34, "id": "573833ac", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 3])"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["data1.min(axis=0)"]}, {"cell_type": "code", "execution_count": 35, "id": "966eea18", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([4, 2])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["data1.max(axis=0) - data1.min(axis=0)"]}, {"cell_type": "code", "execution_count": 38, "id": "c3687060", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.  , 0.  ],\n", "       [0.75, 0.5 ],\n", "       [1.  , 1.  ]])"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["#[             [0,0],      [4,2]          [0/4,0/2]      [0,0][3,1],      [4,2]          [3/4,1/2]      [0.75,0.5] [4,2]       [4,2]          [4/4,2/2]      [1,1]]\n", "(data1 - data1.min(axis=0)) / (data1.max(axis=0) - data1.min(axis=0))"]}, {"cell_type": "code", "execution_count": null, "id": "9409c1e6", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "data = [\n", "    [1,3],\n", "    [4,4],\n", "    [5,5]\n", "]\n", "data1 = np.array(data)\n", "result = (data1 - data1.min(axis=0)) / (data1.max(axis=0) - data1.min(axis=0))\n", "result"]}, {"cell_type": "markdown", "id": "c4236f07", "metadata": {}, "source": ["# 2.2 StandardScaler"]}, {"cell_type": "code", "execution_count": null, "id": "03842756", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from sklearn.preprocessing import StandardScaler\n", "data = [\n", "    [1,3],\n", "    [4,4],\n", "    [5,5]\n", "]\n", "scaler = StandardScaler()\n", "scaler = scaler.fit(data)\n", "result = scaler.transform(data)\n", "result"]}, {"cell_type": "markdown", "id": "6984496a", "metadata": {}, "source": ["# 2.3 数据标准化方式对比"]}, {"cell_type": "code", "execution_count": null, "id": "6164746b", "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "import numpy as np\n", "import pandas as pd\n", "data = [\n", "    [1,3],\n", "    [np.nan,4],\n", "    [5,5]\n", "]\n", "df = pd.DataFrame(data)\n", "print(df)\n", "print(\"====================================\")\n", "scaler = MinMaxScaler()\n", "scaler = scaler.fit(data)\n", "result = scaler.transform(data)\n", "print(result)"]}, {"cell_type": "markdown", "id": "033b5dba", "metadata": {}, "source": ["# 3.1 SimpleImputer"]}, {"cell_type": "code", "execution_count": 8, "id": "b83d3f11", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pixel0</th>\n", "      <th>pixel1</th>\n", "      <th>pixel2</th>\n", "      <th>pixel3</th>\n", "      <th>pixel4</th>\n", "      <th>pixel5</th>\n", "      <th>pixel6</th>\n", "      <th>pixel7</th>\n", "      <th>pixel8</th>\n", "      <th>pixel9</th>\n", "      <th>...</th>\n", "      <th>pixel774</th>\n", "      <th>pixel775</th>\n", "      <th>pixel776</th>\n", "      <th>pixel777</th>\n", "      <th>pixel778</th>\n", "      <th>pixel779</th>\n", "      <th>pixel780</th>\n", "      <th>pixel781</th>\n", "      <th>pixel782</th>\n", "      <th>pixel783</th>\n", "    </tr>\n", "    <tr>\n", "      <th>label</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 784 columns</p>\n", "</div>"], "text/plain": ["       pixel0  pixel1  pixel2  pixel3  pixel4  pixel5  pixel6  pixel7  pixel8  \\\n", "label                                                                           \n", "1           0       0       0       0       0       0       0       0       0   \n", "0           0       0       0       0       0       0       0       0       0   \n", "1           0       0       0       0       0       0       0       0       0   \n", "4           0       0       0       0       0       0       0       0       0   \n", "0           0       0       0       0       0       0       0       0       0   \n", "\n", "       pixel9  ...  pixel774  pixel775  pixel776  pixel777  pixel778  \\\n", "label          ...                                                     \n", "1           0  ...         0         0         0         0         0   \n", "0           0  ...         0         0         0         0         0   \n", "1           0  ...         0         0         0         0         0   \n", "4           0  ...         0         0         0         0         0   \n", "0           0  ...         0         0         0         0         0   \n", "\n", "       pixel779  pixel780  pixel781  pixel782  pixel783  \n", "label                                                    \n", "1             0         0         0         0         0  \n", "0             0         0         0         0         0  \n", "1             0         0         0         0         0  \n", "4             0         0         0         0         0  \n", "0             0         0         0         0         0  \n", "\n", "[5 rows x 784 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.impute import SimpleImputer\n", "import pandas as pd\n", "import numpy as np\n", "# 读取本地数据文件\n", "df = pd.read_csv(r'c:/Users/<USER>/Desktop/暑期实训2周/data1.csv', index_col=0)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 2, "id": "357e6d1d", "metadata": {}, "outputs": [{"data": {"text/plain": ["pandas.core.frame.DataFrame"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["type(df)"]}, {"cell_type": "code", "execution_count": 3, "id": "0e3291fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 42000 entries, 1 to 9\n", "Columns: 784 entries, pixel0 to pixel783\n", "dtypes: int64(784)\n", "memory usage: 251.5 MB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 18, "id": "ff815d8d", "metadata": {}, "outputs": [], "source": ["mean = SimpleImputer() # fit "]}, {"cell_type": "code", "execution_count": 19, "id": "9ff74782", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'Age'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3791\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3790\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3791\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine\u001b[38;5;241m.\u001b[39mget_loc(casted_key)\n\u001b[0;32m   3792\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:152\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:181\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:7080\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:7088\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Age'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[19], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28mtype\u001b[39m(df\u001b[38;5;241m.\u001b[39mloc[:,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAge\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1147\u001b[0m, in \u001b[0;36m_LocationIndexer.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   1145\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_scalar_access(key):\n\u001b[0;32m   1146\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_get_value(\u001b[38;5;241m*\u001b[39mkey, takeable\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_takeable)\n\u001b[1;32m-> 1147\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_tuple(key)\n\u001b[0;32m   1148\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[0;32m   1149\u001b[0m     \u001b[38;5;66;03m# we by definition only have the 0th axis\u001b[39;00m\n\u001b[0;32m   1150\u001b[0m     axis \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxis \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;241m0\u001b[39m\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1330\u001b[0m, in \u001b[0;36m_LocIndexer._getitem_tuple\u001b[1;34m(self, tup)\u001b[0m\n\u001b[0;32m   1328\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m suppress(IndexingError):\n\u001b[0;32m   1329\u001b[0m     tup \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_expand_ellipsis(tup)\n\u001b[1;32m-> 1330\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_lowerdim(tup)\n\u001b[0;32m   1332\u001b[0m \u001b[38;5;66;03m# no multi-index, so validate all of the indexers\u001b[39;00m\n\u001b[0;32m   1333\u001b[0m tup \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_tuple_indexer(tup)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1039\u001b[0m, in \u001b[0;36m_LocationIndexer._getitem_lowerdim\u001b[1;34m(self, tup)\u001b[0m\n\u001b[0;32m   1035\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i, key \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(tup):\n\u001b[0;32m   1036\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_label_like(key):\n\u001b[0;32m   1037\u001b[0m         \u001b[38;5;66;03m# We don't need to check for tuples here because those are\u001b[39;00m\n\u001b[0;32m   1038\u001b[0m         \u001b[38;5;66;03m#  caught by the _is_nested_tuple_indexer check above.\u001b[39;00m\n\u001b[1;32m-> 1039\u001b[0m         section \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_axis(key, axis\u001b[38;5;241m=\u001b[39mi)\n\u001b[0;32m   1041\u001b[0m         \u001b[38;5;66;03m# We should never have a scalar section here, because\u001b[39;00m\n\u001b[0;32m   1042\u001b[0m         \u001b[38;5;66;03m#  _getitem_lowerdim is only called after a check for\u001b[39;00m\n\u001b[0;32m   1043\u001b[0m         \u001b[38;5;66;03m#  is_scalar_access, which that would be.\u001b[39;00m\n\u001b[0;32m   1044\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m section\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mndim:\n\u001b[0;32m   1045\u001b[0m             \u001b[38;5;66;03m# we're in the middle of slicing through a MultiIndex\u001b[39;00m\n\u001b[0;32m   1046\u001b[0m             \u001b[38;5;66;03m# revise the key wrt to `section` by inserting an _NS\u001b[39;00m\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1393\u001b[0m, in \u001b[0;36m_LocIndexer._getitem_axis\u001b[1;34m(self, key, axis)\u001b[0m\n\u001b[0;32m   1391\u001b[0m \u001b[38;5;66;03m# fall thru to straight lookup\u001b[39;00m\n\u001b[0;32m   1392\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_key(key, axis)\n\u001b[1;32m-> 1393\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_label(key, axis\u001b[38;5;241m=\u001b[39maxis)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1343\u001b[0m, in \u001b[0;36m_LocIndexer._get_label\u001b[1;34m(self, label, axis)\u001b[0m\n\u001b[0;32m   1341\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_get_label\u001b[39m(\u001b[38;5;28mself\u001b[39m, label, axis: AxisInt):\n\u001b[0;32m   1342\u001b[0m     \u001b[38;5;66;03m# GH#5567 this will fail if the label is not present in the axis.\u001b[39;00m\n\u001b[1;32m-> 1343\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39mxs(label, axis\u001b[38;5;241m=\u001b[39maxis)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\generic.py:4222\u001b[0m, in \u001b[0;36mNDFrame.xs\u001b[1;34m(self, key, axis, level, drop_level)\u001b[0m\n\u001b[0;32m   4220\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m axis \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4221\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m drop_level:\n\u001b[1;32m-> 4222\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m[key]\n\u001b[0;32m   4223\u001b[0m     index \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\n\u001b[0;32m   4224\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\frame.py:3893\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3891\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   3892\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 3893\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mget_loc(key)\n\u001b[0;32m   3894\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   3895\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3798\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3793\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3794\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3795\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3796\u001b[0m     ):\n\u001b[0;32m   3797\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3798\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3799\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3800\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3801\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3802\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3803\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Age'"]}], "source": ["type(df.loc[:,'Age'])"]}, {"cell_type": "code", "execution_count": 7, "id": "a75e146b", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'Age'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3791\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3790\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3791\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine\u001b[38;5;241m.\u001b[39mget_loc(casted_key)\n\u001b[0;32m   3792\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:152\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:181\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:7080\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:7088\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Age'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28mtype\u001b[39m(df\u001b[38;5;241m.\u001b[39mloc[:,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAge\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mvalues)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1147\u001b[0m, in \u001b[0;36m_LocationIndexer.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   1145\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_scalar_access(key):\n\u001b[0;32m   1146\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_get_value(\u001b[38;5;241m*\u001b[39mkey, takeable\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_takeable)\n\u001b[1;32m-> 1147\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_tuple(key)\n\u001b[0;32m   1148\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[0;32m   1149\u001b[0m     \u001b[38;5;66;03m# we by definition only have the 0th axis\u001b[39;00m\n\u001b[0;32m   1150\u001b[0m     axis \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxis \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;241m0\u001b[39m\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1330\u001b[0m, in \u001b[0;36m_LocIndexer._getitem_tuple\u001b[1;34m(self, tup)\u001b[0m\n\u001b[0;32m   1328\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m suppress(IndexingError):\n\u001b[0;32m   1329\u001b[0m     tup \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_expand_ellipsis(tup)\n\u001b[1;32m-> 1330\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_lowerdim(tup)\n\u001b[0;32m   1332\u001b[0m \u001b[38;5;66;03m# no multi-index, so validate all of the indexers\u001b[39;00m\n\u001b[0;32m   1333\u001b[0m tup \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_tuple_indexer(tup)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1039\u001b[0m, in \u001b[0;36m_LocationIndexer._getitem_lowerdim\u001b[1;34m(self, tup)\u001b[0m\n\u001b[0;32m   1035\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i, key \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(tup):\n\u001b[0;32m   1036\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_label_like(key):\n\u001b[0;32m   1037\u001b[0m         \u001b[38;5;66;03m# We don't need to check for tuples here because those are\u001b[39;00m\n\u001b[0;32m   1038\u001b[0m         \u001b[38;5;66;03m#  caught by the _is_nested_tuple_indexer check above.\u001b[39;00m\n\u001b[1;32m-> 1039\u001b[0m         section \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_axis(key, axis\u001b[38;5;241m=\u001b[39mi)\n\u001b[0;32m   1041\u001b[0m         \u001b[38;5;66;03m# We should never have a scalar section here, because\u001b[39;00m\n\u001b[0;32m   1042\u001b[0m         \u001b[38;5;66;03m#  _getitem_lowerdim is only called after a check for\u001b[39;00m\n\u001b[0;32m   1043\u001b[0m         \u001b[38;5;66;03m#  is_scalar_access, which that would be.\u001b[39;00m\n\u001b[0;32m   1044\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m section\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mndim:\n\u001b[0;32m   1045\u001b[0m             \u001b[38;5;66;03m# we're in the middle of slicing through a MultiIndex\u001b[39;00m\n\u001b[0;32m   1046\u001b[0m             \u001b[38;5;66;03m# revise the key wrt to `section` by inserting an _NS\u001b[39;00m\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1393\u001b[0m, in \u001b[0;36m_LocIndexer._getitem_axis\u001b[1;34m(self, key, axis)\u001b[0m\n\u001b[0;32m   1391\u001b[0m \u001b[38;5;66;03m# fall thru to straight lookup\u001b[39;00m\n\u001b[0;32m   1392\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_key(key, axis)\n\u001b[1;32m-> 1393\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_label(key, axis\u001b[38;5;241m=\u001b[39maxis)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1343\u001b[0m, in \u001b[0;36m_LocIndexer._get_label\u001b[1;34m(self, label, axis)\u001b[0m\n\u001b[0;32m   1341\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_get_label\u001b[39m(\u001b[38;5;28mself\u001b[39m, label, axis: AxisInt):\n\u001b[0;32m   1342\u001b[0m     \u001b[38;5;66;03m# GH#5567 this will fail if the label is not present in the axis.\u001b[39;00m\n\u001b[1;32m-> 1343\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39mxs(label, axis\u001b[38;5;241m=\u001b[39maxis)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\generic.py:4222\u001b[0m, in \u001b[0;36mNDFrame.xs\u001b[1;34m(self, key, axis, level, drop_level)\u001b[0m\n\u001b[0;32m   4220\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m axis \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4221\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m drop_level:\n\u001b[1;32m-> 4222\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m[key]\n\u001b[0;32m   4223\u001b[0m     index \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\n\u001b[0;32m   4224\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\frame.py:3893\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3891\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   3892\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 3893\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mget_loc(key)\n\u001b[0;32m   3894\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   3895\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3798\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3793\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3794\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3795\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3796\u001b[0m     ):\n\u001b[0;32m   3797\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3798\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3799\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3800\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3801\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3802\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3803\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Age'"]}], "source": ["type(df.loc[:,'Age'].values)"]}, {"cell_type": "code", "execution_count": null, "id": "56dbaeab", "metadata": {}, "outputs": [], "source": ["df.loc[:,'Age'].values.reshape(-1,1)"]}, {"cell_type": "code", "execution_count": null, "id": "2059137b", "metadata": {}, "outputs": [], "source": ["age_mean = mean.fit_transform(df.loc[:,'Age'].values.reshape(-1,1))\n", "age_mean"]}, {"cell_type": "code", "execution_count": null, "id": "8e7264db", "metadata": {}, "outputs": [], "source": ["1              =    1\n", "2                   2\n", "3                   3\n", "df.loc[:,'Age'] = age_mean"]}, {"cell_type": "code", "execution_count": 24, "id": "7b464e26", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 891 entries, 0 to 890\n", "Data columns (total 5 columns):\n", " #   Column    Non-Null Count  Dtype  \n", "---  ------    --------------  -----  \n", " 0   lable     891 non-null    int64  \n", " 1   Age       891 non-null    float64\n", " 2   Sex       891 non-null    object \n", " 3   Embarked  889 non-null    object \n", " 4   Survived  891 non-null    object \n", "dtypes: float64(1), int64(1), object(3)\n", "memory usage: 34.9+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 9, "id": "1774033e", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'Age'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3791\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3790\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3791\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine\u001b[38;5;241m.\u001b[39mget_loc(casted_key)\n\u001b[0;32m   3792\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:152\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:181\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:7080\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:7088\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Age'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[9], line 8\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[38;5;66;03m# 用均值填充Age列的缺失值\u001b[39;00m\n\u001b[0;32m      7\u001b[0m mean \u001b[38;5;241m=\u001b[39m SimpleImputer()\n\u001b[1;32m----> 8\u001b[0m age \u001b[38;5;241m=\u001b[39m df\u001b[38;5;241m.\u001b[39mloc[:,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAge\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mvalues\u001b[38;5;241m.\u001b[39mreshape(\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m,\u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m      9\u001b[0m age_mean \u001b[38;5;241m=\u001b[39m mean\u001b[38;5;241m.\u001b[39mfit_transform(age)\n\u001b[0;32m     10\u001b[0m df\u001b[38;5;241m.\u001b[39mloc[:,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAge\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m age_mean\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1147\u001b[0m, in \u001b[0;36m_LocationIndexer.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   1145\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_scalar_access(key):\n\u001b[0;32m   1146\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_get_value(\u001b[38;5;241m*\u001b[39mkey, takeable\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_takeable)\n\u001b[1;32m-> 1147\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_tuple(key)\n\u001b[0;32m   1148\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[0;32m   1149\u001b[0m     \u001b[38;5;66;03m# we by definition only have the 0th axis\u001b[39;00m\n\u001b[0;32m   1150\u001b[0m     axis \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxis \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;241m0\u001b[39m\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1330\u001b[0m, in \u001b[0;36m_LocIndexer._getitem_tuple\u001b[1;34m(self, tup)\u001b[0m\n\u001b[0;32m   1328\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m suppress(IndexingError):\n\u001b[0;32m   1329\u001b[0m     tup \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_expand_ellipsis(tup)\n\u001b[1;32m-> 1330\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_lowerdim(tup)\n\u001b[0;32m   1332\u001b[0m \u001b[38;5;66;03m# no multi-index, so validate all of the indexers\u001b[39;00m\n\u001b[0;32m   1333\u001b[0m tup \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_tuple_indexer(tup)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1039\u001b[0m, in \u001b[0;36m_LocationIndexer._getitem_lowerdim\u001b[1;34m(self, tup)\u001b[0m\n\u001b[0;32m   1035\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i, key \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(tup):\n\u001b[0;32m   1036\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_label_like(key):\n\u001b[0;32m   1037\u001b[0m         \u001b[38;5;66;03m# We don't need to check for tuples here because those are\u001b[39;00m\n\u001b[0;32m   1038\u001b[0m         \u001b[38;5;66;03m#  caught by the _is_nested_tuple_indexer check above.\u001b[39;00m\n\u001b[1;32m-> 1039\u001b[0m         section \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_axis(key, axis\u001b[38;5;241m=\u001b[39mi)\n\u001b[0;32m   1041\u001b[0m         \u001b[38;5;66;03m# We should never have a scalar section here, because\u001b[39;00m\n\u001b[0;32m   1042\u001b[0m         \u001b[38;5;66;03m#  _getitem_lowerdim is only called after a check for\u001b[39;00m\n\u001b[0;32m   1043\u001b[0m         \u001b[38;5;66;03m#  is_scalar_access, which that would be.\u001b[39;00m\n\u001b[0;32m   1044\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m section\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mndim:\n\u001b[0;32m   1045\u001b[0m             \u001b[38;5;66;03m# we're in the middle of slicing through a MultiIndex\u001b[39;00m\n\u001b[0;32m   1046\u001b[0m             \u001b[38;5;66;03m# revise the key wrt to `section` by inserting an _NS\u001b[39;00m\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1393\u001b[0m, in \u001b[0;36m_LocIndexer._getitem_axis\u001b[1;34m(self, key, axis)\u001b[0m\n\u001b[0;32m   1391\u001b[0m \u001b[38;5;66;03m# fall thru to straight lookup\u001b[39;00m\n\u001b[0;32m   1392\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_key(key, axis)\n\u001b[1;32m-> 1393\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_label(key, axis\u001b[38;5;241m=\u001b[39maxis)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1343\u001b[0m, in \u001b[0;36m_LocIndexer._get_label\u001b[1;34m(self, label, axis)\u001b[0m\n\u001b[0;32m   1341\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_get_label\u001b[39m(\u001b[38;5;28mself\u001b[39m, label, axis: AxisInt):\n\u001b[0;32m   1342\u001b[0m     \u001b[38;5;66;03m# GH#5567 this will fail if the label is not present in the axis.\u001b[39;00m\n\u001b[1;32m-> 1343\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39mxs(label, axis\u001b[38;5;241m=\u001b[39maxis)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\generic.py:4222\u001b[0m, in \u001b[0;36mNDFrame.xs\u001b[1;34m(self, key, axis, level, drop_level)\u001b[0m\n\u001b[0;32m   4220\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m axis \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4221\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m drop_level:\n\u001b[1;32m-> 4222\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m[key]\n\u001b[0;32m   4223\u001b[0m     index \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\n\u001b[0;32m   4224\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\frame.py:3893\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3891\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   3892\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 3893\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mget_loc(key)\n\u001b[0;32m   3894\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   3895\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3798\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3793\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3794\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3795\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3796\u001b[0m     ):\n\u001b[0;32m   3797\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3798\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3799\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3800\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3801\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3802\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3803\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Age'"]}], "source": ["from sklearn.impute import SimpleImputer\n", "import pandas as pd\n", "import numpy as np\n", "# 读取本地数据文件\n", "df = pd.read_csv(r'c:/Users/<USER>/Desktop/暑期实训2周/data1.csv', index_col=0)\n", "# 用均值填充Age列的缺失值\n", "mean = SimpleImputer()\n", "age = df.loc[:,'Age'].values.reshape(-1,1)\n", "age_mean = mean.fit_transform(age)\n", "df.loc[:,'Age'] = age_mean\n", "df.info()\n", "df.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "895575f1", "metadata": {}, "outputs": [], "source": ["from sklearn.impute import SimpleImputer\n", "import pandas\n", "import numpy\n", "df = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\day01\\数据预处理数据集\\data2.csv\",index_col=0)\n", "constant = SimpleImputer(strategy='constant',fill_value=100)\n", "age = df.loc[:,'Age'].values.reshape(-1,1)\n", "age_constant = constant.fit_transform(age)\n", "df.loc[:,'Age'] = age_constant\n", "df.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "075db996", "metadata": {}, "outputs": [], "source": ["from sklearn.impute import SimpleImputer\n", "mean = SimpleImputer()\n", "most_frequent = SimpleImputer(strategy='most_frequent')\n", "embarked = df.loc[:,'Embarked'].values.reshape(-1,1)\n", "embarked_most_frequent = most_frequent.fit_transform(embarked)\n", "embarked_most_frequent = most_frequent.fit_transform(embarked)\n", "df.loc[:,'Embarked'] = embarked_most_frequent\n", "df.info()\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a673c9be", "metadata": {}, "outputs": [], "source": ["df.info()"]}, {"cell_type": "markdown", "id": "c4a42243", "metadata": {}, "source": ["# 3.2 LabelEncoder"]}, {"cell_type": "code", "execution_count": null, "id": "c1afc211", "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import LabelEncoder\n", "y = df.loc[:,'Survived']\n", "ll = LabelEncoder()\n", "ll = ll.fit(y)\n", "print(ll.classes_)\n", "print(\"===============================================================\")\n", "result = ll.transform(y)\n", "print(result[1:20])\n", "print(\"===============================================================\")\n", "print(set(zip(y.values[1:20],result[1:20])))\n", "print(\"===============================================================\")\n", "df.loc[:,'Survived'] = result\n", "df.info()\n", "print(\"===============================================================\")\n", "df.head()"]}, {"cell_type": "markdown", "id": "a05d1aa6", "metadata": {}, "source": ["# 3.3 OrdinalEncoder"]}, {"cell_type": "code", "execution_count": null, "id": "a4c79119", "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import OrdinalEncoder\n", "df1 = df.copy()\n", "ordinal = OrdinalEncoder()\n", "ordinal = ordinal.fit(pd.DataFrame(df1.loc[:,'Sex']))\n", "result = ordinal.transform(pd.DataFrame(df1.loc[:,'Sex']))\n", "print(result)\n", "df1.loc[:,'Sex'] = result\n", "\n", "ordinal = ordinal.fit(pd.DataFrame(df1.loc[:,'Embarked']))\n", "result = ordinal.transform(pd.DataFrame(df1.loc[:,'Embarked']))\n", "df1.loc[:,'Embarked'] = result\n", "print(\"=====================================================================\")\n", "print(df1.head())\n", "print(type(df1.loc[:,'Sex']))\n", "print(type(result))"]}, {"cell_type": "markdown", "id": "845826ec", "metadata": {}, "source": ["# 3.4 OneHotEncoder"]}, {"cell_type": "code", "execution_count": 17, "id": "ba2b3517", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "\"None of [Index(['Sex', 'Embarked'], dtype='object')] are in the [columns]\"", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[17], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01ms<PERSON>arn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpreprocessing\u001b[39;00m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m OneHotEncoder\n\u001b[1;32m----> 2\u001b[0m df2 \u001b[38;5;241m=\u001b[39m df\u001b[38;5;241m.\u001b[39mloc[:,[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSex\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mEmbarked\u001b[39m\u001b[38;5;124m'\u001b[39m]]\n\u001b[0;32m      3\u001b[0m onehot \u001b[38;5;241m=\u001b[39m OneHotEncoder(categories\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mauto\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mfit(df2)\n\u001b[0;32m      4\u001b[0m result \u001b[38;5;241m=\u001b[39m onehot\u001b[38;5;241m.\u001b[39mtransform(df2)\u001b[38;5;241m.\u001b[39mtoarray()\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1147\u001b[0m, in \u001b[0;36m_LocationIndexer.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   1145\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_scalar_access(key):\n\u001b[0;32m   1146\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_get_value(\u001b[38;5;241m*\u001b[39mkey, takeable\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_takeable)\n\u001b[1;32m-> 1147\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_tuple(key)\n\u001b[0;32m   1148\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[0;32m   1149\u001b[0m     \u001b[38;5;66;03m# we by definition only have the 0th axis\u001b[39;00m\n\u001b[0;32m   1150\u001b[0m     axis \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxis \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;241m0\u001b[39m\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1339\u001b[0m, in \u001b[0;36m_LocIndexer._getitem_tuple\u001b[1;34m(self, tup)\u001b[0m\n\u001b[0;32m   1336\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_multi_take_opportunity(tup):\n\u001b[0;32m   1337\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_multi_take(tup)\n\u001b[1;32m-> 1339\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_tuple_same_dim(tup)\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:994\u001b[0m, in \u001b[0;36m_LocationIndexer._getitem_tuple_same_dim\u001b[1;34m(self, tup)\u001b[0m\n\u001b[0;32m    991\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m com\u001b[38;5;241m.\u001b[39mis_null_slice(key):\n\u001b[0;32m    992\u001b[0m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[1;32m--> 994\u001b[0m retval \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(retval, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname)\u001b[38;5;241m.\u001b[39m_getitem_axis(key, axis\u001b[38;5;241m=\u001b[39mi)\n\u001b[0;32m    995\u001b[0m \u001b[38;5;66;03m# We should never have retval.ndim < self.ndim, as that should\u001b[39;00m\n\u001b[0;32m    996\u001b[0m \u001b[38;5;66;03m#  be handled by the _getitem_lowerdim call above.\u001b[39;00m\n\u001b[0;32m    997\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m retval\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mndim\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1382\u001b[0m, in \u001b[0;36m_LocIndexer._getitem_axis\u001b[1;34m(self, key, axis)\u001b[0m\n\u001b[0;32m   1379\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(key, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mndim\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m key\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   1380\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>ueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCannot index with multidimensional key\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m-> 1382\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_iterable(key, axis\u001b[38;5;241m=\u001b[39maxis)\n\u001b[0;32m   1384\u001b[0m \u001b[38;5;66;03m# nested tuple slicing\u001b[39;00m\n\u001b[0;32m   1385\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_nested_tuple(key, labels):\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1322\u001b[0m, in \u001b[0;36m_LocIndexer._getitem_iterable\u001b[1;34m(self, key, axis)\u001b[0m\n\u001b[0;32m   1319\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_key(key, axis)\n\u001b[0;32m   1321\u001b[0m \u001b[38;5;66;03m# A collection of keys\u001b[39;00m\n\u001b[1;32m-> 1322\u001b[0m keyarr, indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_listlike_indexer(key, axis)\n\u001b[0;32m   1323\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_reindex_with_indexers(\n\u001b[0;32m   1324\u001b[0m     {axis: [keyarr, indexer]}, copy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, allow_dups\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m   1325\u001b[0m )\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexing.py:1520\u001b[0m, in \u001b[0;36m_LocIndexer._get_listlike_indexer\u001b[1;34m(self, key, axis)\u001b[0m\n\u001b[0;32m   1517\u001b[0m ax \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_get_axis(axis)\n\u001b[0;32m   1518\u001b[0m axis_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_get_axis_name(axis)\n\u001b[1;32m-> 1520\u001b[0m keyarr, indexer \u001b[38;5;241m=\u001b[39m ax\u001b[38;5;241m.\u001b[39m_get_indexer_strict(key, axis_name)\n\u001b[0;32m   1522\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m keyarr, indexer\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:6115\u001b[0m, in \u001b[0;36mIndex._get_indexer_strict\u001b[1;34m(self, key, axis_name)\u001b[0m\n\u001b[0;32m   6112\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[0;32m   6113\u001b[0m     keyarr, indexer, new_indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reindex_non_unique(keyarr)\n\u001b[1;32m-> 6115\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_raise_if_missing(keyarr, indexer, axis_name)\n\u001b[0;32m   6117\u001b[0m keyarr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtake(indexer)\n\u001b[0;32m   6118\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, Index):\n\u001b[0;32m   6119\u001b[0m     \u001b[38;5;66;03m# GH 42790 - Preserve name from an Index\u001b[39;00m\n", "File \u001b[1;32mc:\\anaconda\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:6176\u001b[0m, in \u001b[0;36mIndex._raise_if_missing\u001b[1;34m(self, key, indexer, axis_name)\u001b[0m\n\u001b[0;32m   6174\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m use_interval_msg:\n\u001b[0;32m   6175\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[1;32m-> 6176\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNone of [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m] are in the [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00maxis_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   6178\u001b[0m not_found \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(ensure_index(key)[missing_mask\u001b[38;5;241m.\u001b[39mnonzero()[\u001b[38;5;241m0\u001b[39m]]\u001b[38;5;241m.\u001b[39munique())\n\u001b[0;32m   6179\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnot_found\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not in index\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: \"None of [Index(['Sex', 'Embarked'], dtype='object')] are in the [columns]\""]}], "source": ["from sklearn.preprocessing import OneHotEncoder\n", "df2 = df.loc[:,['Sex','Embarked']]\n", "onehot = OneHotEncoder(categories='auto').fit(df2)\n", "result = onehot.transform(df2).toarray()\n", "print(result)\n", "print(onehot.get_feature_names_out())\n", "df2 = df.copy()\n", "df3 = pd.concat([df2,pd.DataFrame(result)],axis=1)\n", "print(df3)\n", "df3 = df3.drop(['Sex','Embarked'],axis=1)\n", "df3.columns = ['Age','Survived','Sex_female','Sex_male','Embarked_C','Embarked_Q','Embarked_S']\n", "df3.head()"]}, {"cell_type": "markdown", "id": "d64023f0", "metadata": {}, "source": ["# 1 方差过滤"]}, {"cell_type": "code", "execution_count": null, "id": "16a12927", "metadata": {}, "outputs": [], "source": ["from sklearn.feature_selection import VarianceThreshold\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.model_selection import cross_val_score\n", "from sklearn.model_selection import train_test_split\n", "import numpy as np\n", "import pandas as pd\n", "df = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\day01\\数据预处理数据集\\data1.csv\")\n", "X = df.iloc[:,1:]\n", "y = df.iloc[:,0]\n", "Xtrain,Xtest,Ytrain,Ytest = train_test_split(X,y,test_size=0.3,random_state=100)\n", "rfc = RandomForestClassifier(random_state=100)\n", "dfc = DecisionTreeClassifier(random_state=100)\n", "rfc = rfc.fit(Xtrain,Ytrain)\n", "dfc = dfc.fit(Xtrain,Ytrain)\n", "print(rfc.score(Xtest,Ytest))\n", "print(dfc.score(Xtest,Ytest))"]}, {"cell_type": "code", "execution_count": null, "id": "d6acce0c", "metadata": {}, "outputs": [], "source": ["selector = VarianceThreshold(np.median(X.var().values))\n", "X0 = selector.fit_transform(X)\n", "Xtrain,Xtest,Ytrain,Ytest = train_test_split(X0,y,test_size=0.3,random_state=100)\n", "rfc = RandomForestClassifier(random_state=100)\n", "dfc = DecisionTreeClassifier(random_state=100)\n", "rfc = rfc.fit(Xtrain,Ytrain)\n", "dfc = dfc.fit(Xtrain,Ytrain)\n", "print(rfc.score(Xtest,Ytest))\n", "print(dfc.score(Xtest,Ytest))"]}, {"cell_type": "markdown", "id": "e6039e06", "metadata": {}, "source": ["# 2 特征与标签相关性检测"]}, {"cell_type": "code", "execution_count": 4, "id": "c1863baa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting mat<PERSON><PERSON><PERSON><PERSON>\n", "  Using cached matplotlib-3.10.3-cp311-cp311-win_amd64.whl.metadata (11 kB)\n", "Collecting contourpy>=1.0.1 (from matplotlib)\n", "  Using cached contourpy-1.3.2-cp311-cp311-win_amd64.whl.metadata (5.5 kB)\n", "Collecting cycler>=0.10 (from matplotlib)\n", "  Using cached cycler-0.12.1-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting fonttools>=4.22.0 (from matplotlib)\n", "  Using cached fonttools-4.58.5-cp311-cp311-win_amd64.whl.metadata (109 kB)\n", "Collecting kiwisolver>=1.3.1 (from matplotlib)\n", "  Using cached kiwisolver-1.4.8-cp311-cp311-win_amd64.whl.metadata (6.3 kB)\n", "Collecting numpy>=1.23 (from matplotlib)\n", "  Using cached numpy-2.3.1-cp311-cp311-win_amd64.whl.metadata (60 kB)\n", "Requirement already satisfied: packaging>=20.0 in c:\\anaconda\\envs\\zjou2025\\lib\\site-packages (from matplotlib) (25.0)\n", "Collecting pillow>=8 (from matplotlib)\n", "  Using cached pillow-11.3.0-cp311-cp311-win_amd64.whl.metadata (9.2 kB)\n", "Collecting pyparsing>=2.3.1 (from matplotlib)\n", "  Using cached pyparsing-3.2.3-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: python-dateutil>=2.7 in c:\\anaconda\\envs\\zjou2025\\lib\\site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: six>=1.5 in c:\\anaconda\\envs\\zjou2025\\lib\\site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n", "Downloading matplotlib-3.10.3-cp311-cp311-win_amd64.whl (8.1 MB)\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   -- ------------------------------------- 0.5/8.1 MB 16.4 kB/s eta 0:07:42\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   --- ------------------------------------ 0.8/8.1 MB 16.9 kB/s eta 0:07:10\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ----- ---------------------------------- 1.0/8.1 MB 18.2 kB/s eta 0:06:25\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------ --------------------------------- 1.3/8.1 MB 19.6 kB/s eta 0:05:45\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   ------- -------------------------------- 1.6/8.1 MB 17.8 kB/s eta 0:06:06\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   --------- ------------------------------ 1.8/8.1 MB 17.5 kB/s eta 0:05:57\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ---------- ----------------------------- 2.1/8.1 MB 17.5 kB/s eta 0:05:42\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ----------- ---------------------------- 2.4/8.1 MB 19.1 kB/s eta 0:04:59\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   ------------- -------------------------- 2.6/8.1 MB 21.5 kB/s eta 0:04:14\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   -------------- ------------------------- 2.9/8.1 MB 19.7 kB/s eta 0:04:23\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   --------------- ------------------------ 3.1/8.1 MB 19.8 kB/s eta 0:04:09\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ---------------- ----------------------- 3.4/8.1 MB 20.2 kB/s eta 0:03:51\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------ --------------------- 3.7/8.1 MB 18.8 kB/s eta 0:03:54\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   ------------------- -------------------- 3.9/8.1 MB 21.2 kB/s eta 0:03:16\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   -------------------- ------------------- 4.2/8.1 MB 26.0 kB/s eta 0:02:29\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ---------------------- ----------------- 4.5/8.1 MB 24.5 kB/s eta 0:02:28\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ----------------------- ---------------- 4.7/8.1 MB 24.6 kB/s eta 0:02:17\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------ --------------- 5.0/8.1 MB 20.9 kB/s eta 0:02:28\n", "   ------------------------- -------------- 5.1/8.1 MB 16.6 kB/s eta 0:03:00\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR: Could not install packages due to an OSError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\pip-unpack-gt4q_y9v\\\\matplotlib-3.10.3-cp311-cp311-win_amd64.whl'\n", "Consider using the `--user` option or check the permissions.\n", "\n"]}], "source": ["%pip install matplotlib"]}, {"cell_type": "code", "execution_count": 2, "id": "4a5d74b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR: Ignored the following versions that require a different python version: 1.21.2 Requires-Python >=3.7,<3.11; 1.21.3 Requires-Python >=3.7,<3.11; 1.21.4 Requires-Python >=3.7,<3.11; 1.21.5 Requires-Python >=3.7,<3.11; 1.21.6 Requires-Python >=3.7,<3.11\n", "ERROR: Could not find a version that satisfies the requirement numpy==1.21.5 (from versions: 1.3.0, 1.4.1, 1.5.0, 1.5.1, 1.6.0, 1.6.1, 1.6.2, 1.7.0, 1.7.1, 1.7.2, 1.8.0, 1.8.1, 1.8.2, 1.9.0, 1.9.1, 1.9.2, 1.9.3, 1.10.0.post2, 1.10.1, 1.10.2, 1.10.4, 1.11.0, 1.11.1, 1.11.2, 1.11.3, 1.12.0, 1.12.1, 1.13.0, 1.13.1, 1.13.3, 1.14.0, 1.14.1, 1.14.2, 1.14.3, 1.14.4, 1.14.5, 1.14.6, 1.15.0, 1.15.1, 1.15.2, 1.15.3, 1.15.4, 1.16.0, 1.16.1, 1.16.2, 1.16.3, 1.16.4, 1.16.5, 1.16.6, 1.17.0, 1.17.1, 1.17.2, 1.17.3, 1.17.4, 1.17.5, 1.18.0, 1.18.1, 1.18.2, 1.18.3, 1.18.4, 1.18.5, 1.19.0, 1.19.1, 1.19.2, 1.19.3, 1.19.4, 1.19.5, 1.20.0, 1.20.1, 1.20.2, 1.20.3, 1.21.0, 1.21.1, 1.22.0, 1.22.1, 1.22.2, 1.22.3, 1.22.4, 1.23.0, 1.23.1, 1.23.2, 1.23.3, 1.23.4, 1.23.5, 1.24.0, 1.24.1, 1.24.2, 1.24.3, 1.24.4, 1.25.0, 1.25.1, 1.25.2, 1.26.0, 1.26.1, 1.26.2, 1.26.3, 1.26.4, 2.0.0, 2.0.1, 2.0.2, 2.1.0, 2.1.1, 2.1.2, 2.1.3, 2.2.0rc1, 2.2.0, 2.2.1, 2.2.2, 2.2.3, 2.2.4, 2.2.5, 2.2.6, 2.3.0rc1, 2.3.0, 2.3.1)\n", "ERROR: No matching distribution found for numpy==1.21.5\n"]}], "source": ["%pip install numpy==1.21.5 pandas==1.4.4 scikit-learn==1.0.2"]}, {"cell_type": "code", "execution_count": null, "id": "2ddca05f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["^C\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"ename": "ModuleNotFoundError", "evalue": "No module named 's<PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m get_ipython().run_line_magic(\u001b[33m'\u001b[39m\u001b[33mpip\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33minstall scikit-learn matplotlib\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msklearn\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mfeature_selection\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m mutual_info_classif \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[32m      5\u001b[39m MIC = mutual_info_classif\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msklearn\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mfeature_selection\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 's<PERSON>arn'"]}, {"name": "stdout", "output_type": "stream", "text": ["Collecting scikit-learn\n", "  Using cached scikit_learn-1.7.0-cp311-cp311-win_amd64.whl.metadata (14 kB)\n", "Collecting mat<PERSON><PERSON><PERSON><PERSON>\n", "  Downloading matplotlib-3.10.3-cp311-cp311-win_amd64.whl.metadata (11 kB)\n", "Collecting numpy>=1.22.0 (from scikit-learn)\n", "  Using cached numpy-2.3.1-cp311-cp311-win_amd64.whl.metadata (60 kB)\n", "Collecting scipy>=1.8.0 (from scikit-learn)\n", "  Using cached scipy-1.16.0-cp311-cp311-win_amd64.whl.metadata (60 kB)\n", "Collecting joblib>=1.2.0 (from scikit-learn)\n", "  Using cached joblib-1.5.1-py3-none-any.whl.metadata (5.6 kB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR: Could not install packages due to an OSError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\pip-unpack-i13uj9ac\\\\scipy-1.16.0-cp311-cp311-win_amd64.whl'\n", "Consider using the `--user` option or check the permissions.\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Collecting threadpoolctl>=3.1.0 (from scikit-learn)\n", "  Using cached threadpoolctl-3.6.0-py3-none-any.whl.metadata (13 kB)\n", "Collecting contourpy>=1.0.1 (from matplotlib)\n", "  Downloading contourpy-1.3.2-cp311-cp311-win_amd64.whl.metadata (5.5 kB)\n", "Collecting cycler>=0.10 (from matplotlib)\n", "  Downloading cycler-0.12.1-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting fonttools>=4.22.0 (from matplotlib)\n", "  Downloading fonttools-4.58.5-cp311-cp311-win_amd64.whl.metadata (109 kB)\n", "Collecting kiwisolver>=1.3.1 (from matplotlib)\n", "  Using cached kiwisolver-1.4.8-cp311-cp311-win_amd64.whl.metadata (6.3 kB)\n", "Requirement already satisfied: packaging>=20.0 in c:\\anaconda\\envs\\zjou2025\\lib\\site-packages (from matplotlib) (25.0)\n", "Collecting pillow>=8 (from matplotlib)\n", "  Using cached pillow-11.3.0-cp311-cp311-win_amd64.whl.metadata (9.2 kB)\n", "Collecting pyparsing>=2.3.1 (from matplotlib)\n", "  Using cached pyparsing-3.2.3-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: python-dateutil>=2.7 in c:\\anaconda\\envs\\zjou2025\\lib\\site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: six>=1.5 in c:\\anaconda\\envs\\zjou2025\\lib\\site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n", "Using cached scikit_learn-1.7.0-cp311-cp311-win_amd64.whl (10.7 MB)\n", "Downloading matplotlib-3.10.3-cp311-cp311-win_amd64.whl (8.1 MB)\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.1 MB ? eta -:--:--\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   -- ------------------------------------- 0.5/8.1 MB 51.6 kB/s eta 0:02:27\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   --- ------------------------------------ 0.8/8.1 MB 50.8 kB/s eta 0:02:24\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ----- ---------------------------------- 1.0/8.1 MB 53.3 kB/s eta 0:02:12\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------ --------------------------------- 1.3/8.1 MB 54.8 kB/s eta 0:02:04\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   ------- -------------------------------- 1.6/8.1 MB 53.5 kB/s eta 0:02:02\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   --------- ------------------------------ 1.8/8.1 MB 54.9 kB/s eta 0:01:54\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ---------- ----------------------------- 2.1/8.1 MB 57.0 kB/s eta 0:01:45\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ----------- ---------------------------- 2.4/8.1 MB 59.5 kB/s eta 0:01:36\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   ------------- -------------------------- 2.6/8.1 MB 59.4 kB/s eta 0:01:32\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   -------------- ------------------------- 2.9/8.1 MB 58.6 kB/s eta 0:01:29\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   --------------- ------------------------ 3.1/8.1 MB 59.1 kB/s eta 0:01:24\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ---------------- ----------------------- 3.4/8.1 MB 54.8 kB/s eta 0:01:25\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------ --------------------- 3.7/8.1 MB 48.2 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   ------------------- -------------------- 3.9/8.1 MB 45.4 kB/s eta 0:01:32\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   -------------------- ------------------- 4.2/8.1 MB 44.9 kB/s eta 0:01:27\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ---------------------- ----------------- 4.5/8.1 MB 48.4 kB/s eta 0:01:15\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ----------------------- ---------------- 4.7/8.1 MB 53.4 kB/s eta 0:01:03\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   ------------------------ --------------- 5.0/8.1 MB 61.1 kB/s eta 0:00:51\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   -------------------------- ------------- 5.2/8.1 MB 62.7 kB/s eta 0:00:46\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   --------------------------- ------------ 5.5/8.1 MB 68.8 kB/s eta 0:00:38\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ---------------------------- ----------- 5.8/8.1 MB 72.9 kB/s eta 0:00:32\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ----------------------------- ---------- 6.0/8.1 MB 74.1 kB/s eta 0:00:28\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   ------------------------------- -------- 6.3/8.1 MB 74.4 kB/s eta 0:00:24\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   -------------------------------- ------- 6.6/8.1 MB 73.5 kB/s eta 0:00:21\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   --------------------------------- ------ 6.8/8.1 MB 62.5 kB/s eta 0:00:20\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ----------------------------------- ---- 7.1/8.1 MB 56.3 kB/s eta 0:00:18\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------ --- 7.3/8.1 MB 54.2 kB/s eta 0:00:14\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ------------------------------------- -- 7.6/8.1 MB 51.6 kB/s eta 0:00:09\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------  7.9/8.1 MB 48.5 kB/s eta 0:00:05\n", "   ---------------------------------------- 8.1/8.1 MB 48.4 kB/s eta 0:00:00\n", "Downloading contourpy-1.3.2-cp311-cp311-win_amd64.whl (222 kB)\n", "Downloading cycler-0.12.1-py3-none-any.whl (8.3 kB)\n", "Downloading fonttools-4.58.5-cp311-cp311-win_amd64.whl (2.2 MB)\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   ---- ----------------------------------- 0.3/2.2 MB ? eta -:--:--\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   --------- ------------------------------ 0.5/2.2 MB 28.8 kB/s eta 0:01:00\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------- -------------------------- 0.8/2.2 MB 34.7 kB/s eta 0:00:43\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ------------------ --------------------- 1.0/2.2 MB 40.9 kB/s eta 0:00:30\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   ----------------------- ---------------- 1.3/2.2 MB 46.0 kB/s eta 0:00:21\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   --------------------------- ------------ 1.6/2.2 MB 49.0 kB/s eta 0:00:14\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   -------------------------------- ------- 1.8/2.2 MB 56.2 kB/s eta 0:00:08\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ------------------------------------- -- 2.1/2.2 MB 59.4 kB/s eta 0:00:03\n", "   ---------------------------------------- 2.2/2.2 MB 57.5 kB/s eta 0:00:00\n", "Using cached joblib-1.5.1-py3-none-any.whl (307 kB)\n", "Downloading kiwisolver-1.4.8-cp311-cp311-win_amd64.whl (71 kB)\n", "Using cached numpy-2.3.1-cp311-cp311-win_amd64.whl (13.0 MB)\n", "Downloading pillow-11.3.0-cp311-cp311-win_amd64.whl (7.0 MB)\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/7.0 MB ? eta -:--:--\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   --- ------------------------------------ 0.5/7.0 MB 50.2 kB/s eta 0:02:09\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ---- ----------------------------------- 0.8/7.0 MB 51.5 kB/s eta 0:02:01\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------ --------------------------------- 1.0/7.0 MB 59.3 kB/s eta 0:01:41\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   ------- -------------------------------- 1.3/7.0 MB 58.9 kB/s eta 0:01:37\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   --------- ------------------------------ 1.6/7.0 MB 61.1 kB/s eta 0:01:29\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ---------- ----------------------------- 1.8/7.0 MB 61.3 kB/s eta 0:01:25\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------ --------------------------- 2.1/7.0 MB 63.3 kB/s eta 0:01:18\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   ------------- -------------------------- 2.4/7.0 MB 65.8 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   --------------- ------------------------ 2.6/7.0 MB 61.7 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ---------------- ----------------------- 2.9/7.0 MB 58.6 kB/s eta 0:01:11\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------ --------------------- 3.1/7.0 MB 61.3 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   ------------------- -------------------- 3.4/7.0 MB 57.5 kB/s eta 0:01:03\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   --------------------- ------------------ 3.7/7.0 MB 50.4 kB/s eta 0:01:06\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ---------------------- ----------------- 3.9/7.0 MB 53.0 kB/s eta 0:00:58\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------ --------------- 4.2/7.0 MB 60.6 kB/s eta 0:00:47\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   ------------------------- -------------- 4.5/7.0 MB 63.9 kB/s eta 0:00:40\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   --------------------------- ------------ 4.7/7.0 MB 68.3 kB/s eta 0:00:34\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ---------------------------- ----------- 5.0/7.0 MB 66.4 kB/s eta 0:00:31\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------ --------- 5.2/7.0 MB 70.7 kB/s eta 0:00:25\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   ------------------------------- -------- 5.5/7.0 MB 72.0 kB/s eta 0:00:21\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   --------------------------------- ------ 5.8/7.0 MB 82.6 kB/s eta 0:00:15\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ---------------------------------- ----- 6.0/7.0 MB 80.3 kB/s eta 0:00:12\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------ --- 6.3/7.0 MB 79.4 kB/s eta 0:00:09\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ------------------------------------- -- 6.6/7.0 MB 76.7 kB/s eta 0:00:06\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------  6.8/7.0 MB 74.4 kB/s eta 0:00:03\n", "   ---------------------------------------- 7.0/7.0 MB 72.8 kB/s eta 0:00:00\n", "Downloading pyparsing-3.2.3-py3-none-any.whl (111 kB)\n", "Downloading scipy-1.16.0-cp311-cp311-win_amd64.whl (38.6 MB)\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/38.6 MB ? eta -:--:--\n", "   ---------------------------------------- 0.1/38.6 MB ? eta -:--:--\n"]}], "source": ["%pip install scikit-learn matplotlib\n", "\n", "from sklearn.feature_selection import mutual_info_classif # type: ignore\n", "\n", "MIC = mutual_info_classif\n", "from sklearn.feature_selection import VarianceThreshold\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.feature_selection import SelectKBest\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.tree import DecisionTreeClassifier\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "df = pd.read_csv(r\"数据预处理数据集\\data1.csv\")\n", "X = df.iloc[:,1:]\n", "y = df.iloc[:,0]\n", "Xtrain1,Xtest1,Ytrain1,Ytest1 = train_test_split(X,y,test_size=0.3,random_state=100)\n", "rfc1 = RandomForestClassifier(random_state=100)\n", "rfc1 = rfc1.fit(Xtrain1,Ytrain1)\n", "print(rfc1.score(Xtest1,Ytest1))\n", "print(\"===============================================================================\")\n", "selector = <PERSON><PERSON>ceThreshold(threshold=0)\n", "X0 = selector.fit_transform(X)\n", "Xtrain2,Xtest2,Ytrain2,Ytest2 = train_test_split(X0,y,test_size=0.3,random_state=100)\n", "rfc2 = RandomForestClassifier(random_state=100)\n", "rfc2 = rfc2.fit(Xtrain2,Ytrain2)\n", "print(rfc2.score(Xtest2,Ytest2))\n", "print(\"===============================================================================\")\n", "result = MIC(X0,y)\n", "k = result.shape[0] - sum(result <= 0)\n", "X_best = SelectKBest(MIC, k=k).fit_transform(X0, y)\n", "Xtrain3,Xtest3,Ytrain3,Ytest3 = train_test_split(X_best,y,test_size=0.3,random_state=100)\n", "rfc3 = RandomForestClassifier(random_state=100)\n", "rfc3 = rfc3.fit(Xtrain3,Ytrain3)\n", "print(rfc3.score(Xtest3,Ytest3))"]}], "metadata": {"kernelspec": {"display_name": "zjou2025", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}