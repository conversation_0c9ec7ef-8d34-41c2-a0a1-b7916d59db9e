#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
智能体可视化功能测试脚本
"""

import sys
import os
import time
import json
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.getcwd())

def test_dependencies():
    """测试依赖包"""
    print("🔍 检查依赖包...")
    try:
        import pymysql
        import matplotlib.pyplot as plt
        import seaborn as sns
        import pandas as pd
        import numpy as np
        from openai import OpenAI
        import httpx
        from rich.console import Console
        
        print("✅ 所有依赖包检查通过！")
        return True
    except ImportError as e:
        print(f"❌ 依赖包缺失: {e}")
        return False

def test_visualization_engine():
    """测试可视化引擎"""
    print("\n📊 测试数据可视化引擎...")
    
    try:
        # 模拟数据
        test_data = [
            {"品牌名称": "思腾智电", "订单数量": 4963, "总收入": 9926.00, "市场份额": 13.20},
            {"品牌名称": "怪兽充电", "订单数量": 5100, "总收入": 10200.00, "市场份额": 13.60},
            {"品牌名称": "搜电", "订单数量": 5076, "总收入": 15228.00, "市场份额": 20.30},
            {"品牌名称": "街电", "订单数量": 4800, "总收入": 14400.00, "市场份额": 19.20},
            {"品牌名称": "小电", "订单数量": 4200, "总收入": 12600.00, "市场份额": 16.80},
            {"品牌名称": "来电", "订单数量": 3800, "总收入": 11400.00, "市场份额": 15.20}
        ]
        
        # 创建可视化引擎（简化版，不依赖AI客户端）
        from test_viz_engine import SimpleVisualizationEngine
        viz_engine = SimpleVisualizationEngine()
        
        # 测试不同图表类型
        test_cases = [
            ("柱状图", "各品牌订单数量对比"),
            ("饼图", "各品牌市场份额分布"),
            ("折线图", "品牌收入趋势")
        ]
        
        results = []
        for chart_type, title in test_cases:
            print(f"  🎨 生成{chart_type}: {title}")
            result = viz_engine.create_chart(test_data, chart_type, title)
            if result["success"]:
                print(f"    ✅ {chart_type}生成成功: {result['path']}")
                results.append(result)
            else:
                print(f"    ❌ {chart_type}生成失败: {result['message']}")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ 可视化引擎测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🗄️ 测试数据库连接...")
    
    try:
        import pymysql
        
        connection = pymysql.connect(
            host='localhost',
            user='root',
            passwd='',
            db='ads',
            charset='utf8'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT DATABASE()")
            result = cursor.fetchone()
            print(f"✅ 数据库连接成功: {result[0]}")
            
            # 测试查询
            cursor.execute("SELECT COUNT(*) FROM order_table")
            count = cursor.fetchone()[0]
            print(f"✅ 订单表记录数: {count}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("💡 请确保MySQL服务已启动，且ads数据库存在")
        return False

def create_test_visualization():
    """创建测试可视化"""
    print("\n🎨 创建测试可视化图表...")
    
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        import pandas as pd
        import numpy as np
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建测试数据
        data = {
            '品牌': ['思腾智电', '怪兽充电', '搜电', '街电', '小电', '来电'],
            '市场份额': [13.2, 13.6, 20.3, 19.2, 16.8, 15.2],
            '订单数量': [4963, 5100, 5076, 4800, 4200, 3800]
        }
        df = pd.DataFrame(data)
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 柱状图
        bars = ax1.bar(df['品牌'], df['订单数量'], color=plt.cm.Set3(np.linspace(0, 1, len(df))))
        ax1.set_title('各品牌订单数量对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('品牌')
        ax1.set_ylabel('订单数量')
        ax1.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}', ha='center', va='bottom')
        
        # 饼图
        colors = plt.cm.Set3(np.linspace(0, 1, len(df)))
        wedges, texts, autotexts = ax2.pie(df['市场份额'], labels=df['品牌'], 
                                          autopct='%1.1f%%', colors=colors, startangle=90)
        ax2.set_title('各品牌市场份额分布', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        
        # 确保目录存在
        os.makedirs('powerbank_memory', exist_ok=True)
        
        # 保存图表
        chart_path = f'powerbank_memory/test_chart_{int(time.time())}.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"✅ 测试图表生成成功: {chart_path}")
        return chart_path
        
    except Exception as e:
        print(f"❌ 测试图表生成失败: {e}")
        return None

def main():
    """主测试函数"""
    print("🚀 智能体可视化功能测试开始")
    print("=" * 60)
    
    # 测试结果
    results = {
        "dependencies": False,
        "database": False,
        "visualization": False,
        "test_chart": False
    }
    
    # 1. 测试依赖包
    results["dependencies"] = test_dependencies()
    
    # 2. 测试数据库连接
    results["database"] = test_database_connection()
    
    # 3. 创建测试图表
    chart_path = create_test_visualization()
    results["test_chart"] = chart_path is not None
    
    # 4. 测试可视化引擎（如果可能）
    try:
        results["visualization"] = test_visualization_engine()
    except:
        print("⚠️ 完整可视化引擎测试跳过（需要完整系统）")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        test_names = {
            "dependencies": "依赖包检查",
            "database": "数据库连接", 
            "visualization": "可视化引擎",
            "test_chart": "测试图表生成"
        }
        print(f"{test_names[test_name]}: {status}")
    
    # 总体评估
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests >= 2:
        print("🎉 基础功能正常，可以进行进一步测试！")
        if chart_path:
            print(f"📁 测试图表已保存: {chart_path}")
            print("💡 可以打开图片文件查看可视化效果")
    else:
        print("⚠️ 部分功能异常，请检查环境配置")
    
    return results

if __name__ == "__main__":
    main()
