# 共享充电宝数据仓库项目需求文档

## 1. 项目背景

### 1.1 业务背景
共享充电宝作为一种便捷的移动充电解决方案，在智能手机普及的时代满足了人们外出时的应急充电需求。当前市场特点：
- 众多品牌参与竞争
- 各区域投放与使用情况差异显著
- 缺乏统一的数据分析平台

### 1.2 项目目标
构建共享充电宝数据仓库，整合多维度数据，为企业决策提供支持：
- **数据整合**：用户信息、使用时间、地点、品牌等多维度数据
- **业务分析**：各区域市场需求、用户使用习惯、品牌地区表现
- **决策支持**：优化投放策略、调整价格体系、提升用户体验、增强市场竞争力

## 2. 开发需求

### 2.1 环境搭建
**要求**：选择以下方案之一，并验证系统可用性

#### 方案一：Hadoop完全分布式
- 搭建Hadoop完全分布式集群
- Hive组件部署在NameNode主节点
- 验证Hadoop和Hive环境可用性

#### 方案二：阿里云平台
- 使用阿里云大数据平台搭建开发环境
- 验证云平台系统可用性
- 确保各组件正常运行

### 2.2 数据采集与数据集成

#### 2.2.1 数据导入MySQL
- **数据源**：提供的CSV数据集（用户、地区、时间、订单）
- **导入方式**：不限（Python、Java等程序）
- **目标**：建立MySQL数据库作为数据暂存层

#### 2.2.2 数据抽取到大数据平台
- **源系统**：MySQL数据库
- **目标系统**：Hive数据仓库
- **数据范围**：共享充电宝相关原始业务数据
- **用途**：为后续数据预处理提供数据基础

#### 2.2.3 数据扩展（可选）
- 根据业务场景自行收集或模拟数据
- 丰富数据仓库项目的维度与规模
- 增强分析的全面性

### 2.3 数据清洗与预处理

#### 2.3.1 数据质量处理
- **去重**：去除重复数据记录
- **格式纠正**：纠正错误的数据格式
- **缺失值处理**：处理空值和异常值
- **数据验证**：确保数据准确性和完整性

#### 2.3.2 数据标准化
- 统一数据格式和编码
- 建立数据质量标准
- 生成高质量数据集

### 2.4 数据建模与数据分析

#### 2.4.1 数据仓库分层架构
**必须包含以下四层**：
- **ODS层**（操作数据存储层）：原始数据存储
- **DWD层**（数据仓库明细层）：清洗后的明细数据
- **DWS层**（数据仓库汇总层）：按维度汇总的数据
- **ADS层**（应用数据服务层）：面向应用的数据集市

#### 2.4.2 性能要求
- 确保数据查询的高效性
- 保证数据模型的可用性
- 支持复杂的多维分析

#### 2.4.3 业务分析需求
**核心分析指标**：
- 按区域统计充电宝使用时长
- 按品牌分析收入情况
- 用户行为模式分析
- 时间维度使用趋势
- 地域分布热力分析

### 2.5 数据可视化

#### 2.5.1 可视化要求
- 根据数据分析结果选择合适的图表类型
- 与ADS层数据分析结果匹配
- 采用前后端分离架构
- 现代化UI设计和用户体验

#### 2.5.2 技术实现
**后端API服务**：
- **框架**：Python Flask
- **功能**：数据分析、API接口、图表数据处理
- **数据源**：读取Hive分析结果CSV文件
- **接口**：RESTful API，支持跨域访问

**前端可视化界面**：
- **框架**：Vue3 (CDN方式)
- **图表库**：ECharts 5.x
- **UI组件**：Element Plus
- **设计风格**：现代化渐变、毛玻璃效果、响应式布局

#### 2.5.3 展示内容
- **KPI仪表板**：总订单数、总收入、用户数、平均使用次数
- **品牌分析**：市场份额饼图、收入对比分析
- **时间趋势**：数据记录趋势面积图
- **区域分析**：省份收入排行榜、热力图展示
- **用户行为**：用户分层环形图、行为统计

#### 2.5.4 系统特色
- **实时数据**：基于Hive分析结果的动态展示
- **交互体验**：图表悬停、点击交互效果
- **响应式设计**：适配桌面和移动设备
- **一键启动**：集成启动脚本，快速部署

## 3. 项目交付物

### 3.1 技术交付物
- [x] 环境搭建文档和验证报告
- [x] 数据导入脚本和说明文档
- [x] ETL流程设计和实现代码
- [x] 数据仓库表结构设计文档
- [x] 数据质量报告
- [x] 可视化看板和分析报告
- [x] 前后端分离可视化系统
- [x] API接口文档和使用说明
- [x] 一键启动部署脚本

### 3.2 业务交付物
- [ ] 业务需求分析报告
- [ ] 数据字典和元数据管理
- [ ] 关键业务指标定义
- [ ] 用户使用手册
- [ ] 项目总结报告

## 4. 技术栈建议

### 4.1 基础环境
- **操作系统**：Linux（CentOS/Ubuntu）
- **大数据平台**：Hadoop + Hive
- **数据库**：MySQL
- **编程语言**：Python/Java

### 4.2 开发工具
- **数据处理**：Hive、Mysql、Python
- **可视化**：Tableau、Power BI、Superset、ECharts
- **调度工具**：Airflow、Oozie
- **版本控制**：Git