<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享充电宝数据仓库 - 可视化大屏</title>
    
    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- ECharts CDN -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 中国地图数据 -->
    <script src="https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json"></script>
    
    <!-- Element Plus CDN -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Axios CDN -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="./styles/main.css">
</head>
<body>
    <div id="app">
        <!-- 页面头部 -->
        <header class="dashboard-header">
            <div class="header-content">
                <h1 class="title">
                    <i class="icon">⚡</i>
                    共享充电宝数据仓库可视化大屏
                </h1>
                <div class="header-info">
                    <span class="time">{{ currentTime }}</span>
                    <span class="status" :class="{ 'online': isOnline, 'offline': !isOnline }">
                        {{ isOnline ? '数据实时' : '连接断开' }}
                    </span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="dashboard-main">
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载数据...</p>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="error" class="error-container">
                <div class="error-icon">❌</div>
                <h3>数据加载失败</h3>
                <p>{{ error }}</p>
                <button @click="loadAllData" class="retry-btn">重试</button>
            </div>

            <!-- 数据展示区域 -->
            <div v-else class="charts-container">
                <!-- 第一行：品牌收入分析 -->
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="card-header">
                            <h3>品牌市场份额与收入分析</h3>
                            <div class="card-stats">
                                <span>总订单: {{ brandData.total?.totalOrders || 0 }}</span>
                                <span>总收入: ¥{{ brandData.total?.totalRevenue || 0 }}</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <div id="brandChart" class="chart"></div>
                        </div>
                    </div>
                </div>

                <!-- 第二行：用户行为分析 -->
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="card-header">
                            <h3>用户行为分析</h3>
                            <div class="card-stats">
                                <span>总用户: {{ userBehaviorData.statistics?.totalUsers || 0 }}</span>
                                <span>平均消费: ¥{{ (userBehaviorData.statistics?.avgConsumption || 0).toFixed(2) }}</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <div id="userBehaviorChart" class="chart"></div>
                        </div>
                    </div>
                </div>

                <!-- 第三行：时间趋势分析 -->
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="card-header">
                            <h3>数据记录时间趋势</h3>
                            <div class="card-stats">
                                <span>覆盖年份: {{ timeData.total?.totalYears || 0 }}</span>
                                <span>总记录: {{ timeData.total?.totalRecords || 0 }}</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <div id="timeChart" class="chart"></div>
                        </div>
                    </div>
                </div>

                <!-- 第四行：地区使用分析 -->
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="card-header">
                            <h3>地区收入排行榜</h3>
                            <div class="card-stats">
                                <span>覆盖地区: {{ regionUsageData.total?.totalRegions || 0 }}</span>
                                <span>平均收入: ¥{{ (regionUsageData.total?.avgRevenue || 0).toFixed(2) }}</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <div id="regionUsageChart" class="chart"></div>
                        </div>
                    </div>
                </div>

                <!-- 第五行：地区热力图 -->
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="card-header">
                            <h3>省份收入热力分布</h3>
                            <div class="card-stats">
                                <span>覆盖省份: {{ regionHeatmapData.total?.totalProvinces || 0 }}</span>
                                <span>总收入: ¥{{ regionHeatmapData.total?.totalRevenue || 0 }}</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <div id="regionHeatmapChart" class="chart"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 页面底部 -->
        <footer class="dashboard-footer">
            <p>&copy; 2025 共享充电宝数据仓库项目 | 最后更新: {{ lastUpdateTime }}</p>
        </footer>
    </div>

    <!-- Vue应用脚本 -->
    <script src="./js/app.js"></script>
</body>
</html>
