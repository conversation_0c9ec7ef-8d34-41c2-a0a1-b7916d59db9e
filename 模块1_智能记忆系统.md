# 🧠 模块1: 智能记忆系统 - 完全重构版

## 📋 模块概述

`PowerBankIntelligentMemory` 是一个专为共享充电宝业务设计的智能记忆系统，支持基于Token和数量的双模式记忆管理，具备业务上下文感知和智能清理功能。

## 🏗️ 核心架构

### 初始化配置
- **Token模式**: 基于DeepSeek模型的Token计算
- **数量模式**: 基于消息条数的简单计数
- **业务感知**: 自动识别共享充电宝业务场景

## 🔧 核心方法详解

### 1. 初始化方法

#### `__init__(count_threshold, token_threshold, use_token_mode, model)`
**功能**: 初始化智能记忆系统
**参数**:
- `count_threshold`: 消息数量阈值 (默认25)
- `token_threshold`: Token数量阈值 (默认4000)
- `use_token_mode`: 是否使用Token模式 (默认True)
- `model`: 使用的模型 (默认"deepseek-chat")

**核心流程**:
```python
# 1. 配置基础参数
# 2. 初始化业务上下文
# 3. 设置业务场景映射
# 4. 初始化Token计算器
```

### 2. 消息管理方法

#### `append_message(message: Dict) -> bool`
**功能**: 添加消息到记忆系统
**特色功能**:
- 自动Token计算和元数据添加
- 智能清理触发检查
- 业务上下文更新
- 异常处理和日志记录

**处理流程**:
```python
# 1. 验证消息格式
# 2. 计算Token数量
# 3. 添加时间戳和业务上下文
# 4. 检查是否需要清理
# 5. 更新统计信息
```

#### `get_messages() -> List[Dict]`
**功能**: 获取当前记忆中的所有消息
**返回**: 包含完整元数据的消息列表

### 3. 智能清理系统

#### `_smart_cleanup()`
**功能**: 智能清理策略
**清理规则**:
- 保留所有系统消息
- 保留最近5条重要消息
- 优先保留业务相关消息
- 重新计算Token和数量统计

#### `_should_cleanup(tokens: int) -> bool`
**功能**: 判断是否需要清理
**判断逻辑**:
- Token模式: 当前Token + 新Token > 阈值
- 数量模式: 当前消息数 + 1 > 阈值

### 4. 业务智能分析

#### `_detect_business_context(content: str) -> Dict`
**功能**: 检测业务上下文
**识别场景**:
- 用户分析: 用户行为、年龄分布等
- 品牌分析: 市场份额、竞争分析等
- 地区分析: 区域分布、热力图等
- 时间分析: 趋势分析、周期性等

#### `_update_business_context(message: Dict)`
**功能**: 更新业务上下文
**更新内容**:
- 当前分析类型
- 关注的数据表
- 关键指标
- 最新结果

### 5. Token计算系统

#### `_init_token_calculator()`
**功能**: 初始化Token计算器
**支持模型**: DeepSeek系列模型

#### `token_calculator(text: str) -> int`
**功能**: 计算文本Token数量
**特点**: 支持中英文混合文本的精确计算

### 6. 状态管理方法

#### `get_business_summary() -> Dict`
**功能**: 获取业务摘要
**返回信息**:
- 当前分析场景
- 关注的数据表
- 查询统计
- 资源使用情况

#### `clear_memory()`
**功能**: 清空所有记忆
**清理范围**:
- 消息列表
- 查询历史
- 统计计数器

## 🎯 业务场景映射

| 分析类型 | 相关数据表 | 应用场景 |
|---------|-----------|---------|
| 用户分析 | user_table, user_behavior_summary | 用户画像、行为分析 |
| 品牌分析 | brand_revenue_summary, order_table | 市场份额、竞争分析 |
| 地区分析 | region_table, region_heatmap_data | 区域运营、热力图 |
| 时间分析 | time_table, time_summary | 趋势分析、周期性 |
| 综合分析 | 全部表 | 多维度综合分析 |

## 🚀 使用示例

```python
# 初始化记忆系统
memory = PowerBankIntelligentMemory(
    count_threshold=25,
    token_threshold=4000,
    use_token_mode=True,
    model="deepseek-chat"
)

# 添加消息
memory.append_message({
    "role": "user",
    "content": "分析各品牌的市场份额"
})

# 获取业务摘要
summary = memory.get_business_summary()
print(f"当前场景: {summary['current_scenario']}")

# 获取所有消息
messages = memory.get_messages()
```

## 📊 技术特点

- **智能清理**: 基于业务重要性的智能消息清理
- **双模式**: Token和数量双模式支持
- **业务感知**: 自动识别共享充电宝业务场景
- **元数据丰富**: 时间戳、Token数、业务上下文等
- **异常处理**: 完善的错误处理和日志记录

---

*本文档基于PowerBankIntelligentMemory v3.0版本*